syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gen/api/v1";

service ActivityService {
  // ListActivities returns a list of activities.
  rpc ListActivities(ListActivitiesRequest) returns (ListActivitiesResponse) {
    option (google.api.http) = {get: "/api/v1/activities"};
  }

  // GetActivity returns the activity with the given id.
  rpc GetActivity(GetActivityRequest) returns (Activity) {
    option (google.api.http) = {get: "/api/v1/{name=activities/*}"};
    option (google.api.method_signature) = "name";
  }
}

message Activity {
  option (google.api.resource) = {
    type: "memos.api.v1/Activity"
    pattern: "activities/{activity}"
    name_field: "name"
    singular: "activity"
    plural: "activities"
  };

  // The name of the activity.
  // Format: activities/{id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // The name of the creator.
  // Format: users/{user}
  string creator = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The type of the activity.
  Type type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The level of the activity.
  Level level = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The create time of the activity.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The payload of the activity.
  ActivityPayload payload = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Activity types.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;
    // Memo comment activity.
    MEMO_COMMENT = 1;
    // Version update activity.
    VERSION_UPDATE = 2;
  }

  // Activity levels.
  enum Level {
    // Unspecified level.
    LEVEL_UNSPECIFIED = 0;
    // Info level.
    INFO = 1;
    // Warn level.
    WARN = 2;
    // Error level.
    ERROR = 3;
  }
}

message ActivityPayload {
  oneof payload {
    // Memo comment activity payload.
    ActivityMemoCommentPayload memo_comment = 1;
  }
}

// ActivityMemoCommentPayload represents the payload of a memo comment activity.
message ActivityMemoCommentPayload {
  // The memo name of comment.
  // Format: memos/{memo}
  string memo = 1;
  // The name of related memo.
  // Format: memos/{memo}
  string related_memo = 2;
}

message ListActivitiesRequest {
  // The maximum number of activities to return.
  // The service may return fewer than this value.
  // If unspecified, at most 100 activities will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // A page token, received from a previous `ListActivities` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 2;
}

message ListActivitiesResponse {
  // The activities.
  repeated Activity activities = 1;

  // A token to retrieve the next page of results.
  // Pass this value in the page_token field in the subsequent call to `ListActivities`
  // method to retrieve the next page of results.
  string next_page_token = 2;
}

message GetActivityRequest {
  // The name of the activity.
  // Format: activities/{id}, id is the system generated auto-incremented id.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/Activity"}
  ];
}
