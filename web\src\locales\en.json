{"about": {"blogs": "Blogs", "description": "A privacy-first, lightweight note-taking service. Easily capture and share your great thoughts.", "documents": "Documents", "github-repository": "GitHub Repo", "official-website": "Official Website"}, "auth": {"create-your-account": "Create your account", "host-tip": "You are registering as the Site Host.", "new-password": "New password", "repeat-new-password": "Repeat the new password", "sign-in-tip": "Already have an account?", "sign-up-tip": "Don't have an account yet?"}, "common": {"about": "About", "add": "Add", "admin": "Admin", "archive": "Archive", "archived": "Archived", "attachments": "Attachments", "avatar": "Avatar", "basic": "Basic", "beta": "Beta", "cancel": "Cancel", "change": "Change", "clear": "Clear", "close": "Close", "collapse": "Collapse", "confirm": "Confirm", "create": "Create", "created-at": "Created At", "database": "Database", "day": "Day", "days": "Days", "delete": "Delete", "description": "Description", "edit": "Edit", "email": "Email", "expand": "Expand", "explore": "Explore", "file": "File", "filter": "Filter", "home": "Home", "image": "Image", "in": "In", "inbox": "Inbox", "input": "Input", "language": "Language", "last-updated-at": "Last updated at", "layout": "Layout", "learn-more": "Learn more", "link": "Link", "mark": "<PERSON>", "memo": "Memo", "memos": "Memos", "name": "Name", "new": "New", "nickname": "Nickname", "null": "<PERSON><PERSON>", "or": "or", "password": "Password", "pin": "<PERSON>n", "pinned": "Pinned", "preview": "Preview", "profile": "Profile", "properties": "Properties", "referenced-by": "Referenced by", "referencing": "Referencing", "relations": "Relations", "remember-me": "Remember me", "rename": "<PERSON><PERSON>", "reset": "Reset", "resources": "Resources", "restore": "Rest<PERSON>", "role": "Role", "save": "Save", "search": "Search", "select": "Select", "settings": "Settings", "share": "Share", "shortcut-filter": "Shortcut filter", "shortcuts": "Shortcuts", "sign-in": "Sign in", "sign-in-with": "Sign in with {{provider}}", "sign-out": "Sign out", "sign-up": "Sign up", "statistics": "Statistics", "tags": "Tags", "title": "Title", "tree-mode": "Tree mode", "type": "Type", "unpin": "Unpin", "update": "Update", "upload": "Upload", "user": "User", "username": "Username", "version": "Version", "visibility": "Visibility", "yourself": "Yourself"}, "days": {"fri": "<PERSON><PERSON>", "mon": "Mon", "sat": "Sat", "sun": "Sun", "thu": "<PERSON>hu", "tue": "<PERSON><PERSON>", "wed": "Wed"}, "editor": {"add-your-comment-here": "Add your comment here...", "any-thoughts": "Any thoughts...", "save": "Save", "no-changes-detected": "No changes detected"}, "filters": {"has-code": "hasCode", "has-link": "hasLink", "has-task-list": "hasTaskList"}, "inbox": {"memo-comment": "{{user}} has a comment on your {{memo}}.", "version-update": "New version {{version}} is available now!"}, "markdown": {"checkbox": "Checkbox", "code-block": "Code block", "content-syntax": "Content syntax"}, "memo": {"archived-at": "Archived at", "click-to-hide-nsfw-content": "Click to hide NSFW content", "click-to-show-nsfw-content": "Click to show NSFW content", "code": "Code", "comment": {"self": "Comments", "write-a-comment": "Write a comment"}, "copy-link": "Copy Link", "count-memos-in-date": "{{count}} {{memos}} in {{date}}", "delete-confirm": "Are you sure you want to delete this memo? THIS ACTION IS IRREVERSIBLE", "direction": "Direction", "direction-asc": "Ascending", "direction-desc": "Descending", "display-time": "Display Time", "filters": "Filters", "links": "Links", "load-more": "Load more", "no-archived-memos": "No archived memos.", "no-memos": "No memos.", "order-by": "Order By", "remove-completed-task-list-items": "Remove done", "remove-completed-task-list-items-confirm": "Are you sure you want to remove all completed to-dos? THIS ACTION IS IRREVERSIBLE", "search-placeholder": "Search memos...", "show-less": "Show less", "show-more": "Show more", "to-do": "To-do", "view-detail": "View Detail", "visibility": {"disabled": "Public memos are disabled", "private": "Private", "protected": "Workspace", "public": "Public"}, "list": "List", "masonry": "Masonry"}, "message": {"archived-successfully": "Archived successfully", "change-memo-created-time": "Change memo created time", "copied": "<PERSON>pied", "deleted-successfully": "Deleted successfully", "description-is-required": "Description is required", "failed-to-embed-memo": "Failed to embed memo", "fill-all": "Please fill in all fields.", "fill-all-required-fields": "Please fill all required fields", "maximum-upload-size-is": "Maximum allowed upload size is {{size}} MiB", "memo-not-found": "Memo not found.", "new-password-not-match": "New passwords do not match.", "no-data": "No data found.", "password-changed": "Password Changed", "password-not-match": "Passwords do not match.", "remove-completed-task-list-items-successfully": "The removal was successful", "restored-successfully": "Restored successfully", "succeed-copy-link": "Link copied successfully.", "update-succeed": "Update succeeded", "user-not-found": "User not found"}, "reference": {"add-references": "Add references", "embedded-usage": "Use as Embedded Content", "no-memos-found": "No memos found", "search-placeholder": "Search content"}, "resource": {"clear": "Clear", "copy-link": "Copy Link", "create-dialog": {"external-link": {"file-name": "File name", "file-name-placeholder": "File name", "link": "Link", "link-placeholder": "https://the.link.to/your/resource", "option": "External link", "type": "Type", "type-placeholder": "File type"}, "local-file": {"choose": "Choose a file…", "option": "Local file"}, "title": "Create Resource", "upload-method": "Upload method"}, "delete-resource": "Delete Resource", "delete-selected-resources": "Delete Selected Resources", "fetching-data": "Fetching data…", "file-drag-drop-prompt": "Drag and drop your file here to upload file", "linked-amount": "Linked amount", "no-files-selected": "No files selected", "no-resources": "No resources.", "no-unused-resources": "No unused resources", "reset-link": "Reset Link", "reset-link-prompt": "Are you sure to reset the link? This will break all current link usages. THIS ACTION IS IRREVERSIBLE", "reset-resource-link": "Reset Resource Link", "unused-resources": "Unused resources"}, "router": {"back-to-top": "Back to Top", "go-to-home": "Go to Home"}, "setting": {"access-token-section": {"access-token-copied-to-clipboard": "Access token copied to clipboard", "access-token-deletion": "Are you sure to delete access token {{accessToken}}? THIS ACTION IS IRREVERSIBLE.", "create-dialog": {"create-access-token": "Create Access Token", "created-at": "Created At", "description": "Description", "duration-1m": "1 Month", "duration-8h": "8 Hours", "duration-never": "Never", "expiration": "Expiration", "expires-at": "Expires At", "some-description": "Some description..."}, "description": "A list of all access tokens for your account.", "title": "Access Tokens", "token": "Token"}, "user-sessions-section": {"title": "Active Sessions", "description": "A list of all active sessions for your account. Sessions automatically expire 2 weeks after the last activity. You can revoke any session except the current one.", "device": "<PERSON><PERSON>", "location": "Location", "last-active": "Last Active", "expires": "Expires", "current": "Current", "never": "Never", "session-revocation": "Are you sure to revoke session {{sessionId}}? You will need to sign in again on that device.", "session-revoked": "Session revoked successfully", "revoke-session": "Revoke session", "cannot-revoke-current": "Cannot revoke current session", "no-sessions": "No active sessions found"}, "account-section": {"change-password": "Change password", "email-note": "Optional", "export-memos": "Export Memos", "nickname-note": "Displayed in the banner", "openapi-reset": "Reset OpenAPI Key", "openapi-sample-post": "Hello #memos from {{url}}", "openapi-title": "OpenAPI", "reset-api": "Reset API", "title": "Account Information", "update-information": "Update Information", "username-note": "Used to sign in"}, "appearance-option": {"dark": "Always dark", "light": "Always light", "system": "Follow system"}, "member": "Member", "member-list": "Member list", "member-section": {"admin": "Admin", "archive-member": "Archive member", "archive-warning": "Are you sure to archive {{username}}?", "create-a-member": "Create a member", "delete-member": "Delete Member", "delete-warning": "Are you sure to delete {{username}}? THIS ACTION IS IRREVERSIBLE", "user": "User"}, "memo-related": "Memo", "memo-related-settings": {"content-lenght-limit": "Content length limit (Byte)", "enable-blur-nsfw-content": "Enable sensitive content (NSFW) blurring", "enable-link-preview": "Enable link preview", "enable-memo-comments": "Enable memo comments", "enable-memo-location": "Enable memo location", "reactions": "Reactions", "title": "Memo related settings"}, "my-account": "My Account", "preference": "Preferences", "preference-section": {"default-memo-sort-option": "Memo display time", "default-memo-visibility": "Default memo visibility", "theme": "Theme"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Authorization endpoint", "client-id": "Client ID", "client-secret": "Client secret", "confirm-delete": "Are you sure to delete \"{{name}}\" SSO configuration? THIS ACTION IS IRREVERSIBLE", "create-sso": "Create SSO", "custom": "Custom", "delete-sso": "Confirm delete", "disabled-password-login-warning": "Password-login is disabled, be extra careful when removing identity providers", "display-name": "Display Name", "identifier": "Identifier", "identifier-filter": "Identifier Filter", "no-sso-found": "No SSO found.", "redirect-url": "Redirect URL", "scopes": "<PERSON><PERSON><PERSON>", "single-sign-on": "Configuring Single Sign-On (SSO) for Authentication", "sso-created": "SSO {{name}} created", "sso-list": "SSO List", "sso-updated": "SSO {{name}} updated", "template": "Template", "token-endpoint": "Token endpoint", "update-sso": "Update SSO", "user-endpoint": "User endpoint"}, "storage": "Storage", "storage-section": {"accesskey": "Access key", "accesskey-placeholder": "Access key / Access ID", "bucket": "Bucket", "bucket-placeholder": "Bucket name", "create-a-service": "Create a service", "create-storage": "Create Storage", "current-storage": "Current object storage", "delete-storage": "Delete Storage", "endpoint": "Endpoint", "filepath-template": "Filepath template", "local-storage-path": "Local storage path", "path": "Storage Path", "path-description": "You can use the same dynamic variables from local storage, like {filename}", "path-placeholder": "custom/path", "presign-placeholder": "Pre-sign URL, optional", "region": "Region", "region-placeholder": "Region name", "s3-compatible-url": "S3 Compatible URL", "secretkey": "Secret key", "secretkey-placeholder": "Secret key / Access Key", "storage-services": "Storage services", "type-database": "Database", "type-local": "Local file system", "update-a-service": "Update a service", "update-local-path": "Update Local Storage Path", "update-local-path-description": "Local storage path is a relative path to your database file", "update-storage": "Update Storage", "url-prefix": "URL prefix", "url-prefix-placeholder": "Custom URL prefix, optional", "url-suffix": "URL suffix", "url-suffix-placeholder": "Custom URL suffix, optional", "warning-text": "Are you sure to delete storage service \"{{name}}\"? THIS ACTION IS IRREVERSIBLE"}, "system": "System", "system-section": {"additional-script": "Additional script", "additional-script-placeholder": "Additional JavaScript code", "additional-style": "Additional style", "additional-style-placeholder": "Additional CSS code", "allow-user-signup": "Allow user signup", "customize-server": {"appearance": "Server Appearance", "description": "Description", "icon-url": "Icon URL", "locale": "Server Locale", "title": "Customize Server"}, "disable-markdown-shortcuts-in-editor": "Disable Markdown shortcuts in editor", "disable-password-login": "Disable password login", "disable-password-login-final-warning": "Please type \"CONFIRM\" if you know what you are doing.", "disable-password-login-warning": "This will disable password login for all users. It is not possible to log in without reverting this setting in the database if your configured identity providers fail. You’ll also have to be extra careful when removing an identity provider", "disable-public-memos": "Disable public memos", "display-with-updated-time": "Display with updated time", "enable-auto-compact": "Enable auto compact", "enable-double-click-to-edit": "Enable double click to edit", "enable-password-login": "Enable password login", "enable-password-login-warning": "This will enable password login for all users. Continue only if you want to users to be able to log in using both SSO and password", "max-upload-size": "Maximum upload size (MiB)", "max-upload-size-hint": "Recommended value is 32 MiB.", "removed-completed-task-list-items": "Enable removal of completed task list items", "server-name": "Server Name", "title": "General"}, "version": "Version", "webhook-section": {"create-dialog": {"an-easy-to-remember-name": "An easy-to-remember name", "create-webhook": "Create webhook", "edit-webhook": "Edit webhook", "payload-url": "Payload URL", "title": "Title", "url-example-post-receive": "https://example.com/postreceive"}, "no-webhooks-found": "No webhooks found.", "title": "Webhooks", "url": "URL"}, "workspace-section": {"disallow-change-nickname": "Disallow changing nickname", "disallow-change-username": "Disallow changing username", "disallow-password-auth": "Disallow password auth", "disallow-user-registration": "Disallow user registration", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "week-start-day": "Week start day"}}, "tag": {"all-tags": "All Tags", "create-tag": "Create Tag", "create-tags-guide": "You can create tags by inputting `#tag`.", "delete-confirm": "Are you sure to delete this tag? All related memos will be archived.", "delete-tag": "Delete Tag", "new-name": "New Name", "no-tag-found": "No tag found", "old-name": "Old Name", "rename-error-empty": "Tag name cannot be empty or contain spaces", "rename-error-repeat": "New name cannot be the same as the old name", "rename-success": "Renamed tag successfully", "rename-tag": "<PERSON><PERSON> tag", "rename-tip": "All your memos with this tag will be updated."}}