// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/user_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// User role enumeration.
type User_Role int32

const (
	// Unspecified role.
	User_ROLE_UNSPECIFIED User_Role = 0
	// Host role with full system access.
	User_HOST User_Role = 1
	// Admin role with administrative privileges.
	User_ADMIN User_Role = 2
	// Regular user role.
	User_USER User_Role = 3
)

// Enum value maps for User_Role.
var (
	User_Role_name = map[int32]string{
		0: "ROLE_UNSPECIFIED",
		1: "HOST",
		2: "ADMIN",
		3: "USER",
	}
	User_Role_value = map[string]int32{
		"ROLE_UNSPECIFIED": 0,
		"HOST":             1,
		"ADMIN":            2,
		"USER":             3,
	}
)

func (x User_Role) Enum() *User_Role {
	p := new(User_Role)
	*p = x
	return p
}

func (x User_Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Role) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_user_service_proto_enumTypes[0].Descriptor()
}

func (User_Role) Type() protoreflect.EnumType {
	return &file_api_v1_user_service_proto_enumTypes[0]
}

func (x User_Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use User_Role.Descriptor instead.
func (User_Role) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{0, 0}
}

type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the user.
	// Format: users/{user}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The role of the user.
	Role User_Role `protobuf:"varint,2,opt,name=role,proto3,enum=memos.api.v1.User_Role" json:"role,omitempty"`
	// Required. The unique username for login.
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	// Optional. The email address of the user.
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// Optional. The display name of the user.
	DisplayName string `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// Optional. The avatar URL of the user.
	AvatarUrl string `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// Optional. The description of the user.
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// Input only. The password for the user.
	Password string `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	// The state of the user.
	State State `protobuf:"varint,9,opt,name=state,proto3,enum=memos.api.v1.State" json:"state,omitempty"`
	// Output only. The creation timestamp.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The last update timestamp.
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_api_v1_user_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetRole() User_Role {
	if x != nil {
		return x.Role
	}
	return User_ROLE_UNSPECIFIED
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *User) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *User) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *User) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *User) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type ListUsersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional. The maximum number of users to return.
	// The service may return fewer than this value.
	// If unspecified, at most 50 users will be returned.
	// The maximum value is 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token, received from a previous `ListUsers` call.
	// Provide this to retrieve the subsequent page.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. Filter to apply to the list results.
	// Example: "state=ACTIVE" or "role=USER" or "email:@example.com"
	// Supported operators: =, !=, <, <=, >, >=, :
	// Supported fields: username, email, role, state, create_time, update_time
	Filter string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. The order to sort results by.
	// Example: "create_time desc" or "username asc"
	OrderBy string `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// Optional. If true, show deleted users in the response.
	ShowDeleted   bool `protobuf:"varint,5,opt,name=show_deleted,json=showDeleted,proto3" json:"show_deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersRequest) Reset() {
	*x = ListUsersRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersRequest) ProtoMessage() {}

func (x *ListUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersRequest.ProtoReflect.Descriptor instead.
func (*ListUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUsersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListUsersRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListUsersRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListUsersRequest) GetShowDeleted() bool {
	if x != nil {
		return x.ShowDeleted
	}
	return false
}

type ListUsersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of users.
	Users []*User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of users (may be approximate).
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersResponse) Reset() {
	*x = ListUsersResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersResponse) ProtoMessage() {}

func (x *ListUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersResponse.ProtoReflect.Descriptor instead.
func (*ListUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListUsersResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListUsersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListUsersResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type GetUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the user.
	// Format: users/{user}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. The fields to return in the response.
	// If not specified, all fields are returned.
	ReadMask      *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=read_mask,json=readMask,proto3" json:"read_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserRequest) GetReadMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.ReadMask
	}
	return nil
}

type CreateUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The user to create.
	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// Optional. The user ID to use for this user.
	// If empty, a unique ID will be generated.
	// Must match the pattern [a-z0-9-]+
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Optional. If set, validate the request but don't actually create the user.
	ValidateOnly bool `protobuf:"varint,3,opt,name=validate_only,json=validateOnly,proto3" json:"validate_only,omitempty"`
	// Optional. An idempotency token that can be used to ensure that multiple
	// requests to create a user have the same result.
	RequestId     string `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateUserRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CreateUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateUserRequest) GetValidateOnly() bool {
	if x != nil {
		return x.ValidateOnly
	}
	return false
}

func (x *CreateUserRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type UpdateUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The user to update.
	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// Required. The list of fields to update.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	// Optional. If set to true, allows updating sensitive fields.
	AllowMissing  bool `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3" json:"allow_missing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateUserRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateUserRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateUserRequest) GetAllowMissing() bool {
	if x != nil {
		return x.AllowMissing
	}
	return false
}

type DeleteUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the user to delete.
	// Format: users/{user}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. If set to true, the user will be deleted even if they have associated data.
	Force         bool `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteUserRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type SearchUsersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The search query.
	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// Optional. The maximum number of users to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token for pagination.
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersRequest) Reset() {
	*x = SearchUsersRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersRequest) ProtoMessage() {}

func (x *SearchUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersRequest.ProtoReflect.Descriptor instead.
func (*SearchUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{7}
}

func (x *SearchUsersRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchUsersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type SearchUsersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of users matching the search query.
	Users []*User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// A token for the next page of results.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of matching users.
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersResponse) Reset() {
	*x = SearchUsersResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersResponse) ProtoMessage() {}

func (x *SearchUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersResponse.ProtoReflect.Descriptor instead.
func (*SearchUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{8}
}

func (x *SearchUsersResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *SearchUsersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchUsersResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type GetUserAvatarRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the user.
	// Format: users/{user}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserAvatarRequest) Reset() {
	*x = GetUserAvatarRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAvatarRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAvatarRequest) ProtoMessage() {}

func (x *GetUserAvatarRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAvatarRequest.ProtoReflect.Descriptor instead.
func (*GetUserAvatarRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserAvatarRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// User statistics messages
type UserStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the user whose stats these are.
	// Format: users/{user}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The timestamps when the memos were displayed.
	MemoDisplayTimestamps []*timestamppb.Timestamp `protobuf:"bytes,2,rep,name=memo_display_timestamps,json=memoDisplayTimestamps,proto3" json:"memo_display_timestamps,omitempty"`
	// The stats of memo types.
	MemoTypeStats *UserStats_MemoTypeStats `protobuf:"bytes,3,opt,name=memo_type_stats,json=memoTypeStats,proto3" json:"memo_type_stats,omitempty"`
	// The count of tags.
	TagCount map[string]int32 `protobuf:"bytes,4,rep,name=tag_count,json=tagCount,proto3" json:"tag_count,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// The pinned memos of the user.
	PinnedMemos []string `protobuf:"bytes,5,rep,name=pinned_memos,json=pinnedMemos,proto3" json:"pinned_memos,omitempty"`
	// Total memo count.
	TotalMemoCount int32 `protobuf:"varint,6,opt,name=total_memo_count,json=totalMemoCount,proto3" json:"total_memo_count,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserStats) Reset() {
	*x = UserStats{}
	mi := &file_api_v1_user_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStats) ProtoMessage() {}

func (x *UserStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStats.ProtoReflect.Descriptor instead.
func (*UserStats) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{10}
}

func (x *UserStats) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserStats) GetMemoDisplayTimestamps() []*timestamppb.Timestamp {
	if x != nil {
		return x.MemoDisplayTimestamps
	}
	return nil
}

func (x *UserStats) GetMemoTypeStats() *UserStats_MemoTypeStats {
	if x != nil {
		return x.MemoTypeStats
	}
	return nil
}

func (x *UserStats) GetTagCount() map[string]int32 {
	if x != nil {
		return x.TagCount
	}
	return nil
}

func (x *UserStats) GetPinnedMemos() []string {
	if x != nil {
		return x.PinnedMemos
	}
	return nil
}

func (x *UserStats) GetTotalMemoCount() int32 {
	if x != nil {
		return x.TotalMemoCount
	}
	return 0
}

type GetUserStatsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the user.
	// Format: users/{user}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatsRequest) Reset() {
	*x = GetUserStatsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsRequest) ProtoMessage() {}

func (x *GetUserStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsRequest.ProtoReflect.Descriptor instead.
func (*GetUserStatsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserStatsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// User settings message
type UserSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the user whose setting this is.
	// Format: users/{user}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The preferred locale of the user.
	Locale string `protobuf:"bytes,2,opt,name=locale,proto3" json:"locale,omitempty"`
	// The preferred appearance of the user.
	Appearance string `protobuf:"bytes,3,opt,name=appearance,proto3" json:"appearance,omitempty"`
	// The default visibility of the memo.
	MemoVisibility string `protobuf:"bytes,4,opt,name=memo_visibility,json=memoVisibility,proto3" json:"memo_visibility,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserSetting) Reset() {
	*x = UserSetting{}
	mi := &file_api_v1_user_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSetting) ProtoMessage() {}

func (x *UserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSetting.ProtoReflect.Descriptor instead.
func (*UserSetting) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{12}
}

func (x *UserSetting) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSetting) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *UserSetting) GetAppearance() string {
	if x != nil {
		return x.Appearance
	}
	return ""
}

func (x *UserSetting) GetMemoVisibility() string {
	if x != nil {
		return x.MemoVisibility
	}
	return ""
}

type GetUserSettingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the user.
	// Format: users/{user}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSettingRequest) Reset() {
	*x = GetUserSettingRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSettingRequest) ProtoMessage() {}

func (x *GetUserSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSettingRequest.ProtoReflect.Descriptor instead.
func (*GetUserSettingRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetUserSettingRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UpdateUserSettingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The user setting to update.
	Setting *UserSetting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// Required. The list of fields to update.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserSettingRequest) Reset() {
	*x = UpdateUserSettingRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserSettingRequest) ProtoMessage() {}

func (x *UpdateUserSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserSettingRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateUserSettingRequest) GetSetting() *UserSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateUserSettingRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// User access token message
type UserAccessToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the access token.
	// Format: users/{user}/accessTokens/{access_token}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Output only. The access token value.
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// The description of the access token.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Output only. The issued timestamp.
	IssuedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`
	// Optional. The expiration timestamp.
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccessToken) Reset() {
	*x = UserAccessToken{}
	mi := &file_api_v1_user_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccessToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccessToken) ProtoMessage() {}

func (x *UserAccessToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccessToken.ProtoReflect.Descriptor instead.
func (*UserAccessToken) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{15}
}

func (x *UserAccessToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserAccessToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *UserAccessToken) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserAccessToken) GetIssuedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.IssuedAt
	}
	return nil
}

func (x *UserAccessToken) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type ListUserAccessTokensRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource whose access tokens will be listed.
	// Format: users/{user}
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. The maximum number of access tokens to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token for pagination.
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserAccessTokensRequest) Reset() {
	*x = ListUserAccessTokensRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserAccessTokensRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserAccessTokensRequest) ProtoMessage() {}

func (x *ListUserAccessTokensRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserAccessTokensRequest.ProtoReflect.Descriptor instead.
func (*ListUserAccessTokensRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListUserAccessTokensRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListUserAccessTokensRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUserAccessTokensRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListUserAccessTokensResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of access tokens.
	AccessTokens []*UserAccessToken `protobuf:"bytes,1,rep,name=access_tokens,json=accessTokens,proto3" json:"access_tokens,omitempty"`
	// A token for the next page of results.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of access tokens.
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserAccessTokensResponse) Reset() {
	*x = ListUserAccessTokensResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserAccessTokensResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserAccessTokensResponse) ProtoMessage() {}

func (x *ListUserAccessTokensResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserAccessTokensResponse.ProtoReflect.Descriptor instead.
func (*ListUserAccessTokensResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListUserAccessTokensResponse) GetAccessTokens() []*UserAccessToken {
	if x != nil {
		return x.AccessTokens
	}
	return nil
}

func (x *ListUserAccessTokensResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListUserAccessTokensResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type CreateUserAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource where this access token will be created.
	// Format: users/{user}
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The access token to create.
	AccessToken *UserAccessToken `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// Optional. The access token ID to use.
	AccessTokenId string `protobuf:"bytes,3,opt,name=access_token_id,json=accessTokenId,proto3" json:"access_token_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserAccessTokenRequest) Reset() {
	*x = CreateUserAccessTokenRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserAccessTokenRequest) ProtoMessage() {}

func (x *CreateUserAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*CreateUserAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateUserAccessTokenRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateUserAccessTokenRequest) GetAccessToken() *UserAccessToken {
	if x != nil {
		return x.AccessToken
	}
	return nil
}

func (x *CreateUserAccessTokenRequest) GetAccessTokenId() string {
	if x != nil {
		return x.AccessTokenId
	}
	return ""
}

type DeleteUserAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the access token to delete.
	// Format: users/{user}/accessTokens/{access_token}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserAccessTokenRequest) Reset() {
	*x = DeleteUserAccessTokenRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserAccessTokenRequest) ProtoMessage() {}

func (x *DeleteUserAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteUserAccessTokenRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UserSession struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the session.
	// Format: users/{user}/sessions/{session}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The session ID.
	SessionId string `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	// The timestamp when the session was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the session was last accessed.
	// Used for sliding expiration calculation (last_accessed_time + 2 weeks).
	LastAccessedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_accessed_time,json=lastAccessedTime,proto3" json:"last_accessed_time,omitempty"`
	// Client information associated with this session.
	ClientInfo    *UserSession_ClientInfo `protobuf:"bytes,5,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSession) Reset() {
	*x = UserSession{}
	mi := &file_api_v1_user_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSession) ProtoMessage() {}

func (x *UserSession) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSession.ProtoReflect.Descriptor instead.
func (*UserSession) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{20}
}

func (x *UserSession) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSession) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UserSession) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *UserSession) GetLastAccessedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAccessedTime
	}
	return nil
}

func (x *UserSession) GetClientInfo() *UserSession_ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

type ListUserSessionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the parent.
	// Format: users/{user}
	Parent        string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserSessionsRequest) Reset() {
	*x = ListUserSessionsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserSessionsRequest) ProtoMessage() {}

func (x *ListUserSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserSessionsRequest.ProtoReflect.Descriptor instead.
func (*ListUserSessionsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListUserSessionsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

type ListUserSessionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of user sessions.
	Sessions      []*UserSession `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserSessionsResponse) Reset() {
	*x = ListUserSessionsResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserSessionsResponse) ProtoMessage() {}

func (x *ListUserSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserSessionsResponse.ProtoReflect.Descriptor instead.
func (*ListUserSessionsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListUserSessionsResponse) GetSessions() []*UserSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type RevokeUserSessionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the session to revoke.
	// Format: users/{user}/sessions/{session}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeUserSessionRequest) Reset() {
	*x = RevokeUserSessionRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeUserSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeUserSessionRequest) ProtoMessage() {}

func (x *RevokeUserSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeUserSessionRequest.ProtoReflect.Descriptor instead.
func (*RevokeUserSessionRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{23}
}

func (x *RevokeUserSessionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListAllUserStatsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional. The maximum number of user stats to return.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token for pagination.
	PageToken     string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllUserStatsRequest) Reset() {
	*x = ListAllUserStatsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllUserStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllUserStatsRequest) ProtoMessage() {}

func (x *ListAllUserStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllUserStatsRequest.ProtoReflect.Descriptor instead.
func (*ListAllUserStatsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListAllUserStatsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAllUserStatsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListAllUserStatsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of user statistics.
	UserStats []*UserStats `protobuf:"bytes,1,rep,name=user_stats,json=userStats,proto3" json:"user_stats,omitempty"`
	// A token for the next page of results.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of user statistics.
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllUserStatsResponse) Reset() {
	*x = ListAllUserStatsResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllUserStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllUserStatsResponse) ProtoMessage() {}

func (x *ListAllUserStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllUserStatsResponse.ProtoReflect.Descriptor instead.
func (*ListAllUserStatsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListAllUserStatsResponse) GetUserStats() []*UserStats {
	if x != nil {
		return x.UserStats
	}
	return nil
}

func (x *ListAllUserStatsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListAllUserStatsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// Memo type statistics.
type UserStats_MemoTypeStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LinkCount     int32                  `protobuf:"varint,1,opt,name=link_count,json=linkCount,proto3" json:"link_count,omitempty"`
	CodeCount     int32                  `protobuf:"varint,2,opt,name=code_count,json=codeCount,proto3" json:"code_count,omitempty"`
	TodoCount     int32                  `protobuf:"varint,3,opt,name=todo_count,json=todoCount,proto3" json:"todo_count,omitempty"`
	UndoCount     int32                  `protobuf:"varint,4,opt,name=undo_count,json=undoCount,proto3" json:"undo_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStats_MemoTypeStats) Reset() {
	*x = UserStats_MemoTypeStats{}
	mi := &file_api_v1_user_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStats_MemoTypeStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStats_MemoTypeStats) ProtoMessage() {}

func (x *UserStats_MemoTypeStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStats_MemoTypeStats.ProtoReflect.Descriptor instead.
func (*UserStats_MemoTypeStats) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{10, 1}
}

func (x *UserStats_MemoTypeStats) GetLinkCount() int32 {
	if x != nil {
		return x.LinkCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetCodeCount() int32 {
	if x != nil {
		return x.CodeCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetTodoCount() int32 {
	if x != nil {
		return x.TodoCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetUndoCount() int32 {
	if x != nil {
		return x.UndoCount
	}
	return 0
}

type UserSession_ClientInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// User agent string of the client.
	UserAgent string `protobuf:"bytes,1,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// IP address of the client.
	IpAddress string `protobuf:"bytes,2,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	// Optional. Device type (e.g., "mobile", "desktop", "tablet").
	DeviceType string `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// Optional. Operating system (e.g., "iOS 17.0", "Windows 11").
	Os string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	// Optional. Browser name and version (e.g., "Chrome 119.0").
	Browser       string `protobuf:"bytes,5,opt,name=browser,proto3" json:"browser,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSession_ClientInfo) Reset() {
	*x = UserSession_ClientInfo{}
	mi := &file_api_v1_user_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSession_ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSession_ClientInfo) ProtoMessage() {}

func (x *UserSession_ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSession_ClientInfo.ProtoReflect.Descriptor instead.
func (*UserSession_ClientInfo) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *UserSession_ClientInfo) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *UserSession_ClientInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *UserSession_ClientInfo) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *UserSession_ClientInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *UserSession_ClientInfo) GetBrowser() string {
	if x != nil {
		return x.Browser
	}
	return ""
}

var File_api_v1_user_service_proto protoreflect.FileDescriptor

const file_api_v1_user_service_proto_rawDesc = "" +
	"\n" +
	"\x19api/v1/user_service.proto\x12\fmemos.api.v1\x1a\x13api/v1/common.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/httpbody.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xcb\x04\n" +
	"\x04User\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x120\n" +
	"\x04role\x18\x02 \x01(\x0e2\x17.memos.api.v1.User.RoleB\x03\xe0A\x02R\x04role\x12\x1f\n" +
	"\busername\x18\x03 \x01(\tB\x03\xe0A\x02R\busername\x12\x19\n" +
	"\x05email\x18\x04 \x01(\tB\x03\xe0A\x01R\x05email\x12&\n" +
	"\fdisplay_name\x18\x05 \x01(\tB\x03\xe0A\x01R\vdisplayName\x12\"\n" +
	"\n" +
	"avatar_url\x18\x06 \x01(\tB\x03\xe0A\x01R\tavatarUrl\x12%\n" +
	"\vdescription\x18\a \x01(\tB\x03\xe0A\x01R\vdescription\x12\x1f\n" +
	"\bpassword\x18\b \x01(\tB\x03\xe0A\x04R\bpassword\x12.\n" +
	"\x05state\x18\t \x01(\x0e2\x13.memos.api.v1.StateB\x03\xe0A\x02R\x05state\x12@\n" +
	"\vcreate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\";\n" +
	"\x04Role\x12\x14\n" +
	"\x10ROLE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04HOST\x10\x01\x12\t\n" +
	"\x05ADMIN\x10\x02\x12\b\n" +
	"\x04USER\x10\x03:7\xeaA4\n" +
	"\x11memos.api.v1/User\x12\fusers/{user}\x1a\x04name*\x05users2\x04user\"\xbd\x01\n" +
	"\x10ListUsersRequest\x12 \n" +
	"\tpage_size\x18\x01 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tB\x03\xe0A\x01R\tpageToken\x12\x1b\n" +
	"\x06filter\x18\x03 \x01(\tB\x03\xe0A\x01R\x06filter\x12\x1e\n" +
	"\border_by\x18\x04 \x01(\tB\x03\xe0A\x01R\aorderBy\x12&\n" +
	"\fshow_deleted\x18\x05 \x01(\bB\x03\xe0A\x01R\vshowDeleted\"\x84\x01\n" +
	"\x11ListUsersResponse\x12(\n" +
	"\x05users\x18\x01 \x03(\v2\x12.memos.api.v1.UserR\x05users\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize\"}\n" +
	"\x0eGetUserRequest\x12-\n" +
	"\x04name\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x04name\x12<\n" +
	"\tread_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x01R\breadMask\"\xaf\x01\n" +
	"\x11CreateUserRequest\x12.\n" +
	"\x04user\x18\x01 \x01(\v2\x12.memos.api.v1.UserB\x06\xe0A\x02\xe0A\x04R\x04user\x12\x1c\n" +
	"\auser_id\x18\x02 \x01(\tB\x03\xe0A\x01R\x06userId\x12(\n" +
	"\rvalidate_only\x18\x03 \x01(\bB\x03\xe0A\x01R\fvalidateOnly\x12\"\n" +
	"\n" +
	"request_id\x18\x04 \x01(\tB\x03\xe0A\x01R\trequestId\"\xac\x01\n" +
	"\x11UpdateUserRequest\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x12.memos.api.v1.UserB\x03\xe0A\x02R\x04user\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x02R\n" +
	"updateMask\x12(\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x03\xe0A\x01R\fallowMissing\"]\n" +
	"\x11DeleteUserRequest\x12-\n" +
	"\x04name\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x04name\x12\x19\n" +
	"\x05force\x18\x02 \x01(\bB\x03\xe0A\x01R\x05force\"u\n" +
	"\x12SearchUsersRequest\x12\x19\n" +
	"\x05query\x18\x01 \x01(\tB\x03\xe0A\x02R\x05query\x12 \n" +
	"\tpage_size\x18\x02 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tB\x03\xe0A\x01R\tpageToken\"\x86\x01\n" +
	"\x13SearchUsersResponse\x12(\n" +
	"\x05users\x18\x01 \x03(\v2\x12.memos.api.v1.UserR\x05users\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize\"E\n" +
	"\x14GetUserAvatarRequest\x12-\n" +
	"\x04name\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x04name\"\xe4\x04\n" +
	"\tUserStats\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12R\n" +
	"\x17memo_display_timestamps\x18\x02 \x03(\v2\x1a.google.protobuf.TimestampR\x15memoDisplayTimestamps\x12M\n" +
	"\x0fmemo_type_stats\x18\x03 \x01(\v2%.memos.api.v1.UserStats.MemoTypeStatsR\rmemoTypeStats\x12B\n" +
	"\ttag_count\x18\x04 \x03(\v2%.memos.api.v1.UserStats.TagCountEntryR\btagCount\x12!\n" +
	"\fpinned_memos\x18\x05 \x03(\tR\vpinnedMemos\x12(\n" +
	"\x10total_memo_count\x18\x06 \x01(\x05R\x0etotalMemoCount\x1a;\n" +
	"\rTagCountEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a\x8b\x01\n" +
	"\rMemoTypeStats\x12\x1d\n" +
	"\n" +
	"link_count\x18\x01 \x01(\x05R\tlinkCount\x12\x1d\n" +
	"\n" +
	"code_count\x18\x02 \x01(\x05R\tcodeCount\x12\x1d\n" +
	"\n" +
	"todo_count\x18\x03 \x01(\x05R\ttodoCount\x12\x1d\n" +
	"\n" +
	"undo_count\x18\x04 \x01(\x05R\tundoCount:?\xeaA<\n" +
	"\x16memos.api.v1/UserStats\x12\fusers/{user}*\tuserStats2\tuserStats\"D\n" +
	"\x13GetUserStatsRequest\x12-\n" +
	"\x04name\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x04name\"\xde\x01\n" +
	"\vUserSetting\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12\x1b\n" +
	"\x06locale\x18\x02 \x01(\tB\x03\xe0A\x01R\x06locale\x12#\n" +
	"\n" +
	"appearance\x18\x03 \x01(\tB\x03\xe0A\x01R\n" +
	"appearance\x12,\n" +
	"\x0fmemo_visibility\x18\x04 \x01(\tB\x03\xe0A\x01R\x0ememoVisibility:F\xeaAC\n" +
	"\x18memos.api.v1/UserSetting\x12\fusers/{user}*\fuserSettings2\vuserSetting\"F\n" +
	"\x15GetUserSettingRequest\x12-\n" +
	"\x04name\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x04name\"\x96\x01\n" +
	"\x18UpdateUserSettingRequest\x128\n" +
	"\asetting\x18\x01 \x01(\v2\x19.memos.api.v1.UserSettingB\x03\xe0A\x02R\asetting\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x02R\n" +
	"updateMask\"\xe7\x02\n" +
	"\x0fUserAccessToken\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12&\n" +
	"\faccess_token\x18\x02 \x01(\tB\x03\xe0A\x03R\vaccessToken\x12%\n" +
	"\vdescription\x18\x03 \x01(\tB\x03\xe0A\x01R\vdescription\x12<\n" +
	"\tissued_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\bissuedAt\x12>\n" +
	"\n" +
	"expires_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01R\texpiresAt:n\xeaAk\n" +
	"\x1cmemos.api.v1/UserAccessToken\x12(users/{user}/accessTokens/{access_token}*\x10userAccessTokens2\x0fuserAccessToken\"\x96\x01\n" +
	"\x1bListUserAccessTokensRequest\x121\n" +
	"\x06parent\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x06parent\x12 \n" +
	"\tpage_size\x18\x02 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tB\x03\xe0A\x01R\tpageToken\"\xa9\x01\n" +
	"\x1cListUserAccessTokensResponse\x12B\n" +
	"\raccess_tokens\x18\x01 \x03(\v2\x1d.memos.api.v1.UserAccessTokenR\faccessTokens\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize\"\xc5\x01\n" +
	"\x1cCreateUserAccessTokenRequest\x121\n" +
	"\x06parent\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x06parent\x12E\n" +
	"\faccess_token\x18\x02 \x01(\v2\x1d.memos.api.v1.UserAccessTokenB\x03\xe0A\x02R\vaccessToken\x12+\n" +
	"\x0faccess_token_id\x18\x03 \x01(\tB\x03\xe0A\x01R\raccessTokenId\"X\n" +
	"\x1cDeleteUserAccessTokenRequest\x128\n" +
	"\x04name\x18\x01 \x01(\tB$\xe0A\x02\xfaA\x1e\n" +
	"\x1cmemos.api.v1/UserAccessTokenR\x04name\"\x94\x04\n" +
	"\vUserSession\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12\"\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tB\x03\xe0A\x03R\tsessionId\x12@\n" +
	"\vcreate_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12M\n" +
	"\x12last_accessed_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\x10lastAccessedTime\x12J\n" +
	"\vclient_info\x18\x05 \x01(\v2$.memos.api.v1.UserSession.ClientInfoB\x03\xe0A\x03R\n" +
	"clientInfo\x1a\xa4\x01\n" +
	"\n" +
	"ClientInfo\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x01 \x01(\tR\tuserAgent\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x02 \x01(\tR\tipAddress\x12$\n" +
	"\vdevice_type\x18\x03 \x01(\tB\x03\xe0A\x01R\n" +
	"deviceType\x12\x13\n" +
	"\x02os\x18\x04 \x01(\tB\x03\xe0A\x01R\x02os\x12\x1d\n" +
	"\abrowser\x18\x05 \x01(\tB\x03\xe0A\x01R\abrowser:D\xeaAA\n" +
	"\x18memos.api.v1/UserSession\x12\x1fusers/{user}/sessions/{session}\x1a\x04name\"L\n" +
	"\x17ListUserSessionsRequest\x121\n" +
	"\x06parent\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x06parent\"Q\n" +
	"\x18ListUserSessionsResponse\x125\n" +
	"\bsessions\x18\x01 \x03(\v2\x19.memos.api.v1.UserSessionR\bsessions\"P\n" +
	"\x18RevokeUserSessionRequest\x124\n" +
	"\x04name\x18\x01 \x01(\tB \xe0A\x02\xfaA\x1a\n" +
	"\x18memos.api.v1/UserSessionR\x04name\"_\n" +
	"\x17ListAllUserStatsRequest\x12 \n" +
	"\tpage_size\x18\x01 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tB\x03\xe0A\x01R\tpageToken\"\x99\x01\n" +
	"\x18ListAllUserStatsResponse\x126\n" +
	"\n" +
	"user_stats\x18\x01 \x03(\v2\x17.memos.api.v1.UserStatsR\tuserStats\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize2\xe2\x10\n" +
	"\vUserService\x12c\n" +
	"\tListUsers\x12\x1e.memos.api.v1.ListUsersRequest\x1a\x1f.memos.api.v1.ListUsersResponse\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/api/v1/users\x12b\n" +
	"\aGetUser\x12\x1c.memos.api.v1.GetUserRequest\x1a\x12.memos.api.v1.User\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/{name=users/*}\x12e\n" +
	"\n" +
	"CreateUser\x12\x1f.memos.api.v1.CreateUserRequest\x1a\x12.memos.api.v1.User\"\"\xdaA\x04user\x82\xd3\xe4\x93\x02\x15:\x04user\"\r/api/v1/users\x12\x7f\n" +
	"\n" +
	"UpdateUser\x12\x1f.memos.api.v1.UpdateUserRequest\x1a\x12.memos.api.v1.User\"<\xdaA\x10user,update_mask\x82\xd3\xe4\x93\x02#:\x04user2\x1b/api/v1/{user.name=users/*}\x12l\n" +
	"\n" +
	"DeleteUser\x12\x1f.memos.api.v1.DeleteUserRequest\x1a\x16.google.protobuf.Empty\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18*\x16/api/v1/{name=users/*}\x12x\n" +
	"\vSearchUsers\x12 .memos.api.v1.SearchUsersRequest\x1a!.memos.api.v1.SearchUsersResponse\"$\xdaA\x05query\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/users:search\x12w\n" +
	"\rGetUserAvatar\x12\".memos.api.v1.GetUserAvatarRequest\x1a\x14.google.api.HttpBody\",\xdaA\x04name\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v1/{name=users/*}/avatar\x12~\n" +
	"\x10ListAllUserStats\x12%.memos.api.v1.ListAllUserStatsRequest\x1a&.memos.api.v1.ListAllUserStatsResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v1/users:stats\x12z\n" +
	"\fGetUserStats\x12!.memos.api.v1.GetUserStatsRequest\x1a\x17.memos.api.v1.UserStats\".\xdaA\x04name\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/{name=users/*}:getStats\x12\x82\x01\n" +
	"\x0eGetUserSetting\x12#.memos.api.v1.GetUserSettingRequest\x1a\x19.memos.api.v1.UserSetting\"0\xdaA\x04name\x82\xd3\xe4\x93\x02#\x12!/api/v1/{name=users/*}:getSetting\x12\xab\x01\n" +
	"\x11UpdateUserSetting\x12&.memos.api.v1.UpdateUserSettingRequest\x1a\x19.memos.api.v1.UserSetting\"S\xdaA\x13setting,update_mask\x82\xd3\xe4\x93\x027:\asetting2,/api/v1/{setting.name=users/*}:updateSetting\x12\xa5\x01\n" +
	"\x14ListUserAccessTokens\x12).memos.api.v1.ListUserAccessTokensRequest\x1a*.memos.api.v1.ListUserAccessTokensResponse\"6\xdaA\x06parent\x82\xd3\xe4\x93\x02'\x12%/api/v1/{parent=users/*}/accessTokens\x12\xb5\x01\n" +
	"\x15CreateUserAccessToken\x12*.memos.api.v1.CreateUserAccessTokenRequest\x1a\x1d.memos.api.v1.UserAccessToken\"Q\xdaA\x13parent,access_token\x82\xd3\xe4\x93\x025:\faccess_token\"%/api/v1/{parent=users/*}/accessTokens\x12\x91\x01\n" +
	"\x15DeleteUserAccessToken\x12*.memos.api.v1.DeleteUserAccessTokenRequest\x1a\x16.google.protobuf.Empty\"4\xdaA\x04name\x82\xd3\xe4\x93\x02'*%/api/v1/{name=users/*/accessTokens/*}\x12\x95\x01\n" +
	"\x10ListUserSessions\x12%.memos.api.v1.ListUserSessionsRequest\x1a&.memos.api.v1.ListUserSessionsResponse\"2\xdaA\x06parent\x82\xd3\xe4\x93\x02#\x12!/api/v1/{parent=users/*}/sessions\x12\x85\x01\n" +
	"\x11RevokeUserSession\x12&.memos.api.v1.RevokeUserSessionRequest\x1a\x16.google.protobuf.Empty\"0\xdaA\x04name\x82\xd3\xe4\x93\x02#*!/api/v1/{name=users/*/sessions/*}B\xa8\x01\n" +
	"\x10com.memos.api.v1B\x10UserServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_user_service_proto_rawDescOnce sync.Once
	file_api_v1_user_service_proto_rawDescData []byte
)

func file_api_v1_user_service_proto_rawDescGZIP() []byte {
	file_api_v1_user_service_proto_rawDescOnce.Do(func() {
		file_api_v1_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_user_service_proto_rawDesc), len(file_api_v1_user_service_proto_rawDesc)))
	})
	return file_api_v1_user_service_proto_rawDescData
}

var file_api_v1_user_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_v1_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_v1_user_service_proto_goTypes = []any{
	(User_Role)(0),                       // 0: memos.api.v1.User.Role
	(*User)(nil),                         // 1: memos.api.v1.User
	(*ListUsersRequest)(nil),             // 2: memos.api.v1.ListUsersRequest
	(*ListUsersResponse)(nil),            // 3: memos.api.v1.ListUsersResponse
	(*GetUserRequest)(nil),               // 4: memos.api.v1.GetUserRequest
	(*CreateUserRequest)(nil),            // 5: memos.api.v1.CreateUserRequest
	(*UpdateUserRequest)(nil),            // 6: memos.api.v1.UpdateUserRequest
	(*DeleteUserRequest)(nil),            // 7: memos.api.v1.DeleteUserRequest
	(*SearchUsersRequest)(nil),           // 8: memos.api.v1.SearchUsersRequest
	(*SearchUsersResponse)(nil),          // 9: memos.api.v1.SearchUsersResponse
	(*GetUserAvatarRequest)(nil),         // 10: memos.api.v1.GetUserAvatarRequest
	(*UserStats)(nil),                    // 11: memos.api.v1.UserStats
	(*GetUserStatsRequest)(nil),          // 12: memos.api.v1.GetUserStatsRequest
	(*UserSetting)(nil),                  // 13: memos.api.v1.UserSetting
	(*GetUserSettingRequest)(nil),        // 14: memos.api.v1.GetUserSettingRequest
	(*UpdateUserSettingRequest)(nil),     // 15: memos.api.v1.UpdateUserSettingRequest
	(*UserAccessToken)(nil),              // 16: memos.api.v1.UserAccessToken
	(*ListUserAccessTokensRequest)(nil),  // 17: memos.api.v1.ListUserAccessTokensRequest
	(*ListUserAccessTokensResponse)(nil), // 18: memos.api.v1.ListUserAccessTokensResponse
	(*CreateUserAccessTokenRequest)(nil), // 19: memos.api.v1.CreateUserAccessTokenRequest
	(*DeleteUserAccessTokenRequest)(nil), // 20: memos.api.v1.DeleteUserAccessTokenRequest
	(*UserSession)(nil),                  // 21: memos.api.v1.UserSession
	(*ListUserSessionsRequest)(nil),      // 22: memos.api.v1.ListUserSessionsRequest
	(*ListUserSessionsResponse)(nil),     // 23: memos.api.v1.ListUserSessionsResponse
	(*RevokeUserSessionRequest)(nil),     // 24: memos.api.v1.RevokeUserSessionRequest
	(*ListAllUserStatsRequest)(nil),      // 25: memos.api.v1.ListAllUserStatsRequest
	(*ListAllUserStatsResponse)(nil),     // 26: memos.api.v1.ListAllUserStatsResponse
	nil,                                  // 27: memos.api.v1.UserStats.TagCountEntry
	(*UserStats_MemoTypeStats)(nil),      // 28: memos.api.v1.UserStats.MemoTypeStats
	(*UserSession_ClientInfo)(nil),       // 29: memos.api.v1.UserSession.ClientInfo
	(State)(0),                           // 30: memos.api.v1.State
	(*timestamppb.Timestamp)(nil),        // 31: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),        // 32: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),                // 33: google.protobuf.Empty
	(*httpbody.HttpBody)(nil),            // 34: google.api.HttpBody
}
var file_api_v1_user_service_proto_depIdxs = []int32{
	0,  // 0: memos.api.v1.User.role:type_name -> memos.api.v1.User.Role
	30, // 1: memos.api.v1.User.state:type_name -> memos.api.v1.State
	31, // 2: memos.api.v1.User.create_time:type_name -> google.protobuf.Timestamp
	31, // 3: memos.api.v1.User.update_time:type_name -> google.protobuf.Timestamp
	1,  // 4: memos.api.v1.ListUsersResponse.users:type_name -> memos.api.v1.User
	32, // 5: memos.api.v1.GetUserRequest.read_mask:type_name -> google.protobuf.FieldMask
	1,  // 6: memos.api.v1.CreateUserRequest.user:type_name -> memos.api.v1.User
	1,  // 7: memos.api.v1.UpdateUserRequest.user:type_name -> memos.api.v1.User
	32, // 8: memos.api.v1.UpdateUserRequest.update_mask:type_name -> google.protobuf.FieldMask
	1,  // 9: memos.api.v1.SearchUsersResponse.users:type_name -> memos.api.v1.User
	31, // 10: memos.api.v1.UserStats.memo_display_timestamps:type_name -> google.protobuf.Timestamp
	28, // 11: memos.api.v1.UserStats.memo_type_stats:type_name -> memos.api.v1.UserStats.MemoTypeStats
	27, // 12: memos.api.v1.UserStats.tag_count:type_name -> memos.api.v1.UserStats.TagCountEntry
	13, // 13: memos.api.v1.UpdateUserSettingRequest.setting:type_name -> memos.api.v1.UserSetting
	32, // 14: memos.api.v1.UpdateUserSettingRequest.update_mask:type_name -> google.protobuf.FieldMask
	31, // 15: memos.api.v1.UserAccessToken.issued_at:type_name -> google.protobuf.Timestamp
	31, // 16: memos.api.v1.UserAccessToken.expires_at:type_name -> google.protobuf.Timestamp
	16, // 17: memos.api.v1.ListUserAccessTokensResponse.access_tokens:type_name -> memos.api.v1.UserAccessToken
	16, // 18: memos.api.v1.CreateUserAccessTokenRequest.access_token:type_name -> memos.api.v1.UserAccessToken
	31, // 19: memos.api.v1.UserSession.create_time:type_name -> google.protobuf.Timestamp
	31, // 20: memos.api.v1.UserSession.last_accessed_time:type_name -> google.protobuf.Timestamp
	29, // 21: memos.api.v1.UserSession.client_info:type_name -> memos.api.v1.UserSession.ClientInfo
	21, // 22: memos.api.v1.ListUserSessionsResponse.sessions:type_name -> memos.api.v1.UserSession
	11, // 23: memos.api.v1.ListAllUserStatsResponse.user_stats:type_name -> memos.api.v1.UserStats
	2,  // 24: memos.api.v1.UserService.ListUsers:input_type -> memos.api.v1.ListUsersRequest
	4,  // 25: memos.api.v1.UserService.GetUser:input_type -> memos.api.v1.GetUserRequest
	5,  // 26: memos.api.v1.UserService.CreateUser:input_type -> memos.api.v1.CreateUserRequest
	6,  // 27: memos.api.v1.UserService.UpdateUser:input_type -> memos.api.v1.UpdateUserRequest
	7,  // 28: memos.api.v1.UserService.DeleteUser:input_type -> memos.api.v1.DeleteUserRequest
	8,  // 29: memos.api.v1.UserService.SearchUsers:input_type -> memos.api.v1.SearchUsersRequest
	10, // 30: memos.api.v1.UserService.GetUserAvatar:input_type -> memos.api.v1.GetUserAvatarRequest
	25, // 31: memos.api.v1.UserService.ListAllUserStats:input_type -> memos.api.v1.ListAllUserStatsRequest
	12, // 32: memos.api.v1.UserService.GetUserStats:input_type -> memos.api.v1.GetUserStatsRequest
	14, // 33: memos.api.v1.UserService.GetUserSetting:input_type -> memos.api.v1.GetUserSettingRequest
	15, // 34: memos.api.v1.UserService.UpdateUserSetting:input_type -> memos.api.v1.UpdateUserSettingRequest
	17, // 35: memos.api.v1.UserService.ListUserAccessTokens:input_type -> memos.api.v1.ListUserAccessTokensRequest
	19, // 36: memos.api.v1.UserService.CreateUserAccessToken:input_type -> memos.api.v1.CreateUserAccessTokenRequest
	20, // 37: memos.api.v1.UserService.DeleteUserAccessToken:input_type -> memos.api.v1.DeleteUserAccessTokenRequest
	22, // 38: memos.api.v1.UserService.ListUserSessions:input_type -> memos.api.v1.ListUserSessionsRequest
	24, // 39: memos.api.v1.UserService.RevokeUserSession:input_type -> memos.api.v1.RevokeUserSessionRequest
	3,  // 40: memos.api.v1.UserService.ListUsers:output_type -> memos.api.v1.ListUsersResponse
	1,  // 41: memos.api.v1.UserService.GetUser:output_type -> memos.api.v1.User
	1,  // 42: memos.api.v1.UserService.CreateUser:output_type -> memos.api.v1.User
	1,  // 43: memos.api.v1.UserService.UpdateUser:output_type -> memos.api.v1.User
	33, // 44: memos.api.v1.UserService.DeleteUser:output_type -> google.protobuf.Empty
	9,  // 45: memos.api.v1.UserService.SearchUsers:output_type -> memos.api.v1.SearchUsersResponse
	34, // 46: memos.api.v1.UserService.GetUserAvatar:output_type -> google.api.HttpBody
	26, // 47: memos.api.v1.UserService.ListAllUserStats:output_type -> memos.api.v1.ListAllUserStatsResponse
	11, // 48: memos.api.v1.UserService.GetUserStats:output_type -> memos.api.v1.UserStats
	13, // 49: memos.api.v1.UserService.GetUserSetting:output_type -> memos.api.v1.UserSetting
	13, // 50: memos.api.v1.UserService.UpdateUserSetting:output_type -> memos.api.v1.UserSetting
	18, // 51: memos.api.v1.UserService.ListUserAccessTokens:output_type -> memos.api.v1.ListUserAccessTokensResponse
	16, // 52: memos.api.v1.UserService.CreateUserAccessToken:output_type -> memos.api.v1.UserAccessToken
	33, // 53: memos.api.v1.UserService.DeleteUserAccessToken:output_type -> google.protobuf.Empty
	23, // 54: memos.api.v1.UserService.ListUserSessions:output_type -> memos.api.v1.ListUserSessionsResponse
	33, // 55: memos.api.v1.UserService.RevokeUserSession:output_type -> google.protobuf.Empty
	40, // [40:56] is the sub-list for method output_type
	24, // [24:40] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_v1_user_service_proto_init() }
func file_api_v1_user_service_proto_init() {
	if File_api_v1_user_service_proto != nil {
		return
	}
	file_api_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_user_service_proto_rawDesc), len(file_api_v1_user_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_user_service_proto_goTypes,
		DependencyIndexes: file_api_v1_user_service_proto_depIdxs,
		EnumInfos:         file_api_v1_user_service_proto_enumTypes,
		MessageInfos:      file_api_v1_user_service_proto_msgTypes,
	}.Build()
	File_api_v1_user_service_proto = out.File
	file_api_v1_user_service_proto_goTypes = nil
	file_api_v1_user_service_proto_depIdxs = nil
}
