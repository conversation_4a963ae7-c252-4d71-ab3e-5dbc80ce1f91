{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "types": ["vite/client"], "allowJs": false, "skipLibCheck": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"]}}, "include": ["./src"]}