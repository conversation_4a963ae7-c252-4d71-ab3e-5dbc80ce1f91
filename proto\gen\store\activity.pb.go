// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/activity.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActivityMemoCommentPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemoId        int32                  `protobuf:"varint,1,opt,name=memo_id,json=memoId,proto3" json:"memo_id,omitempty"`
	RelatedMemoId int32                  `protobuf:"varint,2,opt,name=related_memo_id,json=relatedMemoId,proto3" json:"related_memo_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityMemoCommentPayload) Reset() {
	*x = ActivityMemoCommentPayload{}
	mi := &file_store_activity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityMemoCommentPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityMemoCommentPayload) ProtoMessage() {}

func (x *ActivityMemoCommentPayload) ProtoReflect() protoreflect.Message {
	mi := &file_store_activity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityMemoCommentPayload.ProtoReflect.Descriptor instead.
func (*ActivityMemoCommentPayload) Descriptor() ([]byte, []int) {
	return file_store_activity_proto_rawDescGZIP(), []int{0}
}

func (x *ActivityMemoCommentPayload) GetMemoId() int32 {
	if x != nil {
		return x.MemoId
	}
	return 0
}

func (x *ActivityMemoCommentPayload) GetRelatedMemoId() int32 {
	if x != nil {
		return x.RelatedMemoId
	}
	return 0
}

type ActivityPayload struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	MemoComment   *ActivityMemoCommentPayload `protobuf:"bytes,1,opt,name=memo_comment,json=memoComment,proto3" json:"memo_comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityPayload) Reset() {
	*x = ActivityPayload{}
	mi := &file_store_activity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityPayload) ProtoMessage() {}

func (x *ActivityPayload) ProtoReflect() protoreflect.Message {
	mi := &file_store_activity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityPayload.ProtoReflect.Descriptor instead.
func (*ActivityPayload) Descriptor() ([]byte, []int) {
	return file_store_activity_proto_rawDescGZIP(), []int{1}
}

func (x *ActivityPayload) GetMemoComment() *ActivityMemoCommentPayload {
	if x != nil {
		return x.MemoComment
	}
	return nil
}

var File_store_activity_proto protoreflect.FileDescriptor

const file_store_activity_proto_rawDesc = "" +
	"\n" +
	"\x14store/activity.proto\x12\vmemos.store\"]\n" +
	"\x1aActivityMemoCommentPayload\x12\x17\n" +
	"\amemo_id\x18\x01 \x01(\x05R\x06memoId\x12&\n" +
	"\x0frelated_memo_id\x18\x02 \x01(\x05R\rrelatedMemoId\"]\n" +
	"\x0fActivityPayload\x12J\n" +
	"\fmemo_comment\x18\x01 \x01(\v2'.memos.store.ActivityMemoCommentPayloadR\vmemoCommentB\x98\x01\n" +
	"\x0fcom.memos.storeB\rActivityProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_activity_proto_rawDescOnce sync.Once
	file_store_activity_proto_rawDescData []byte
)

func file_store_activity_proto_rawDescGZIP() []byte {
	file_store_activity_proto_rawDescOnce.Do(func() {
		file_store_activity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_activity_proto_rawDesc), len(file_store_activity_proto_rawDesc)))
	})
	return file_store_activity_proto_rawDescData
}

var file_store_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_store_activity_proto_goTypes = []any{
	(*ActivityMemoCommentPayload)(nil), // 0: memos.store.ActivityMemoCommentPayload
	(*ActivityPayload)(nil),            // 1: memos.store.ActivityPayload
}
var file_store_activity_proto_depIdxs = []int32{
	0, // 0: memos.store.ActivityPayload.memo_comment:type_name -> memos.store.ActivityMemoCommentPayload
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_store_activity_proto_init() }
func file_store_activity_proto_init() {
	if File_store_activity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_activity_proto_rawDesc), len(file_store_activity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_activity_proto_goTypes,
		DependencyIndexes: file_store_activity_proto_depIdxs,
		MessageInfos:      file_store_activity_proto_msgTypes,
	}.Build()
	File_store_activity_proto = out.File
	file_store_activity_proto_goTypes = nil
	file_store_activity_proto_depIdxs = nil
}
