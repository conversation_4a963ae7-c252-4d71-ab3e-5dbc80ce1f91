// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/attachment_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Attachment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the attachment.
	// Format: attachments/{attachment}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Output only. The creation timestamp.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The filename of the attachment.
	Filename string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	// Input only. The content of the attachment.
	Content []byte `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// Optional. The external link of the attachment.
	ExternalLink string `protobuf:"bytes,5,opt,name=external_link,json=externalLink,proto3" json:"external_link,omitempty"`
	// The MIME type of the attachment.
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// Output only. The size of the attachment in bytes.
	Size int64 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`
	// Optional. The related memo. Refer to `Memo.name`.
	// Format: memos/{memo}
	Memo          *string `protobuf:"bytes,8,opt,name=memo,proto3,oneof" json:"memo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{0}
}

func (x *Attachment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Attachment) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Attachment) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Attachment) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Attachment) GetExternalLink() string {
	if x != nil {
		return x.ExternalLink
	}
	return ""
}

func (x *Attachment) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Attachment) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Attachment) GetMemo() string {
	if x != nil && x.Memo != nil {
		return *x.Memo
	}
	return ""
}

type CreateAttachmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The attachment to create.
	Attachment *Attachment `protobuf:"bytes,1,opt,name=attachment,proto3" json:"attachment,omitempty"`
	// Optional. The attachment ID to use for this attachment.
	// If empty, a unique ID will be generated.
	AttachmentId  string `protobuf:"bytes,2,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAttachmentRequest) Reset() {
	*x = CreateAttachmentRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAttachmentRequest) ProtoMessage() {}

func (x *CreateAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAttachmentRequest.ProtoReflect.Descriptor instead.
func (*CreateAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAttachmentRequest) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *CreateAttachmentRequest) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

type ListAttachmentsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional. The maximum number of attachments to return.
	// The service may return fewer than this value.
	// If unspecified, at most 50 attachments will be returned.
	// The maximum value is 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token, received from a previous `ListAttachments` call.
	// Provide this to retrieve the subsequent page.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. Filter to apply to the list results.
	// Example: "type=image/png" or "filename:*.jpg"
	// Supported operators: =, !=, <, <=, >, >=, :
	// Supported fields: filename, type, size, create_time, memo
	Filter string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. The order to sort results by.
	// Example: "create_time desc" or "filename asc"
	OrderBy       string `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAttachmentsRequest) Reset() {
	*x = ListAttachmentsRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAttachmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttachmentsRequest) ProtoMessage() {}

func (x *ListAttachmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttachmentsRequest.ProtoReflect.Descriptor instead.
func (*ListAttachmentsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAttachmentsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAttachmentsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListAttachmentsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListAttachmentsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListAttachmentsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of attachments.
	Attachments []*Attachment `protobuf:"bytes,1,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of attachments (may be approximate).
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAttachmentsResponse) Reset() {
	*x = ListAttachmentsResponse{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAttachmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttachmentsResponse) ProtoMessage() {}

func (x *ListAttachmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttachmentsResponse.ProtoReflect.Descriptor instead.
func (*ListAttachmentsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAttachmentsResponse) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ListAttachmentsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListAttachmentsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type GetAttachmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The attachment name of the attachment to retrieve.
	// Format: attachments/{attachment}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAttachmentRequest) Reset() {
	*x = GetAttachmentRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachmentRequest) ProtoMessage() {}

func (x *GetAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachmentRequest.ProtoReflect.Descriptor instead.
func (*GetAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAttachmentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetAttachmentBinaryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The attachment name of the attachment.
	// Format: attachments/{attachment}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The filename of the attachment. Mainly used for downloading.
	Filename string `protobuf:"bytes,2,opt,name=filename,proto3" json:"filename,omitempty"`
	// Optional. A flag indicating if the thumbnail version of the attachment should be returned.
	Thumbnail     bool `protobuf:"varint,3,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAttachmentBinaryRequest) Reset() {
	*x = GetAttachmentBinaryRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAttachmentBinaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachmentBinaryRequest) ProtoMessage() {}

func (x *GetAttachmentBinaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachmentBinaryRequest.ProtoReflect.Descriptor instead.
func (*GetAttachmentBinaryRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAttachmentBinaryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAttachmentBinaryRequest) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *GetAttachmentBinaryRequest) GetThumbnail() bool {
	if x != nil {
		return x.Thumbnail
	}
	return false
}

type UpdateAttachmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The attachment which replaces the attachment on the server.
	Attachment *Attachment `protobuf:"bytes,1,opt,name=attachment,proto3" json:"attachment,omitempty"`
	// Required. The list of fields to update.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAttachmentRequest) Reset() {
	*x = UpdateAttachmentRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAttachmentRequest) ProtoMessage() {}

func (x *UpdateAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAttachmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateAttachmentRequest) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *UpdateAttachmentRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteAttachmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The attachment name of the attachment to delete.
	// Format: attachments/{attachment}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAttachmentRequest) Reset() {
	*x = DeleteAttachmentRequest{}
	mi := &file_api_v1_attachment_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAttachmentRequest) ProtoMessage() {}

func (x *DeleteAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_attachment_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAttachmentRequest.ProtoReflect.Descriptor instead.
func (*DeleteAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_attachment_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteAttachmentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_attachment_service_proto protoreflect.FileDescriptor

const file_api_v1_attachment_service_proto_rawDesc = "" +
	"\n" +
	"\x1fapi/v1/attachment_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/httpbody.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xfb\x02\n" +
	"\n" +
	"Attachment\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12@\n" +
	"\vcreate_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12\x1f\n" +
	"\bfilename\x18\x03 \x01(\tB\x03\xe0A\x02R\bfilename\x12\x1d\n" +
	"\acontent\x18\x04 \x01(\fB\x03\xe0A\x04R\acontent\x12(\n" +
	"\rexternal_link\x18\x05 \x01(\tB\x03\xe0A\x01R\fexternalLink\x12\x17\n" +
	"\x04type\x18\x06 \x01(\tB\x03\xe0A\x02R\x04type\x12\x17\n" +
	"\x04size\x18\a \x01(\x03B\x03\xe0A\x03R\x04size\x12\x1c\n" +
	"\x04memo\x18\b \x01(\tB\x03\xe0A\x01H\x00R\x04memo\x88\x01\x01:O\xeaAL\n" +
	"\x17memos.api.v1/Attachment\x12\x18attachments/{attachment}*\vattachments2\n" +
	"attachmentB\a\n" +
	"\x05_memo\"\x82\x01\n" +
	"\x17CreateAttachmentRequest\x12=\n" +
	"\n" +
	"attachment\x18\x01 \x01(\v2\x18.memos.api.v1.AttachmentB\x03\xe0A\x02R\n" +
	"attachment\x12(\n" +
	"\rattachment_id\x18\x02 \x01(\tB\x03\xe0A\x01R\fattachmentId\"\x9b\x01\n" +
	"\x16ListAttachmentsRequest\x12 \n" +
	"\tpage_size\x18\x01 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tB\x03\xe0A\x01R\tpageToken\x12\x1b\n" +
	"\x06filter\x18\x03 \x01(\tB\x03\xe0A\x01R\x06filter\x12\x1e\n" +
	"\border_by\x18\x04 \x01(\tB\x03\xe0A\x01R\aorderBy\"\x9c\x01\n" +
	"\x17ListAttachmentsResponse\x12:\n" +
	"\vattachments\x18\x01 \x03(\v2\x18.memos.api.v1.AttachmentR\vattachments\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize\"K\n" +
	"\x14GetAttachmentRequest\x123\n" +
	"\x04name\x18\x01 \x01(\tB\x1f\xe0A\x02\xfaA\x19\n" +
	"\x17memos.api.v1/AttachmentR\x04name\"\x95\x01\n" +
	"\x1aGetAttachmentBinaryRequest\x123\n" +
	"\x04name\x18\x01 \x01(\tB\x1f\xe0A\x02\xfaA\x19\n" +
	"\x17memos.api.v1/AttachmentR\x04name\x12\x1f\n" +
	"\bfilename\x18\x02 \x01(\tB\x03\xe0A\x02R\bfilename\x12!\n" +
	"\tthumbnail\x18\x03 \x01(\bB\x03\xe0A\x01R\tthumbnail\"\x9a\x01\n" +
	"\x17UpdateAttachmentRequest\x12=\n" +
	"\n" +
	"attachment\x18\x01 \x01(\v2\x18.memos.api.v1.AttachmentB\x03\xe0A\x02R\n" +
	"attachment\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x02R\n" +
	"updateMask\"N\n" +
	"\x17DeleteAttachmentRequest\x123\n" +
	"\x04name\x18\x01 \x01(\tB\x1f\xe0A\x02\xfaA\x19\n" +
	"\x17memos.api.v1/AttachmentR\x04name2\xdb\x06\n" +
	"\x11AttachmentService\x12\x89\x01\n" +
	"\x10CreateAttachment\x12%.memos.api.v1.CreateAttachmentRequest\x1a\x18.memos.api.v1.Attachment\"4\xdaA\n" +
	"attachment\x82\xd3\xe4\x93\x02!:\n" +
	"attachment\"\x13/api/v1/attachments\x12{\n" +
	"\x0fListAttachments\x12$.memos.api.v1.ListAttachmentsRequest\x1a%.memos.api.v1.ListAttachmentsResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v1/attachments\x12z\n" +
	"\rGetAttachment\x12\".memos.api.v1.GetAttachmentRequest\x1a\x18.memos.api.v1.Attachment\"+\xdaA\x04name\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/{name=attachments/*}\x12\x94\x01\n" +
	"\x13GetAttachmentBinary\x12(.memos.api.v1.GetAttachmentBinaryRequest\x1a\x14.google.api.HttpBody\"=\xdaA\rname,filename\x82\xd3\xe4\x93\x02'\x12%/file/{name=attachments/*}/{filename}\x12\xa9\x01\n" +
	"\x10UpdateAttachment\x12%.memos.api.v1.UpdateAttachmentRequest\x1a\x18.memos.api.v1.Attachment\"T\xdaA\x16attachment,update_mask\x82\xd3\xe4\x93\x025:\n" +
	"attachment2'/api/v1/{attachment.name=attachments/*}\x12~\n" +
	"\x10DeleteAttachment\x12%.memos.api.v1.DeleteAttachmentRequest\x1a\x16.google.protobuf.Empty\"+\xdaA\x04name\x82\xd3\xe4\x93\x02\x1e*\x1c/api/v1/{name=attachments/*}B\xae\x01\n" +
	"\x10com.memos.api.v1B\x16AttachmentServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_attachment_service_proto_rawDescOnce sync.Once
	file_api_v1_attachment_service_proto_rawDescData []byte
)

func file_api_v1_attachment_service_proto_rawDescGZIP() []byte {
	file_api_v1_attachment_service_proto_rawDescOnce.Do(func() {
		file_api_v1_attachment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_attachment_service_proto_rawDesc), len(file_api_v1_attachment_service_proto_rawDesc)))
	})
	return file_api_v1_attachment_service_proto_rawDescData
}

var file_api_v1_attachment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_v1_attachment_service_proto_goTypes = []any{
	(*Attachment)(nil),                 // 0: memos.api.v1.Attachment
	(*CreateAttachmentRequest)(nil),    // 1: memos.api.v1.CreateAttachmentRequest
	(*ListAttachmentsRequest)(nil),     // 2: memos.api.v1.ListAttachmentsRequest
	(*ListAttachmentsResponse)(nil),    // 3: memos.api.v1.ListAttachmentsResponse
	(*GetAttachmentRequest)(nil),       // 4: memos.api.v1.GetAttachmentRequest
	(*GetAttachmentBinaryRequest)(nil), // 5: memos.api.v1.GetAttachmentBinaryRequest
	(*UpdateAttachmentRequest)(nil),    // 6: memos.api.v1.UpdateAttachmentRequest
	(*DeleteAttachmentRequest)(nil),    // 7: memos.api.v1.DeleteAttachmentRequest
	(*timestamppb.Timestamp)(nil),      // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),      // 9: google.protobuf.FieldMask
	(*httpbody.HttpBody)(nil),          // 10: google.api.HttpBody
	(*emptypb.Empty)(nil),              // 11: google.protobuf.Empty
}
var file_api_v1_attachment_service_proto_depIdxs = []int32{
	8,  // 0: memos.api.v1.Attachment.create_time:type_name -> google.protobuf.Timestamp
	0,  // 1: memos.api.v1.CreateAttachmentRequest.attachment:type_name -> memos.api.v1.Attachment
	0,  // 2: memos.api.v1.ListAttachmentsResponse.attachments:type_name -> memos.api.v1.Attachment
	0,  // 3: memos.api.v1.UpdateAttachmentRequest.attachment:type_name -> memos.api.v1.Attachment
	9,  // 4: memos.api.v1.UpdateAttachmentRequest.update_mask:type_name -> google.protobuf.FieldMask
	1,  // 5: memos.api.v1.AttachmentService.CreateAttachment:input_type -> memos.api.v1.CreateAttachmentRequest
	2,  // 6: memos.api.v1.AttachmentService.ListAttachments:input_type -> memos.api.v1.ListAttachmentsRequest
	4,  // 7: memos.api.v1.AttachmentService.GetAttachment:input_type -> memos.api.v1.GetAttachmentRequest
	5,  // 8: memos.api.v1.AttachmentService.GetAttachmentBinary:input_type -> memos.api.v1.GetAttachmentBinaryRequest
	6,  // 9: memos.api.v1.AttachmentService.UpdateAttachment:input_type -> memos.api.v1.UpdateAttachmentRequest
	7,  // 10: memos.api.v1.AttachmentService.DeleteAttachment:input_type -> memos.api.v1.DeleteAttachmentRequest
	0,  // 11: memos.api.v1.AttachmentService.CreateAttachment:output_type -> memos.api.v1.Attachment
	3,  // 12: memos.api.v1.AttachmentService.ListAttachments:output_type -> memos.api.v1.ListAttachmentsResponse
	0,  // 13: memos.api.v1.AttachmentService.GetAttachment:output_type -> memos.api.v1.Attachment
	10, // 14: memos.api.v1.AttachmentService.GetAttachmentBinary:output_type -> google.api.HttpBody
	0,  // 15: memos.api.v1.AttachmentService.UpdateAttachment:output_type -> memos.api.v1.Attachment
	11, // 16: memos.api.v1.AttachmentService.DeleteAttachment:output_type -> google.protobuf.Empty
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_v1_attachment_service_proto_init() }
func file_api_v1_attachment_service_proto_init() {
	if File_api_v1_attachment_service_proto != nil {
		return
	}
	file_api_v1_attachment_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_attachment_service_proto_rawDesc), len(file_api_v1_attachment_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_attachment_service_proto_goTypes,
		DependencyIndexes: file_api_v1_attachment_service_proto_depIdxs,
		MessageInfos:      file_api_v1_attachment_service_proto_msgTypes,
	}.Build()
	File_api_v1_attachment_service_proto = out.File
	file_api_v1_attachment_service_proto_goTypes = nil
	file_api_v1_attachment_service_proto_depIdxs = nil
}
