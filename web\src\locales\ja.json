{"auth": {"create-your-account": "アカウントを作成", "host-tip": "管理者として登録されます。", "new-password": "新しいパスワード", "repeat-new-password": "新しいパスワードを確認(繰り返し)", "sign-in-tip": "すでにアカウントを持っていますか?", "sign-up-tip": "アカウントを持っていませんか?"}, "common": {"about": "Memos について", "admin": "管理者設定", "archive": "アーカイブにする", "archived": "ゴミ箱", "avatar": "アイコン", "basic": "基本設定", "beta": "ベータ", "cancel": "キャンセル", "change": "変更", "clear": "クリア", "close": "閉じる", "confirm": "確認する", "create": "作成する", "database": "データベース", "days": "日", "delete": "削除する", "description": "説明", "edit": "編集する", "email": "Eメール", "explore": "共有メモ", "file": "ファイル", "filter": "フィルター", "home": "ホーム", "image": "画像", "inbox": "受信トレイ", "language": "言語", "learn-more": "さらに詳しく", "link": "リンク", "mark": "マーク", "memos": "Memos", "name": "名前", "new": "新しく追加", "nickname": "ニックネーム", "null": "null", "or": "もしくは", "password": "パスワード", "pin": "ピン", "pinned": "ピン留め", "preview": "プレビュー", "profile": "プロファイル", "remember-me": "パスワードを保存する", "rename": "リネーム", "reset": "リセット", "resources": "ファイル", "restore": "戻す", "role": "ロール", "save": "保存する", "search": "検索", "select": "選択", "settings": "設定", "share": "シェアする", "sign-in": "サインイン", "sign-in-with": "{{provider}}でサインイン", "sign-out": "ログアウト", "sign-up": "登録", "statistics": "統計", "tags": "タグ", "title": "タイトル", "type": "タイプ", "unpin": "ピンを外す", "update": "上書き", "upload": "アップロード", "username": "ユーザーネーム", "version": "バージョン", "visibility": "公開範囲", "yourself": "あなた自身の"}, "days": {"fri": "金", "mon": "月", "sat": "土", "sun": "日", "thu": "木", "tue": "火", "wed": "水"}, "editor": {"add-your-comment-here": "ここにコメントを追加...", "any-thoughts": "今思ったことは...", "save": "保存"}, "inbox": {"memo-comment": "{{user}} があなたの {{memo}} にコメントしました。", "version-update": "新しいバージョン {{version}} が利用可能です!"}, "memo": {"archived-at": "アーカイブ:", "code": "コード", "comment": {"self": "コメント", "write-a-comment": "コメントを書く"}, "copy-link": "リンクをコピー", "count-memos-in-date": "{{date}} 日に {{count}} 件のメモ", "delete-confirm": "本当に削除しますか？\n\n削除後の復元は不可能です。", "links": "リンク", "no-archived-memos": "アーカイブされたメモはありません。", "remove-completed-task-list-items": "完了を削除", "remove-completed-task-list-items-confirm": "すべての完了したタスクを削除してもよろしいですか？（このアクションは取り消せません）", "search-placeholder": "メモを検索", "show-less": "表示を少なくする", "show-more": "続きを見る", "to-do": "To-do", "view-detail": "詳細を見る", "visibility": {"disabled": "公開メモは無効化されています。", "private": "非公開", "protected": "メンバーに表示", "public": "公開メモ"}}, "message": {"archived-successfully": "アーカイブが完了しました", "change-memo-created-time": "メモの作成時間が変更されました。", "copied": "コピーしました！", "deleted-successfully": "削除されました", "fill-all": "すべての項目を入力してください。", "maximum-upload-size-is": "ファイルの最大サイズは{{size}} MiBです。", "memo-not-found": "メモは見つかりませんでした", "new-password-not-match": "新しいパスワードが一致しません", "no-data": "ファイルが見つかりませんでした。", "password-changed": "パスワードを変更しました", "password-not-match": "パスワードが一致しません。", "remove-completed-task-list-items-successfully": "削除が成功しました！", "restored-successfully": "リストア成功", "succeed-copy-link": "リンクのコピーに成功しました。", "update-succeed": "変更は保存されました", "user-not-found": "ユーザーが見つかりませんでした"}, "reference": {"add-references": "変更は保存されました", "embedded-usage": "埋め込みとして使用する", "no-memos-found": "メモが見つかりません", "search-placeholder": "内容を検索"}, "resource": {"clear": "クリア", "copy-link": "リンクをコピー", "create-dialog": {"external-link": {"file-name": "ファイル名", "file-name-placeholder": "ファイル名", "link": "リンク", "link-placeholder": "https://the.link.to/your/resource", "option": "リンクからアップロード", "type": "タイプ", "type-placeholder": "ファイルタイプ"}, "local-file": {"choose": "ファイルを選択する…", "option": "PCからアップロード"}, "title": "ファイルをアップロード", "upload-method": "アップロード方法"}, "delete-resource": "ファイルを削除する", "delete-selected-resources": "選択したファイルを削除", "fetching-data": "データを取得中…", "file-drag-drop-prompt": "アップロードするファイルをドラックアンドドロップする", "linked-amount": "リンクしているメモの数", "no-files-selected": "ファイルが選択されていません", "no-resources": "ファイルはありません。", "no-unused-resources": "未使用のファイルはありません", "reset-link": "リンクをリセット", "reset-link-prompt": "本当にリンクをリセットしますか？実行すると既存のリンクは無効化されます。操作後、元に戻せません。", "reset-resource-link": "リンクをリセットする"}, "router": {"back-to-top": "トップに戻る", "go-to-home": "ホームへ戻る"}, "setting": {"account-section": {"change-password": "パスワードを変更", "email-note": "オプション", "export-memos": "メモをエクスポート", "nickname-note": "表示される名前です", "openapi-reset": "OpenAPI Keyをリセットする", "openapi-sample-post": "{{url}}より、こんにちは！#memos", "openapi-title": "OpenAPI", "reset-api": "APIをリセットする", "title": "アカウント情報", "update-information": "プロフィールを編集", "username-note": "ログインに使用します"}, "appearance-option": {"dark": "ダークモード", "light": "ライトモード", "system": "システム設定に従う"}, "member": "メンバー", "member-list": "メンバーリスト", "member-section": {"archive-member": "メンバーをゴミ箱に入れる", "archive-warning": "本当に{{username}}をゴミ箱に追加してよろしいですか?", "create-a-member": "メンバーを追加する", "delete-member": "メンバーを消去する", "delete-warning": "本当に{{username}}を消去していいですか?\n\n消去後、元に戻せません！"}, "memo-related": "Memo", "my-account": "アカウント設定", "preference": "設定", "preference-section": {"default-memo-sort-option": "メモの表示時間", "default-memo-visibility": "公開範囲の初期設定", "theme": "テーマ"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Authorization endpoint", "client-id": "Client ID", "client-secret": "Client secret", "confirm-delete": "本当に\"{{name}}\"のSSO設定を削除してもいいですか?\n\n消去後、元に戻せません!", "create-sso": "SSOを作成する", "custom": "カスタム", "delete-sso": "削除を確定", "disabled-password-login-warning": "パスワードによるログインが無効になっていますので、アイデンティティプロバイダーを削除する際はご注意ください", "display-name": "表示名", "identifier": "識別子", "identifier-filter": "識別子フィルター", "redirect-url": "リダイレクトURL", "scopes": "<PERSON><PERSON><PERSON>", "sso-created": "SSO {{name}}を作成しました", "sso-list": "SSOリスト", "sso-updated": "SSO {{name}}を更新しました", "template": "テンプレート", "token-endpoint": "Token endpoint", "update-sso": "SSOを更新する", "user-endpoint": "User endpoint"}, "storage": "ストレージ", "storage-section": {"accesskey": "Access key", "accesskey-placeholder": "Access key / Access ID", "bucket": "Bucket", "bucket-placeholder": "Bucket name", "create-a-service": "ストレージを登録", "create-storage": "ストレージを作成", "current-storage": "現在のストレージ", "delete-storage": "ストレージを削除", "endpoint": "Endpoint", "local-storage-path": "Local storage のパス", "path": "ストレージのパス", "path-description": "ローカルストレージから、{filename}のような変数を使用できます。", "path-placeholder": "custom/path", "presign-placeholder": "署名付きURL（オプション）", "region": "Region", "region-placeholder": "Region name", "s3-compatible-url": "S3 Compatible URL", "secretkey": "Secret key", "secretkey-placeholder": "Secret key / Access Key", "storage-services": "登録済みのストレージサービス", "type-database": "Database", "type-local": "Local", "update-a-service": "ストレージを更新", "update-local-path": "Local Storage のパスを更新", "update-local-path-description": "Local storage のパスは、データベースファイルへの相対パスを登録してください", "update-storage": "ストレージを更新", "url-prefix": "URL prefix", "url-prefix-placeholder": "カスタムURLプレフィックス（オプション）", "url-suffix": "URLサフィックス", "url-suffix-placeholder": "カスタムURLサフィックス (オプション)", "warning-text": "本当にストレージサービス\"{{name}}\"を消去しますか? \n\n消去後、元に戻せません！"}, "system": "システム", "system-section": {"additional-script": "追加スクリプト", "additional-script-placeholder": "Javascriptのコードを追加してください", "additional-style": "追加CSS", "additional-style-placeholder": "CSSのコードを追加してください", "allow-user-signup": "ユーザー登録を有効にする", "customize-server": {"appearance": "サーバーの外観", "description": "説明", "icon-url": "アイコンのURL", "locale": "サーバーの言語", "title": "サーバーをカスタマイズ"}, "disable-password-login": "パスワードでのログインを無効にする", "disable-password-login-final-warning": "何をしているか理解している場合は、「CONFIRM」と入力してください。", "disable-password-login-warning": "これにより、すべてのユーザーのパスワードでのログインが無効になります。設定済みのIDプロバイダーが失敗した場合、この設定をデータベースで元に戻さない限り、ログインすることはできません。IDプロバイダーを削除する際も、非常に注意が必要です", "disable-public-memos": "公開メモを無効化する", "display-with-updated-time": "更新日時を表示する", "enable-auto-compact": "折りたたみを有効化", "enable-double-click-to-edit": "ダブルクリックで編集を有効化", "enable-password-login": "パスワードでのログインを有効にする", "enable-password-login-warning": "これにより、すべてのユーザーのパスワードでのログインが有効になります。SSOとパスワードの両方を使用してログインできるようにしたい場合のみ、続行してください", "max-upload-size": "最大ファイルサイズ(MiB)", "max-upload-size-hint": "推奨サイズは32 MiBです。", "removed-completed-task-list-items": "完了削除を有効にする", "server-name": "サーバーの名前"}}, "tag": {"all-tags": "すべてのタグ", "create-tag": "タグを作成する", "create-tags-guide": "`#タグ名`と入力することでタグを作成できます。", "delete-confirm": "このタグを削除しますか?", "delete-tag": "タグを削除", "no-tag-found": "タグが見つかりませんでした"}}