version: 1

before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy

builds:
  - main: ./bin/memos
    binary: memos
    goos:
      - linux
      - darwin

archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of `uname`.
    name_template: >-
      {{ .ProjectName }}_{{ .Tag }}_{{ .Os }}_{{ .Arch }}

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"

checksum:
  disable: true

release:
  draft: true
  replace_existing_draft: true
  make_latest: true
  mode: replace
  skip_upload: false
