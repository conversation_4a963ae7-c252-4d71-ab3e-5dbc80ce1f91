// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/inbox_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status enumeration for inbox notifications.
type Inbox_Status int32

const (
	// Unspecified status.
	Inbox_STATUS_UNSPECIFIED Inbox_Status = 0
	// The notification is unread.
	Inbox_UNREAD Inbox_Status = 1
	// The notification is archived.
	Inbox_ARCHIVED Inbox_Status = 2
)

// Enum value maps for Inbox_Status.
var (
	Inbox_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "UNREAD",
		2: "ARCHIVED",
	}
	Inbox_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"UNREAD":             1,
		"ARCHIVED":           2,
	}
)

func (x Inbox_Status) Enum() *Inbox_Status {
	p := new(Inbox_Status)
	*p = x
	return p
}

func (x Inbox_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Inbox_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_inbox_service_proto_enumTypes[0].Descriptor()
}

func (Inbox_Status) Type() protoreflect.EnumType {
	return &file_api_v1_inbox_service_proto_enumTypes[0]
}

func (x Inbox_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Inbox_Status.Descriptor instead.
func (Inbox_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{0, 0}
}

// Type enumeration for inbox notifications.
type Inbox_Type int32

const (
	// Unspecified type.
	Inbox_TYPE_UNSPECIFIED Inbox_Type = 0
	// Memo comment notification.
	Inbox_MEMO_COMMENT Inbox_Type = 1
	// Version update notification.
	Inbox_VERSION_UPDATE Inbox_Type = 2
)

// Enum value maps for Inbox_Type.
var (
	Inbox_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MEMO_COMMENT",
		2: "VERSION_UPDATE",
	}
	Inbox_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MEMO_COMMENT":     1,
		"VERSION_UPDATE":   2,
	}
)

func (x Inbox_Type) Enum() *Inbox_Type {
	p := new(Inbox_Type)
	*p = x
	return p
}

func (x Inbox_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Inbox_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_inbox_service_proto_enumTypes[1].Descriptor()
}

func (Inbox_Type) Type() protoreflect.EnumType {
	return &file_api_v1_inbox_service_proto_enumTypes[1]
}

func (x Inbox_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Inbox_Type.Descriptor instead.
func (Inbox_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{0, 1}
}

type Inbox struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the inbox.
	// Format: inboxes/{inbox}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The sender of the inbox notification.
	// Format: users/{user}
	Sender string `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	// The receiver of the inbox notification.
	// Format: users/{user}
	Receiver string `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// The status of the inbox notification.
	Status Inbox_Status `protobuf:"varint,4,opt,name=status,proto3,enum=memos.api.v1.Inbox_Status" json:"status,omitempty"`
	// Output only. The creation timestamp.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The type of the inbox notification.
	Type Inbox_Type `protobuf:"varint,6,opt,name=type,proto3,enum=memos.api.v1.Inbox_Type" json:"type,omitempty"`
	// Optional. The activity ID associated with this inbox notification.
	ActivityId    *int32 `protobuf:"varint,7,opt,name=activity_id,json=activityId,proto3,oneof" json:"activity_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Inbox) Reset() {
	*x = Inbox{}
	mi := &file_api_v1_inbox_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Inbox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inbox) ProtoMessage() {}

func (x *Inbox) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_inbox_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inbox.ProtoReflect.Descriptor instead.
func (*Inbox) Descriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{0}
}

func (x *Inbox) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Inbox) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *Inbox) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *Inbox) GetStatus() Inbox_Status {
	if x != nil {
		return x.Status
	}
	return Inbox_STATUS_UNSPECIFIED
}

func (x *Inbox) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Inbox) GetType() Inbox_Type {
	if x != nil {
		return x.Type
	}
	return Inbox_TYPE_UNSPECIFIED
}

func (x *Inbox) GetActivityId() int32 {
	if x != nil && x.ActivityId != nil {
		return *x.ActivityId
	}
	return 0
}

type ListInboxesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource whose inboxes will be listed.
	// Format: users/{user}
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. The maximum number of inboxes to return.
	// The service may return fewer than this value.
	// If unspecified, at most 50 inboxes will be returned.
	// The maximum value is 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. A page token, received from a previous `ListInboxes` call.
	// Provide this to retrieve the subsequent page.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. Filter to apply to the list results.
	// Example: "status=UNREAD" or "type=MEMO_COMMENT"
	// Supported operators: =, !=
	// Supported fields: status, type, sender, create_time
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. The order to sort results by.
	// Example: "create_time desc" or "status asc"
	OrderBy       string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListInboxesRequest) Reset() {
	*x = ListInboxesRequest{}
	mi := &file_api_v1_inbox_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListInboxesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInboxesRequest) ProtoMessage() {}

func (x *ListInboxesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_inbox_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInboxesRequest.ProtoReflect.Descriptor instead.
func (*ListInboxesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListInboxesRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListInboxesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListInboxesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListInboxesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListInboxesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListInboxesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of inboxes.
	Inboxes []*Inbox `protobuf:"bytes,1,rep,name=inboxes,proto3" json:"inboxes,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// The total count of inboxes (may be approximate).
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListInboxesResponse) Reset() {
	*x = ListInboxesResponse{}
	mi := &file_api_v1_inbox_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListInboxesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInboxesResponse) ProtoMessage() {}

func (x *ListInboxesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_inbox_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInboxesResponse.ProtoReflect.Descriptor instead.
func (*ListInboxesResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListInboxesResponse) GetInboxes() []*Inbox {
	if x != nil {
		return x.Inboxes
	}
	return nil
}

func (x *ListInboxesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListInboxesResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type UpdateInboxRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The inbox to update.
	Inbox *Inbox `protobuf:"bytes,1,opt,name=inbox,proto3" json:"inbox,omitempty"`
	// Required. The list of fields to update.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	// Optional. If set to true, allows updating missing fields.
	AllowMissing  bool `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3" json:"allow_missing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateInboxRequest) Reset() {
	*x = UpdateInboxRequest{}
	mi := &file_api_v1_inbox_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateInboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInboxRequest) ProtoMessage() {}

func (x *UpdateInboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_inbox_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInboxRequest.ProtoReflect.Descriptor instead.
func (*UpdateInboxRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateInboxRequest) GetInbox() *Inbox {
	if x != nil {
		return x.Inbox
	}
	return nil
}

func (x *UpdateInboxRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateInboxRequest) GetAllowMissing() bool {
	if x != nil {
		return x.AllowMissing
	}
	return false
}

type DeleteInboxRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the inbox to delete.
	// Format: inboxes/{inbox}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteInboxRequest) Reset() {
	*x = DeleteInboxRequest{}
	mi := &file_api_v1_inbox_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteInboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInboxRequest) ProtoMessage() {}

func (x *DeleteInboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_inbox_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInboxRequest.ProtoReflect.Descriptor instead.
func (*DeleteInboxRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_inbox_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteInboxRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_inbox_service_proto protoreflect.FileDescriptor

const file_api_v1_inbox_service_proto_rawDesc = "" +
	"\n" +
	"\x1aapi/v1/inbox_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x87\x04\n" +
	"\x05Inbox\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12\x1b\n" +
	"\x06sender\x18\x02 \x01(\tB\x03\xe0A\x03R\x06sender\x12\x1f\n" +
	"\breceiver\x18\x03 \x01(\tB\x03\xe0A\x03R\breceiver\x127\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1a.memos.api.v1.Inbox.StatusB\x03\xe0A\x01R\x06status\x12@\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x121\n" +
	"\x04type\x18\x06 \x01(\x0e2\x18.memos.api.v1.Inbox.TypeB\x03\xe0A\x03R\x04type\x12)\n" +
	"\vactivity_id\x18\a \x01(\x05B\x03\xe0A\x01H\x00R\n" +
	"activityId\x88\x01\x01\":\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06UNREAD\x10\x01\x12\f\n" +
	"\bARCHIVED\x10\x02\"B\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fMEMO_COMMENT\x10\x01\x12\x12\n" +
	"\x0eVERSION_UPDATE\x10\x02:>\xeaA;\n" +
	"\x12memos.api.v1/Inbox\x12\x0finboxes/{inbox}\x1a\x04name*\ainboxes2\x05inboxB\x0e\n" +
	"\f_activity_id\"\xca\x01\n" +
	"\x12ListInboxesRequest\x121\n" +
	"\x06parent\x18\x01 \x01(\tB\x19\xe0A\x02\xfaA\x13\n" +
	"\x11memos.api.v1/UserR\x06parent\x12 \n" +
	"\tpage_size\x18\x02 \x01(\x05B\x03\xe0A\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tB\x03\xe0A\x01R\tpageToken\x12\x1b\n" +
	"\x06filter\x18\x04 \x01(\tB\x03\xe0A\x01R\x06filter\x12\x1e\n" +
	"\border_by\x18\x05 \x01(\tB\x03\xe0A\x01R\aorderBy\"\x8b\x01\n" +
	"\x13ListInboxesResponse\x12-\n" +
	"\ainboxes\x18\x01 \x03(\v2\x13.memos.api.v1.InboxR\ainboxes\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSize\"\xb0\x01\n" +
	"\x12UpdateInboxRequest\x12.\n" +
	"\x05inbox\x18\x01 \x01(\v2\x13.memos.api.v1.InboxB\x03\xe0A\x02R\x05inbox\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x02R\n" +
	"updateMask\x12(\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x03\xe0A\x01R\fallowMissing\"D\n" +
	"\x12DeleteInboxRequest\x12.\n" +
	"\x04name\x18\x01 \x01(\tB\x1a\xe0A\x02\xfaA\x14\n" +
	"\x12memos.api.v1/InboxR\x04name2\x92\x03\n" +
	"\fInboxService\x12\x85\x01\n" +
	"\vListInboxes\x12 .memos.api.v1.ListInboxesRequest\x1a!.memos.api.v1.ListInboxesResponse\"1\xdaA\x06parent\x82\xd3\xe4\x93\x02\"\x12 /api/v1/{parent=users/*}/inboxes\x12\x87\x01\n" +
	"\vUpdateInbox\x12 .memos.api.v1.UpdateInboxRequest\x1a\x13.memos.api.v1.Inbox\"A\xdaA\x11inbox,update_mask\x82\xd3\xe4\x93\x02':\x05inbox2\x1e/api/v1/{inbox.name=inboxes/*}\x12p\n" +
	"\vDeleteInbox\x12 .memos.api.v1.DeleteInboxRequest\x1a\x16.google.protobuf.Empty\"'\xdaA\x04name\x82\xd3\xe4\x93\x02\x1a*\x18/api/v1/{name=inboxes/*}B\xa9\x01\n" +
	"\x10com.memos.api.v1B\x11InboxServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_inbox_service_proto_rawDescOnce sync.Once
	file_api_v1_inbox_service_proto_rawDescData []byte
)

func file_api_v1_inbox_service_proto_rawDescGZIP() []byte {
	file_api_v1_inbox_service_proto_rawDescOnce.Do(func() {
		file_api_v1_inbox_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_inbox_service_proto_rawDesc), len(file_api_v1_inbox_service_proto_rawDesc)))
	})
	return file_api_v1_inbox_service_proto_rawDescData
}

var file_api_v1_inbox_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_v1_inbox_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_v1_inbox_service_proto_goTypes = []any{
	(Inbox_Status)(0),             // 0: memos.api.v1.Inbox.Status
	(Inbox_Type)(0),               // 1: memos.api.v1.Inbox.Type
	(*Inbox)(nil),                 // 2: memos.api.v1.Inbox
	(*ListInboxesRequest)(nil),    // 3: memos.api.v1.ListInboxesRequest
	(*ListInboxesResponse)(nil),   // 4: memos.api.v1.ListInboxesResponse
	(*UpdateInboxRequest)(nil),    // 5: memos.api.v1.UpdateInboxRequest
	(*DeleteInboxRequest)(nil),    // 6: memos.api.v1.DeleteInboxRequest
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 8: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),         // 9: google.protobuf.Empty
}
var file_api_v1_inbox_service_proto_depIdxs = []int32{
	0, // 0: memos.api.v1.Inbox.status:type_name -> memos.api.v1.Inbox.Status
	7, // 1: memos.api.v1.Inbox.create_time:type_name -> google.protobuf.Timestamp
	1, // 2: memos.api.v1.Inbox.type:type_name -> memos.api.v1.Inbox.Type
	2, // 3: memos.api.v1.ListInboxesResponse.inboxes:type_name -> memos.api.v1.Inbox
	2, // 4: memos.api.v1.UpdateInboxRequest.inbox:type_name -> memos.api.v1.Inbox
	8, // 5: memos.api.v1.UpdateInboxRequest.update_mask:type_name -> google.protobuf.FieldMask
	3, // 6: memos.api.v1.InboxService.ListInboxes:input_type -> memos.api.v1.ListInboxesRequest
	5, // 7: memos.api.v1.InboxService.UpdateInbox:input_type -> memos.api.v1.UpdateInboxRequest
	6, // 8: memos.api.v1.InboxService.DeleteInbox:input_type -> memos.api.v1.DeleteInboxRequest
	4, // 9: memos.api.v1.InboxService.ListInboxes:output_type -> memos.api.v1.ListInboxesResponse
	2, // 10: memos.api.v1.InboxService.UpdateInbox:output_type -> memos.api.v1.Inbox
	9, // 11: memos.api.v1.InboxService.DeleteInbox:output_type -> google.protobuf.Empty
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_v1_inbox_service_proto_init() }
func file_api_v1_inbox_service_proto_init() {
	if File_api_v1_inbox_service_proto != nil {
		return
	}
	file_api_v1_inbox_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_inbox_service_proto_rawDesc), len(file_api_v1_inbox_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_inbox_service_proto_goTypes,
		DependencyIndexes: file_api_v1_inbox_service_proto_depIdxs,
		EnumInfos:         file_api_v1_inbox_service_proto_enumTypes,
		MessageInfos:      file_api_v1_inbox_service_proto_msgTypes,
	}.Build()
	File_api_v1_inbox_service_proto = out.File
	file_api_v1_inbox_service_proto_goTypes = nil
	file_api_v1_inbox_service_proto_depIdxs = nil
}
