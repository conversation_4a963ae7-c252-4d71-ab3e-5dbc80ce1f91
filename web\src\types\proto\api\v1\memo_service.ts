// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/memo_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";
import { Attachment } from "./attachment_service";
import { State, stateFromJSON, stateToNumber } from "./common";
import { Node } from "./markdown_service";

export const protobufPackage = "memos.api.v1";

export enum Visibility {
  VISIBILITY_UNSPECIFIED = "VISIBILITY_UNSPECIFIED",
  PRIVATE = "PRIVATE",
  PROTECTED = "PROTECTED",
  PUBLIC = "PUBLIC",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function visibilityFromJSON(object: any): Visibility {
  switch (object) {
    case 0:
    case "VISIBILITY_UNSPECIFIED":
      return Visibility.VISIBILITY_UNSPECIFIED;
    case 1:
    case "PRIVATE":
      return Visibility.PRIVATE;
    case 2:
    case "PROTECTED":
      return Visibility.PROTECTED;
    case 3:
    case "PUBLIC":
      return Visibility.PUBLIC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Visibility.UNRECOGNIZED;
  }
}

export function visibilityToNumber(object: Visibility): number {
  switch (object) {
    case Visibility.VISIBILITY_UNSPECIFIED:
      return 0;
    case Visibility.PRIVATE:
      return 1;
    case Visibility.PROTECTED:
      return 2;
    case Visibility.PUBLIC:
      return 3;
    case Visibility.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface Reaction {
  /**
   * The resource name of the reaction.
   * Format: reactions/{reaction}
   */
  name: string;
  /**
   * The resource name of the creator.
   * Format: users/{user}
   */
  creator: string;
  /**
   * The resource name of the content.
   * For memo reactions, this should be the memo's resource name.
   * Format: memos/{memo}
   */
  contentId: string;
  /** Required. The type of reaction (e.g., "👍", "❤️", "😄"). */
  reactionType: string;
  /** Output only. The creation timestamp. */
  createTime?: Date | undefined;
}

export interface Memo {
  /**
   * The resource name of the memo.
   * Format: memos/{memo}, memo is the user defined id or uuid.
   */
  name: string;
  /** The state of the memo. */
  state: State;
  /**
   * The name of the creator.
   * Format: users/{user}
   */
  creator: string;
  /** Output only. The creation timestamp. */
  createTime?:
    | Date
    | undefined;
  /** Output only. The last update timestamp. */
  updateTime?:
    | Date
    | undefined;
  /** The display timestamp of the memo. */
  displayTime?:
    | Date
    | undefined;
  /** Required. The content of the memo in Markdown format. */
  content: string;
  /** Output only. The parsed nodes from the content. */
  nodes: Node[];
  /** The visibility of the memo. */
  visibility: Visibility;
  /** Output only. The tags extracted from the content. */
  tags: string[];
  /** Whether the memo is pinned. */
  pinned: boolean;
  /** Optional. The attachments of the memo. */
  attachments: Attachment[];
  /** Optional. The relations of the memo. */
  relations: MemoRelation[];
  /** Output only. The reactions to the memo. */
  reactions: Reaction[];
  /** Output only. The computed properties of the memo. */
  property?:
    | Memo_Property
    | undefined;
  /**
   * Output only. The name of the parent memo.
   * Format: memos/{memo}
   */
  parent?:
    | string
    | undefined;
  /** Output only. The snippet of the memo content. Plain text only. */
  snippet: string;
  /** Optional. The location of the memo. */
  location?: Location | undefined;
}

/** Computed properties of a memo. */
export interface Memo_Property {
  hasLink: boolean;
  hasTaskList: boolean;
  hasCode: boolean;
  hasIncompleteTasks: boolean;
}

export interface Location {
  /** A placeholder text for the location. */
  placeholder: string;
  /** The latitude of the location. */
  latitude: number;
  /** The longitude of the location. */
  longitude: number;
}

export interface CreateMemoRequest {
  /** Required. The memo to create. */
  memo?:
    | Memo
    | undefined;
  /**
   * Optional. The memo ID to use for this memo.
   * If empty, a unique ID will be generated.
   */
  memoId: string;
  /** Optional. If set, validate the request but don't actually create the memo. */
  validateOnly: boolean;
  /** Optional. An idempotency token. */
  requestId: string;
}

export interface ListMemosRequest {
  /**
   * Optional. The parent is the owner of the memos.
   * If not specified or `users/-`, it will list all memos.
   * Format: users/{user}
   */
  parent: string;
  /**
   * Optional. The maximum number of memos to return.
   * The service may return fewer than this value.
   * If unspecified, at most 50 memos will be returned.
   * The maximum value is 1000; values above 1000 will be coerced to 1000.
   */
  pageSize: number;
  /**
   * Optional. A page token, received from a previous `ListMemos` call.
   * Provide this to retrieve the subsequent page.
   */
  pageToken: string;
  /**
   * Optional. The state of the memos to list.
   * Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
   */
  state: State;
  /**
   * Optional. The order to sort results by.
   * Default to "display_time desc".
   * Example: "display_time desc" or "create_time asc"
   */
  orderBy: string;
  /**
   * Optional. Filter to apply to the list results.
   * Filter is a CEL expression to filter memos.
   * Refer to `Shortcut.filter`.
   */
  filter: string;
  /** Optional. If true, show deleted memos in the response. */
  showDeleted: boolean;
  /**
   * [Deprecated] Old filter contains some specific conditions to filter memos.
   * Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
   */
  oldFilter: string;
}

export interface ListMemosResponse {
  /** The list of memos. */
  memos: Memo[];
  /**
   * A token that can be sent as `page_token` to retrieve the next page.
   * If this field is omitted, there are no subsequent pages.
   */
  nextPageToken: string;
  /** The total count of memos (may be approximate). */
  totalSize: number;
}

export interface GetMemoRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /**
   * Optional. The fields to return in the response.
   * If not specified, all fields are returned.
   */
  readMask?: string[] | undefined;
}

export interface UpdateMemoRequest {
  /**
   * Required. The memo to update.
   * The `name` field is required.
   */
  memo?:
    | Memo
    | undefined;
  /** Required. The list of fields to update. */
  updateMask?:
    | string[]
    | undefined;
  /** Optional. If set to true, allows updating sensitive fields. */
  allowMissing: boolean;
}

export interface DeleteMemoRequest {
  /**
   * Required. The resource name of the memo to delete.
   * Format: memos/{memo}
   */
  name: string;
  /** Optional. If set to true, the memo will be deleted even if it has associated data. */
  force: boolean;
}

export interface RenameMemoTagRequest {
  /**
   * Required. The parent, who owns the tags.
   * Format: memos/{memo}. Use "memos/-" to rename all tags.
   */
  parent: string;
  /** Required. The old tag name to rename. */
  oldTag: string;
  /** Required. The new tag name. */
  newTag: string;
}

export interface DeleteMemoTagRequest {
  /**
   * Required. The parent, who owns the tags.
   * Format: memos/{memo}. Use "memos/-" to delete all tags.
   */
  parent: string;
  /** Required. The tag name to delete. */
  tag: string;
  /** Optional. Whether to delete related memos. */
  deleteRelatedMemos: boolean;
}

export interface SetMemoAttachmentsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Required. The attachments to set for the memo. */
  attachments: Attachment[];
}

export interface ListMemoAttachmentsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Optional. The maximum number of attachments to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface ListMemoAttachmentsResponse {
  /** The list of attachments. */
  attachments: Attachment[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of attachments. */
  totalSize: number;
}

export interface MemoRelation {
  /** The memo in the relation. */
  memo?:
    | MemoRelation_Memo
    | undefined;
  /** The related memo. */
  relatedMemo?: MemoRelation_Memo | undefined;
  type: MemoRelation_Type;
}

/** The type of the relation. */
export enum MemoRelation_Type {
  TYPE_UNSPECIFIED = "TYPE_UNSPECIFIED",
  REFERENCE = "REFERENCE",
  COMMENT = "COMMENT",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function memoRelation_TypeFromJSON(object: any): MemoRelation_Type {
  switch (object) {
    case 0:
    case "TYPE_UNSPECIFIED":
      return MemoRelation_Type.TYPE_UNSPECIFIED;
    case 1:
    case "REFERENCE":
      return MemoRelation_Type.REFERENCE;
    case 2:
    case "COMMENT":
      return MemoRelation_Type.COMMENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MemoRelation_Type.UNRECOGNIZED;
  }
}

export function memoRelation_TypeToNumber(object: MemoRelation_Type): number {
  switch (object) {
    case MemoRelation_Type.TYPE_UNSPECIFIED:
      return 0;
    case MemoRelation_Type.REFERENCE:
      return 1;
    case MemoRelation_Type.COMMENT:
      return 2;
    case MemoRelation_Type.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Memo reference in relations. */
export interface MemoRelation_Memo {
  /**
   * The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Output only. The snippet of the memo content. Plain text only. */
  snippet: string;
}

export interface SetMemoRelationsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Required. The relations to set for the memo. */
  relations: MemoRelation[];
}

export interface ListMemoRelationsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Optional. The maximum number of relations to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface ListMemoRelationsResponse {
  /** The list of relations. */
  relations: MemoRelation[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of relations. */
  totalSize: number;
}

export interface CreateMemoCommentRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Required. The comment to create. */
  comment?:
    | Memo
    | undefined;
  /** Optional. The comment ID to use. */
  commentId: string;
}

export interface ListMemoCommentsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Optional. The maximum number of comments to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
  /** Optional. The order to sort results by. */
  orderBy: string;
}

export interface ListMemoCommentsResponse {
  /** The list of comment memos. */
  memos: Memo[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of comments. */
  totalSize: number;
}

export interface ListMemoReactionsRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Optional. The maximum number of reactions to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface ListMemoReactionsResponse {
  /** The list of reactions. */
  reactions: Reaction[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of reactions. */
  totalSize: number;
}

export interface UpsertMemoReactionRequest {
  /**
   * Required. The resource name of the memo.
   * Format: memos/{memo}
   */
  name: string;
  /** Required. The reaction to upsert. */
  reaction?: Reaction | undefined;
}

export interface DeleteMemoReactionRequest {
  /**
   * Required. The resource name of the reaction to delete.
   * Format: reactions/{reaction}
   */
  name: string;
}

function createBaseReaction(): Reaction {
  return { name: "", creator: "", contentId: "", reactionType: "", createTime: undefined };
}

export const Reaction: MessageFns<Reaction> = {
  encode(message: Reaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.creator !== "") {
      writer.uint32(18).string(message.creator);
    }
    if (message.contentId !== "") {
      writer.uint32(26).string(message.contentId);
    }
    if (message.reactionType !== "") {
      writer.uint32(34).string(message.reactionType);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Reaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.contentId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.reactionType = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Reaction>): Reaction {
    return Reaction.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Reaction>): Reaction {
    const message = createBaseReaction();
    message.name = object.name ?? "";
    message.creator = object.creator ?? "";
    message.contentId = object.contentId ?? "";
    message.reactionType = object.reactionType ?? "";
    message.createTime = object.createTime ?? undefined;
    return message;
  },
};

function createBaseMemo(): Memo {
  return {
    name: "",
    state: State.STATE_UNSPECIFIED,
    creator: "",
    createTime: undefined,
    updateTime: undefined,
    displayTime: undefined,
    content: "",
    nodes: [],
    visibility: Visibility.VISIBILITY_UNSPECIFIED,
    tags: [],
    pinned: false,
    attachments: [],
    relations: [],
    reactions: [],
    property: undefined,
    parent: undefined,
    snippet: "",
    location: undefined,
  };
}

export const Memo: MessageFns<Memo> = {
  encode(message: Memo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(16).int32(stateToNumber(message.state));
    }
    if (message.creator !== "") {
      writer.uint32(26).string(message.creator);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(34).fork()).join();
    }
    if (message.updateTime !== undefined) {
      Timestamp.encode(toTimestamp(message.updateTime), writer.uint32(42).fork()).join();
    }
    if (message.displayTime !== undefined) {
      Timestamp.encode(toTimestamp(message.displayTime), writer.uint32(50).fork()).join();
    }
    if (message.content !== "") {
      writer.uint32(58).string(message.content);
    }
    for (const v of message.nodes) {
      Node.encode(v!, writer.uint32(66).fork()).join();
    }
    if (message.visibility !== Visibility.VISIBILITY_UNSPECIFIED) {
      writer.uint32(72).int32(visibilityToNumber(message.visibility));
    }
    for (const v of message.tags) {
      writer.uint32(82).string(v!);
    }
    if (message.pinned !== false) {
      writer.uint32(88).bool(message.pinned);
    }
    for (const v of message.attachments) {
      Attachment.encode(v!, writer.uint32(98).fork()).join();
    }
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(106).fork()).join();
    }
    for (const v of message.reactions) {
      Reaction.encode(v!, writer.uint32(114).fork()).join();
    }
    if (message.property !== undefined) {
      Memo_Property.encode(message.property, writer.uint32(122).fork()).join();
    }
    if (message.parent !== undefined) {
      writer.uint32(130).string(message.parent);
    }
    if (message.snippet !== "") {
      writer.uint32(138).string(message.snippet);
    }
    if (message.location !== undefined) {
      Location.encode(message.location, writer.uint32(146).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Memo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.updateTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.displayTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.nodes.push(Node.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.visibility = visibilityFromJSON(reader.int32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.pinned = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.attachments.push(Attachment.decode(reader, reader.uint32()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.reactions.push(Reaction.decode(reader, reader.uint32()));
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.property = Memo_Property.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.snippet = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.location = Location.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Memo>): Memo {
    return Memo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Memo>): Memo {
    const message = createBaseMemo();
    message.name = object.name ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.creator = object.creator ?? "";
    message.createTime = object.createTime ?? undefined;
    message.updateTime = object.updateTime ?? undefined;
    message.displayTime = object.displayTime ?? undefined;
    message.content = object.content ?? "";
    message.nodes = object.nodes?.map((e) => Node.fromPartial(e)) || [];
    message.visibility = object.visibility ?? Visibility.VISIBILITY_UNSPECIFIED;
    message.tags = object.tags?.map((e) => e) || [];
    message.pinned = object.pinned ?? false;
    message.attachments = object.attachments?.map((e) => Attachment.fromPartial(e)) || [];
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    message.reactions = object.reactions?.map((e) => Reaction.fromPartial(e)) || [];
    message.property = (object.property !== undefined && object.property !== null)
      ? Memo_Property.fromPartial(object.property)
      : undefined;
    message.parent = object.parent ?? undefined;
    message.snippet = object.snippet ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? Location.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseMemo_Property(): Memo_Property {
  return { hasLink: false, hasTaskList: false, hasCode: false, hasIncompleteTasks: false };
}

export const Memo_Property: MessageFns<Memo_Property> = {
  encode(message: Memo_Property, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasLink !== false) {
      writer.uint32(8).bool(message.hasLink);
    }
    if (message.hasTaskList !== false) {
      writer.uint32(16).bool(message.hasTaskList);
    }
    if (message.hasCode !== false) {
      writer.uint32(24).bool(message.hasCode);
    }
    if (message.hasIncompleteTasks !== false) {
      writer.uint32(32).bool(message.hasIncompleteTasks);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Memo_Property {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemo_Property();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasLink = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasTaskList = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.hasCode = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.hasIncompleteTasks = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Memo_Property>): Memo_Property {
    return Memo_Property.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Memo_Property>): Memo_Property {
    const message = createBaseMemo_Property();
    message.hasLink = object.hasLink ?? false;
    message.hasTaskList = object.hasTaskList ?? false;
    message.hasCode = object.hasCode ?? false;
    message.hasIncompleteTasks = object.hasIncompleteTasks ?? false;
    return message;
  },
};

function createBaseLocation(): Location {
  return { placeholder: "", latitude: 0, longitude: 0 };
}

export const Location: MessageFns<Location> = {
  encode(message: Location, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.placeholder !== "") {
      writer.uint32(10).string(message.placeholder);
    }
    if (message.latitude !== 0) {
      writer.uint32(17).double(message.latitude);
    }
    if (message.longitude !== 0) {
      writer.uint32(25).double(message.longitude);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Location {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.placeholder = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.latitude = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.longitude = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Location>): Location {
    return Location.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Location>): Location {
    const message = createBaseLocation();
    message.placeholder = object.placeholder ?? "";
    message.latitude = object.latitude ?? 0;
    message.longitude = object.longitude ?? 0;
    return message;
  },
};

function createBaseCreateMemoRequest(): CreateMemoRequest {
  return { memo: undefined, memoId: "", validateOnly: false, requestId: "" };
}

export const CreateMemoRequest: MessageFns<CreateMemoRequest> = {
  encode(message: CreateMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    if (message.memoId !== "") {
      writer.uint32(18).string(message.memoId);
    }
    if (message.validateOnly !== false) {
      writer.uint32(24).bool(message.validateOnly);
    }
    if (message.requestId !== "") {
      writer.uint32(34).string(message.requestId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = Memo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memoId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.validateOnly = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateMemoRequest>): CreateMemoRequest {
    return CreateMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateMemoRequest>): CreateMemoRequest {
    const message = createBaseCreateMemoRequest();
    message.memo = (object.memo !== undefined && object.memo !== null) ? Memo.fromPartial(object.memo) : undefined;
    message.memoId = object.memoId ?? "";
    message.validateOnly = object.validateOnly ?? false;
    message.requestId = object.requestId ?? "";
    return message;
  },
};

function createBaseListMemosRequest(): ListMemosRequest {
  return {
    parent: "",
    pageSize: 0,
    pageToken: "",
    state: State.STATE_UNSPECIFIED,
    orderBy: "",
    filter: "",
    showDeleted: false,
    oldFilter: "",
  };
}

export const ListMemosRequest: MessageFns<ListMemosRequest> = {
  encode(message: ListMemosRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(32).int32(stateToNumber(message.state));
    }
    if (message.orderBy !== "") {
      writer.uint32(42).string(message.orderBy);
    }
    if (message.filter !== "") {
      writer.uint32(50).string(message.filter);
    }
    if (message.showDeleted !== false) {
      writer.uint32(56).bool(message.showDeleted);
    }
    if (message.oldFilter !== "") {
      writer.uint32(66).string(message.oldFilter);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemosRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemosRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.orderBy = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.showDeleted = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.oldFilter = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemosRequest>): ListMemosRequest {
    return ListMemosRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemosRequest>): ListMemosRequest {
    const message = createBaseListMemosRequest();
    message.parent = object.parent ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.orderBy = object.orderBy ?? "";
    message.filter = object.filter ?? "";
    message.showDeleted = object.showDeleted ?? false;
    message.oldFilter = object.oldFilter ?? "";
    return message;
  },
};

function createBaseListMemosResponse(): ListMemosResponse {
  return { memos: [], nextPageToken: "", totalSize: 0 };
}

export const ListMemosResponse: MessageFns<ListMemosResponse> = {
  encode(message: ListMemosResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.memos) {
      Memo.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemosResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemosResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memos.push(Memo.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemosResponse>): ListMemosResponse {
    return ListMemosResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemosResponse>): ListMemosResponse {
    const message = createBaseListMemosResponse();
    message.memos = object.memos?.map((e) => Memo.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseGetMemoRequest(): GetMemoRequest {
  return { name: "", readMask: undefined };
}

export const GetMemoRequest: MessageFns<GetMemoRequest> = {
  encode(message: GetMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.readMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.readMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetMemoRequest>): GetMemoRequest {
    return GetMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetMemoRequest>): GetMemoRequest {
    const message = createBaseGetMemoRequest();
    message.name = object.name ?? "";
    message.readMask = object.readMask ?? undefined;
    return message;
  },
};

function createBaseUpdateMemoRequest(): UpdateMemoRequest {
  return { memo: undefined, updateMask: undefined, allowMissing: false };
}

export const UpdateMemoRequest: MessageFns<UpdateMemoRequest> = {
  encode(message: UpdateMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    if (message.allowMissing !== false) {
      writer.uint32(24).bool(message.allowMissing);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = Memo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.allowMissing = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateMemoRequest>): UpdateMemoRequest {
    return UpdateMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateMemoRequest>): UpdateMemoRequest {
    const message = createBaseUpdateMemoRequest();
    message.memo = (object.memo !== undefined && object.memo !== null) ? Memo.fromPartial(object.memo) : undefined;
    message.updateMask = object.updateMask ?? undefined;
    message.allowMissing = object.allowMissing ?? false;
    return message;
  },
};

function createBaseDeleteMemoRequest(): DeleteMemoRequest {
  return { name: "", force: false };
}

export const DeleteMemoRequest: MessageFns<DeleteMemoRequest> = {
  encode(message: DeleteMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.force !== false) {
      writer.uint32(16).bool(message.force);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.force = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoRequest>): DeleteMemoRequest {
    return DeleteMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoRequest>): DeleteMemoRequest {
    const message = createBaseDeleteMemoRequest();
    message.name = object.name ?? "";
    message.force = object.force ?? false;
    return message;
  },
};

function createBaseRenameMemoTagRequest(): RenameMemoTagRequest {
  return { parent: "", oldTag: "", newTag: "" };
}

export const RenameMemoTagRequest: MessageFns<RenameMemoTagRequest> = {
  encode(message: RenameMemoTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.oldTag !== "") {
      writer.uint32(18).string(message.oldTag);
    }
    if (message.newTag !== "") {
      writer.uint32(26).string(message.newTag);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameMemoTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameMemoTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.oldTag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.newTag = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RenameMemoTagRequest>): RenameMemoTagRequest {
    return RenameMemoTagRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RenameMemoTagRequest>): RenameMemoTagRequest {
    const message = createBaseRenameMemoTagRequest();
    message.parent = object.parent ?? "";
    message.oldTag = object.oldTag ?? "";
    message.newTag = object.newTag ?? "";
    return message;
  },
};

function createBaseDeleteMemoTagRequest(): DeleteMemoTagRequest {
  return { parent: "", tag: "", deleteRelatedMemos: false };
}

export const DeleteMemoTagRequest: MessageFns<DeleteMemoTagRequest> = {
  encode(message: DeleteMemoTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.tag !== "") {
      writer.uint32(18).string(message.tag);
    }
    if (message.deleteRelatedMemos !== false) {
      writer.uint32(24).bool(message.deleteRelatedMemos);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.deleteRelatedMemos = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoTagRequest>): DeleteMemoTagRequest {
    return DeleteMemoTagRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoTagRequest>): DeleteMemoTagRequest {
    const message = createBaseDeleteMemoTagRequest();
    message.parent = object.parent ?? "";
    message.tag = object.tag ?? "";
    message.deleteRelatedMemos = object.deleteRelatedMemos ?? false;
    return message;
  },
};

function createBaseSetMemoAttachmentsRequest(): SetMemoAttachmentsRequest {
  return { name: "", attachments: [] };
}

export const SetMemoAttachmentsRequest: MessageFns<SetMemoAttachmentsRequest> = {
  encode(message: SetMemoAttachmentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.attachments) {
      Attachment.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetMemoAttachmentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetMemoAttachmentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.attachments.push(Attachment.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SetMemoAttachmentsRequest>): SetMemoAttachmentsRequest {
    return SetMemoAttachmentsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SetMemoAttachmentsRequest>): SetMemoAttachmentsRequest {
    const message = createBaseSetMemoAttachmentsRequest();
    message.name = object.name ?? "";
    message.attachments = object.attachments?.map((e) => Attachment.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListMemoAttachmentsRequest(): ListMemoAttachmentsRequest {
  return { name: "", pageSize: 0, pageToken: "" };
}

export const ListMemoAttachmentsRequest: MessageFns<ListMemoAttachmentsRequest> = {
  encode(message: ListMemoAttachmentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoAttachmentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoAttachmentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoAttachmentsRequest>): ListMemoAttachmentsRequest {
    return ListMemoAttachmentsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoAttachmentsRequest>): ListMemoAttachmentsRequest {
    const message = createBaseListMemoAttachmentsRequest();
    message.name = object.name ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListMemoAttachmentsResponse(): ListMemoAttachmentsResponse {
  return { attachments: [], nextPageToken: "", totalSize: 0 };
}

export const ListMemoAttachmentsResponse: MessageFns<ListMemoAttachmentsResponse> = {
  encode(message: ListMemoAttachmentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.attachments) {
      Attachment.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoAttachmentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoAttachmentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.attachments.push(Attachment.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoAttachmentsResponse>): ListMemoAttachmentsResponse {
    return ListMemoAttachmentsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoAttachmentsResponse>): ListMemoAttachmentsResponse {
    const message = createBaseListMemoAttachmentsResponse();
    message.attachments = object.attachments?.map((e) => Attachment.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseMemoRelation(): MemoRelation {
  return { memo: undefined, relatedMemo: undefined, type: MemoRelation_Type.TYPE_UNSPECIFIED };
}

export const MemoRelation: MessageFns<MemoRelation> = {
  encode(message: MemoRelation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      MemoRelation_Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    if (message.relatedMemo !== undefined) {
      MemoRelation_Memo.encode(message.relatedMemo, writer.uint32(18).fork()).join();
    }
    if (message.type !== MemoRelation_Type.TYPE_UNSPECIFIED) {
      writer.uint32(24).int32(memoRelation_TypeToNumber(message.type));
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MemoRelation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemoRelation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = MemoRelation_Memo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relatedMemo = MemoRelation_Memo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = memoRelation_TypeFromJSON(reader.int32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MemoRelation>): MemoRelation {
    return MemoRelation.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MemoRelation>): MemoRelation {
    const message = createBaseMemoRelation();
    message.memo = (object.memo !== undefined && object.memo !== null)
      ? MemoRelation_Memo.fromPartial(object.memo)
      : undefined;
    message.relatedMemo = (object.relatedMemo !== undefined && object.relatedMemo !== null)
      ? MemoRelation_Memo.fromPartial(object.relatedMemo)
      : undefined;
    message.type = object.type ?? MemoRelation_Type.TYPE_UNSPECIFIED;
    return message;
  },
};

function createBaseMemoRelation_Memo(): MemoRelation_Memo {
  return { name: "", snippet: "" };
}

export const MemoRelation_Memo: MessageFns<MemoRelation_Memo> = {
  encode(message: MemoRelation_Memo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.snippet !== "") {
      writer.uint32(18).string(message.snippet);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MemoRelation_Memo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemoRelation_Memo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.snippet = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MemoRelation_Memo>): MemoRelation_Memo {
    return MemoRelation_Memo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MemoRelation_Memo>): MemoRelation_Memo {
    const message = createBaseMemoRelation_Memo();
    message.name = object.name ?? "";
    message.snippet = object.snippet ?? "";
    return message;
  },
};

function createBaseSetMemoRelationsRequest(): SetMemoRelationsRequest {
  return { name: "", relations: [] };
}

export const SetMemoRelationsRequest: MessageFns<SetMemoRelationsRequest> = {
  encode(message: SetMemoRelationsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetMemoRelationsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetMemoRelationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SetMemoRelationsRequest>): SetMemoRelationsRequest {
    return SetMemoRelationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SetMemoRelationsRequest>): SetMemoRelationsRequest {
    const message = createBaseSetMemoRelationsRequest();
    message.name = object.name ?? "";
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListMemoRelationsRequest(): ListMemoRelationsRequest {
  return { name: "", pageSize: 0, pageToken: "" };
}

export const ListMemoRelationsRequest: MessageFns<ListMemoRelationsRequest> = {
  encode(message: ListMemoRelationsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoRelationsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoRelationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoRelationsRequest>): ListMemoRelationsRequest {
    return ListMemoRelationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoRelationsRequest>): ListMemoRelationsRequest {
    const message = createBaseListMemoRelationsRequest();
    message.name = object.name ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListMemoRelationsResponse(): ListMemoRelationsResponse {
  return { relations: [], nextPageToken: "", totalSize: 0 };
}

export const ListMemoRelationsResponse: MessageFns<ListMemoRelationsResponse> = {
  encode(message: ListMemoRelationsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoRelationsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoRelationsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoRelationsResponse>): ListMemoRelationsResponse {
    return ListMemoRelationsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoRelationsResponse>): ListMemoRelationsResponse {
    const message = createBaseListMemoRelationsResponse();
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseCreateMemoCommentRequest(): CreateMemoCommentRequest {
  return { name: "", comment: undefined, commentId: "" };
}

export const CreateMemoCommentRequest: MessageFns<CreateMemoCommentRequest> = {
  encode(message: CreateMemoCommentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.comment !== undefined) {
      Memo.encode(message.comment, writer.uint32(18).fork()).join();
    }
    if (message.commentId !== "") {
      writer.uint32(26).string(message.commentId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMemoCommentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMemoCommentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.comment = Memo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.commentId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateMemoCommentRequest>): CreateMemoCommentRequest {
    return CreateMemoCommentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateMemoCommentRequest>): CreateMemoCommentRequest {
    const message = createBaseCreateMemoCommentRequest();
    message.name = object.name ?? "";
    message.comment = (object.comment !== undefined && object.comment !== null)
      ? Memo.fromPartial(object.comment)
      : undefined;
    message.commentId = object.commentId ?? "";
    return message;
  },
};

function createBaseListMemoCommentsRequest(): ListMemoCommentsRequest {
  return { name: "", pageSize: 0, pageToken: "", orderBy: "" };
}

export const ListMemoCommentsRequest: MessageFns<ListMemoCommentsRequest> = {
  encode(message: ListMemoCommentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    if (message.orderBy !== "") {
      writer.uint32(34).string(message.orderBy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoCommentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoCommentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.orderBy = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoCommentsRequest>): ListMemoCommentsRequest {
    return ListMemoCommentsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoCommentsRequest>): ListMemoCommentsRequest {
    const message = createBaseListMemoCommentsRequest();
    message.name = object.name ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.orderBy = object.orderBy ?? "";
    return message;
  },
};

function createBaseListMemoCommentsResponse(): ListMemoCommentsResponse {
  return { memos: [], nextPageToken: "", totalSize: 0 };
}

export const ListMemoCommentsResponse: MessageFns<ListMemoCommentsResponse> = {
  encode(message: ListMemoCommentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.memos) {
      Memo.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoCommentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoCommentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memos.push(Memo.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoCommentsResponse>): ListMemoCommentsResponse {
    return ListMemoCommentsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoCommentsResponse>): ListMemoCommentsResponse {
    const message = createBaseListMemoCommentsResponse();
    message.memos = object.memos?.map((e) => Memo.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseListMemoReactionsRequest(): ListMemoReactionsRequest {
  return { name: "", pageSize: 0, pageToken: "" };
}

export const ListMemoReactionsRequest: MessageFns<ListMemoReactionsRequest> = {
  encode(message: ListMemoReactionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoReactionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoReactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoReactionsRequest>): ListMemoReactionsRequest {
    return ListMemoReactionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoReactionsRequest>): ListMemoReactionsRequest {
    const message = createBaseListMemoReactionsRequest();
    message.name = object.name ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListMemoReactionsResponse(): ListMemoReactionsResponse {
  return { reactions: [], nextPageToken: "", totalSize: 0 };
}

export const ListMemoReactionsResponse: MessageFns<ListMemoReactionsResponse> = {
  encode(message: ListMemoReactionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.reactions) {
      Reaction.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoReactionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoReactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.reactions.push(Reaction.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoReactionsResponse>): ListMemoReactionsResponse {
    return ListMemoReactionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoReactionsResponse>): ListMemoReactionsResponse {
    const message = createBaseListMemoReactionsResponse();
    message.reactions = object.reactions?.map((e) => Reaction.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseUpsertMemoReactionRequest(): UpsertMemoReactionRequest {
  return { name: "", reaction: undefined };
}

export const UpsertMemoReactionRequest: MessageFns<UpsertMemoReactionRequest> = {
  encode(message: UpsertMemoReactionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.reaction !== undefined) {
      Reaction.encode(message.reaction, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpsertMemoReactionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpsertMemoReactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reaction = Reaction.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpsertMemoReactionRequest>): UpsertMemoReactionRequest {
    return UpsertMemoReactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpsertMemoReactionRequest>): UpsertMemoReactionRequest {
    const message = createBaseUpsertMemoReactionRequest();
    message.name = object.name ?? "";
    message.reaction = (object.reaction !== undefined && object.reaction !== null)
      ? Reaction.fromPartial(object.reaction)
      : undefined;
    return message;
  },
};

function createBaseDeleteMemoReactionRequest(): DeleteMemoReactionRequest {
  return { name: "" };
}

export const DeleteMemoReactionRequest: MessageFns<DeleteMemoReactionRequest> = {
  encode(message: DeleteMemoReactionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoReactionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoReactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoReactionRequest>): DeleteMemoReactionRequest {
    return DeleteMemoReactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoReactionRequest>): DeleteMemoReactionRequest {
    const message = createBaseDeleteMemoReactionRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type MemoServiceDefinition = typeof MemoServiceDefinition;
export const MemoServiceDefinition = {
  name: "MemoService",
  fullName: "memos.api.v1.MemoService",
  methods: {
    /** CreateMemo creates a memo. */
    createMemo: {
      name: "CreateMemo",
      requestType: CreateMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 109, 101, 109, 111])],
          578365826: [
            new Uint8Array([
              21,
              58,
              4,
              109,
              101,
              109,
              111,
              34,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              101,
              109,
              111,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemos lists memos with pagination and filter. */
    listMemos: {
      name: "ListMemos",
      requestType: ListMemosRequest,
      requestStream: false,
      responseType: ListMemosResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([0]), new Uint8Array([6, 112, 97, 114, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              49,
              90,
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              109,
              101,
              109,
              111,
              115,
              18,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              101,
              109,
              111,
              115,
            ]),
          ],
        },
      },
    },
    /** GetMemo gets a memo. */
    getMemo: {
      name: "GetMemo",
      requestType: GetMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** UpdateMemo updates a memo. */
    updateMemo: {
      name: "UpdateMemo",
      requestType: UpdateMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([16, 109, 101, 109, 111, 44, 117, 112, 100, 97, 116, 101, 95, 109, 97, 115, 107])],
          578365826: [
            new Uint8Array([
              35,
              58,
              4,
              109,
              101,
              109,
              111,
              50,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              109,
              101,
              109,
              111,
              46,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteMemo deletes a memo. */
    deleteMemo: {
      name: "DeleteMemo",
      requestType: DeleteMemoRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              42,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** RenameMemoTag renames a tag for a memo. */
    renameMemoTag: {
      name: "RenameMemoTag",
      requestType: RenameMemoTagRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              22,
              112,
              97,
              114,
              101,
              110,
              116,
              44,
              111,
              108,
              100,
              95,
              116,
              97,
              103,
              44,
              110,
              101,
              119,
              95,
              116,
              97,
              103,
            ]),
          ],
          578365826: [
            new Uint8Array([
              41,
              58,
              1,
              42,
              50,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              116,
              97,
              103,
              115,
              58,
              114,
              101,
              110,
              97,
              109,
              101,
            ]),
          ],
        },
      },
    },
    /** DeleteMemoTag deletes a tag for a memo. */
    deleteMemoTag: {
      name: "DeleteMemoTag",
      requestType: DeleteMemoTagRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([10, 112, 97, 114, 101, 110, 116, 44, 116, 97, 103])],
          578365826: [
            new Uint8Array([
              37,
              42,
              35,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              116,
              97,
              103,
              115,
              47,
              123,
              116,
              97,
              103,
              125,
            ]),
          ],
        },
      },
    },
    /** SetMemoAttachments sets attachments for a memo. */
    setMemoAttachments: {
      name: "SetMemoAttachments",
      requestType: SetMemoAttachmentsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              39,
              58,
              1,
              42,
              50,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoAttachments lists attachments for a memo. */
    listMemoAttachments: {
      name: "ListMemoAttachments",
      requestType: ListMemoAttachmentsRequest,
      requestStream: false,
      responseType: ListMemoAttachmentsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** SetMemoRelations sets relations for a memo. */
    setMemoRelations: {
      name: "SetMemoRelations",
      requestType: SetMemoRelationsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              58,
              1,
              42,
              50,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              108,
              97,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoRelations lists relations for a memo. */
    listMemoRelations: {
      name: "ListMemoRelations",
      requestType: ListMemoRelationsRequest,
      requestStream: false,
      responseType: ListMemoRelationsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              108,
              97,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** CreateMemoComment creates a comment for a memo. */
    createMemoComment: {
      name: "CreateMemoComment",
      requestType: CreateMemoCommentRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([12, 110, 97, 109, 101, 44, 99, 111, 109, 109, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              42,
              58,
              7,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              34,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoComments lists comments for a memo. */
    listMemoComments: {
      name: "ListMemoComments",
      requestType: ListMemoCommentsRequest,
      requestStream: false,
      responseType: ListMemoCommentsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              33,
              18,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoReactions lists reactions for a memo. */
    listMemoReactions: {
      name: "ListMemoReactions",
      requestType: ListMemoReactionsRequest,
      requestStream: false,
      responseType: ListMemoReactionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** UpsertMemoReaction upserts a reaction for a memo. */
    upsertMemoReaction: {
      name: "UpsertMemoReaction",
      requestType: UpsertMemoReactionRequest,
      requestStream: false,
      responseType: Reaction,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              58,
              1,
              42,
              34,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** DeleteMemoReaction deletes a reaction for a memo. */
    deleteMemoReaction: {
      name: "DeleteMemoReaction",
      requestType: DeleteMemoReactionRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              28,
              42,
              26,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
