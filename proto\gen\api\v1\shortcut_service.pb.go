// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/shortcut_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Shortcut struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the shortcut.
	// Format: users/{user}/shortcuts/{shortcut}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The title of the shortcut.
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// The filter expression for the shortcut.
	Filter        string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Shortcut) Reset() {
	*x = Shortcut{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Shortcut) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Shortcut) ProtoMessage() {}

func (x *Shortcut) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Shortcut.ProtoReflect.Descriptor instead.
func (*Shortcut) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{0}
}

func (x *Shortcut) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Shortcut) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Shortcut) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

type ListShortcutsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource where shortcuts are listed.
	// Format: users/{user}
	Parent        string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShortcutsRequest) Reset() {
	*x = ListShortcutsRequest{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShortcutsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShortcutsRequest) ProtoMessage() {}

func (x *ListShortcutsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShortcutsRequest.ProtoReflect.Descriptor instead.
func (*ListShortcutsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListShortcutsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

type ListShortcutsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of shortcuts.
	Shortcuts     []*Shortcut `protobuf:"bytes,1,rep,name=shortcuts,proto3" json:"shortcuts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShortcutsResponse) Reset() {
	*x = ListShortcutsResponse{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShortcutsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShortcutsResponse) ProtoMessage() {}

func (x *ListShortcutsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShortcutsResponse.ProtoReflect.Descriptor instead.
func (*ListShortcutsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListShortcutsResponse) GetShortcuts() []*Shortcut {
	if x != nil {
		return x.Shortcuts
	}
	return nil
}

type GetShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the shortcut to retrieve.
	// Format: users/{user}/shortcuts/{shortcut}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetShortcutRequest) Reset() {
	*x = GetShortcutRequest{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShortcutRequest) ProtoMessage() {}

func (x *GetShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShortcutRequest.ProtoReflect.Descriptor instead.
func (*GetShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetShortcutRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreateShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource where this shortcut will be created.
	// Format: users/{user}
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The shortcut to create.
	Shortcut *Shortcut `protobuf:"bytes,2,opt,name=shortcut,proto3" json:"shortcut,omitempty"`
	// Optional. If set, validate the request, but do not actually create the shortcut.
	ValidateOnly  bool `protobuf:"varint,3,opt,name=validate_only,json=validateOnly,proto3" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateShortcutRequest) Reset() {
	*x = CreateShortcutRequest{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateShortcutRequest) ProtoMessage() {}

func (x *CreateShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateShortcutRequest.ProtoReflect.Descriptor instead.
func (*CreateShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateShortcutRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateShortcutRequest) GetShortcut() *Shortcut {
	if x != nil {
		return x.Shortcut
	}
	return nil
}

func (x *CreateShortcutRequest) GetValidateOnly() bool {
	if x != nil {
		return x.ValidateOnly
	}
	return false
}

type UpdateShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The shortcut resource which replaces the resource on the server.
	Shortcut *Shortcut `protobuf:"bytes,1,opt,name=shortcut,proto3" json:"shortcut,omitempty"`
	// Optional. The list of fields to update.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateShortcutRequest) Reset() {
	*x = UpdateShortcutRequest{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateShortcutRequest) ProtoMessage() {}

func (x *UpdateShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateShortcutRequest.ProtoReflect.Descriptor instead.
func (*UpdateShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateShortcutRequest) GetShortcut() *Shortcut {
	if x != nil {
		return x.Shortcut
	}
	return nil
}

func (x *UpdateShortcutRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the shortcut to delete.
	// Format: users/{user}/shortcuts/{shortcut}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteShortcutRequest) Reset() {
	*x = DeleteShortcutRequest{}
	mi := &file_api_v1_shortcut_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteShortcutRequest) ProtoMessage() {}

func (x *DeleteShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_shortcut_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteShortcutRequest.ProtoReflect.Descriptor instead.
func (*DeleteShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_shortcut_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteShortcutRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_shortcut_service_proto protoreflect.FileDescriptor

const file_api_v1_shortcut_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/v1/shortcut_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\"\xaf\x01\n" +
	"\bShortcut\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12\x19\n" +
	"\x05title\x18\x02 \x01(\tB\x03\xe0A\x02R\x05title\x12\x1b\n" +
	"\x06filter\x18\x03 \x01(\tB\x03\xe0A\x01R\x06filter:R\xeaAO\n" +
	"\x15memos.api.v1/Shortcut\x12!users/{user}/shortcuts/{shortcut}*\tshortcuts2\bshortcut\"M\n" +
	"\x14ListShortcutsRequest\x125\n" +
	"\x06parent\x18\x01 \x01(\tB\x1d\xe0A\x02\xfaA\x17\x12\x15memos.api.v1/ShortcutR\x06parent\"M\n" +
	"\x15ListShortcutsResponse\x124\n" +
	"\tshortcuts\x18\x01 \x03(\v2\x16.memos.api.v1.ShortcutR\tshortcuts\"G\n" +
	"\x12GetShortcutRequest\x121\n" +
	"\x04name\x18\x01 \x01(\tB\x1d\xe0A\x02\xfaA\x17\n" +
	"\x15memos.api.v1/ShortcutR\x04name\"\xb1\x01\n" +
	"\x15CreateShortcutRequest\x125\n" +
	"\x06parent\x18\x01 \x01(\tB\x1d\xe0A\x02\xfaA\x17\x12\x15memos.api.v1/ShortcutR\x06parent\x127\n" +
	"\bshortcut\x18\x02 \x01(\v2\x16.memos.api.v1.ShortcutB\x03\xe0A\x02R\bshortcut\x12(\n" +
	"\rvalidate_only\x18\x03 \x01(\bB\x03\xe0A\x01R\fvalidateOnly\"\x92\x01\n" +
	"\x15UpdateShortcutRequest\x127\n" +
	"\bshortcut\x18\x01 \x01(\v2\x16.memos.api.v1.ShortcutB\x03\xe0A\x02R\bshortcut\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x01R\n" +
	"updateMask\"J\n" +
	"\x15DeleteShortcutRequest\x121\n" +
	"\x04name\x18\x01 \x01(\tB\x1d\xe0A\x02\xfaA\x17\n" +
	"\x15memos.api.v1/ShortcutR\x04name2\xde\x05\n" +
	"\x0fShortcutService\x12\x8d\x01\n" +
	"\rListShortcuts\x12\".memos.api.v1.ListShortcutsRequest\x1a#.memos.api.v1.ListShortcutsResponse\"3\xdaA\x06parent\x82\xd3\xe4\x93\x02$\x12\"/api/v1/{parent=users/*}/shortcuts\x12z\n" +
	"\vGetShortcut\x12 .memos.api.v1.GetShortcutRequest\x1a\x16.memos.api.v1.Shortcut\"1\xdaA\x04name\x82\xd3\xe4\x93\x02$\x12\"/api/v1/{name=users/*/shortcuts/*}\x12\x95\x01\n" +
	"\x0eCreateShortcut\x12#.memos.api.v1.CreateShortcutRequest\x1a\x16.memos.api.v1.Shortcut\"F\xdaA\x0fparent,shortcut\x82\xd3\xe4\x93\x02.:\bshortcut\"\"/api/v1/{parent=users/*}/shortcuts\x12\xa3\x01\n" +
	"\x0eUpdateShortcut\x12#.memos.api.v1.UpdateShortcutRequest\x1a\x16.memos.api.v1.Shortcut\"T\xdaA\x14shortcut,update_mask\x82\xd3\xe4\x93\x027:\bshortcut2+/api/v1/{shortcut.name=users/*/shortcuts/*}\x12\x80\x01\n" +
	"\x0eDeleteShortcut\x12#.memos.api.v1.DeleteShortcutRequest\x1a\x16.google.protobuf.Empty\"1\xdaA\x04name\x82\xd3\xe4\x93\x02$*\"/api/v1/{name=users/*/shortcuts/*}B\xac\x01\n" +
	"\x10com.memos.api.v1B\x14ShortcutServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_shortcut_service_proto_rawDescOnce sync.Once
	file_api_v1_shortcut_service_proto_rawDescData []byte
)

func file_api_v1_shortcut_service_proto_rawDescGZIP() []byte {
	file_api_v1_shortcut_service_proto_rawDescOnce.Do(func() {
		file_api_v1_shortcut_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_shortcut_service_proto_rawDesc), len(file_api_v1_shortcut_service_proto_rawDesc)))
	})
	return file_api_v1_shortcut_service_proto_rawDescData
}

var file_api_v1_shortcut_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_v1_shortcut_service_proto_goTypes = []any{
	(*Shortcut)(nil),              // 0: memos.api.v1.Shortcut
	(*ListShortcutsRequest)(nil),  // 1: memos.api.v1.ListShortcutsRequest
	(*ListShortcutsResponse)(nil), // 2: memos.api.v1.ListShortcutsResponse
	(*GetShortcutRequest)(nil),    // 3: memos.api.v1.GetShortcutRequest
	(*CreateShortcutRequest)(nil), // 4: memos.api.v1.CreateShortcutRequest
	(*UpdateShortcutRequest)(nil), // 5: memos.api.v1.UpdateShortcutRequest
	(*DeleteShortcutRequest)(nil), // 6: memos.api.v1.DeleteShortcutRequest
	(*fieldmaskpb.FieldMask)(nil), // 7: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),         // 8: google.protobuf.Empty
}
var file_api_v1_shortcut_service_proto_depIdxs = []int32{
	0, // 0: memos.api.v1.ListShortcutsResponse.shortcuts:type_name -> memos.api.v1.Shortcut
	0, // 1: memos.api.v1.CreateShortcutRequest.shortcut:type_name -> memos.api.v1.Shortcut
	0, // 2: memos.api.v1.UpdateShortcutRequest.shortcut:type_name -> memos.api.v1.Shortcut
	7, // 3: memos.api.v1.UpdateShortcutRequest.update_mask:type_name -> google.protobuf.FieldMask
	1, // 4: memos.api.v1.ShortcutService.ListShortcuts:input_type -> memos.api.v1.ListShortcutsRequest
	3, // 5: memos.api.v1.ShortcutService.GetShortcut:input_type -> memos.api.v1.GetShortcutRequest
	4, // 6: memos.api.v1.ShortcutService.CreateShortcut:input_type -> memos.api.v1.CreateShortcutRequest
	5, // 7: memos.api.v1.ShortcutService.UpdateShortcut:input_type -> memos.api.v1.UpdateShortcutRequest
	6, // 8: memos.api.v1.ShortcutService.DeleteShortcut:input_type -> memos.api.v1.DeleteShortcutRequest
	2, // 9: memos.api.v1.ShortcutService.ListShortcuts:output_type -> memos.api.v1.ListShortcutsResponse
	0, // 10: memos.api.v1.ShortcutService.GetShortcut:output_type -> memos.api.v1.Shortcut
	0, // 11: memos.api.v1.ShortcutService.CreateShortcut:output_type -> memos.api.v1.Shortcut
	0, // 12: memos.api.v1.ShortcutService.UpdateShortcut:output_type -> memos.api.v1.Shortcut
	8, // 13: memos.api.v1.ShortcutService.DeleteShortcut:output_type -> google.protobuf.Empty
	9, // [9:14] is the sub-list for method output_type
	4, // [4:9] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_v1_shortcut_service_proto_init() }
func file_api_v1_shortcut_service_proto_init() {
	if File_api_v1_shortcut_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_shortcut_service_proto_rawDesc), len(file_api_v1_shortcut_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_shortcut_service_proto_goTypes,
		DependencyIndexes: file_api_v1_shortcut_service_proto_depIdxs,
		MessageInfos:      file_api_v1_shortcut_service_proto_msgTypes,
	}.Build()
	File_api_v1_shortcut_service_proto = out.File
	file_api_v1_shortcut_service_proto_goTypes = nil
	file_api_v1_shortcut_service_proto_depIdxs = nil
}
