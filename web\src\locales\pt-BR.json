{"about": {"blogs": "Blogs", "description": "Um serviço de anotações leve que prioriza a privacidade. Capture e compartilhe facilmente suas grandes ideias.", "documents": "Documentação", "github-repository": "Repositório do GitHub", "official-website": "Site Oficial"}, "auth": {"create-your-account": "Crie sua conta", "host-tip": "Você está se registrando como Administrador desta instância.", "new-password": "Nova senha", "repeat-new-password": "<PERSON>ita a nova senha", "sign-in-tip": "Já tem uma conta?", "sign-up-tip": "Ainda não tem uma conta?"}, "common": {"about": "Sobre", "add": "<PERSON><PERSON><PERSON><PERSON>", "admin": "Admin", "archive": "<PERSON><PERSON><PERSON><PERSON>", "archived": "Arquivo", "avatar": "Avatar", "basic": "Básico", "beta": "Beta", "cancel": "<PERSON><PERSON><PERSON>", "change": "Alterar", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar", "create": "<PERSON><PERSON><PERSON>", "created-at": "C<PERSON><PERSON> em", "database": "Banco de dados", "day": "<PERSON>a", "days": "<PERSON><PERSON>", "delete": "Deletar", "description": "Descrição", "edit": "<PERSON><PERSON>", "email": "Email", "expand": "Expandir", "explore": "Explorar", "file": "Arquivo", "filter": "Filtro", "home": "Início", "image": "Imagem", "in": "Em", "inbox": "Notificações", "input": "Entrada", "language": "Idioma", "last-updated-at": "Atualizado em", "learn-more": "<PERSON><PERSON> mais", "link": "Link", "mark": "Vincular", "memo": "Memo", "memos": "Memos", "name": "Nome", "new": "Novo", "nickname": "Apelido", "null": "<PERSON><PERSON>", "or": "ou", "password": "<PERSON><PERSON>", "pin": "Fixar", "pinned": "Fixado", "preview": "Pré-visualizar", "profile": "Perfil", "properties": "<PERSON><PERSON><PERSON><PERSON>", "referenced-by": "Referenciado por", "referencing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relations": "Relações", "remember-me": "<PERSON><PERSON><PERSON> de mim", "rename": "Renomear", "reset": "Redefinir", "resources": "Recursos", "restore": "Restaurar", "role": "Cargo", "save": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "settings": "<PERSON><PERSON><PERSON><PERSON>", "share": "Compartilhar", "shortcut-filter": "Filtro de atalhos", "shortcuts": "Atalhos", "sign-in": "Entrar", "sign-in-with": "Entrar com {{provider}}", "sign-out": "<PERSON><PERSON>", "sign-up": "Registrar", "statistics": "Estatísticas", "tags": "Tags", "title": "<PERSON><PERSON><PERSON><PERSON>", "tree-mode": "<PERSON><PERSON>", "type": "Tipo", "unpin": "Desafixar", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "username": "Nome de usuário", "version": "Vers<PERSON>", "visibility": "Visibilidade", "yourself": "<PERSON><PERSON><PERSON> mesmo"}, "days": {"fri": "Sex", "mon": "Seg", "sat": "<PERSON><PERSON><PERSON>", "sun": "Dom", "thu": "<PERSON>ui", "tue": "<PERSON><PERSON>", "wed": "<PERSON>ua"}, "editor": {"add-your-comment-here": "Adicione seu comentário aqui…", "any-thoughts": "<PERSON><PERSON>a ideia…", "save": "<PERSON><PERSON>"}, "filters": {"has-code": "temCódigo", "has-link": "temLink", "has-task-list": "temListaDeTarefas"}, "inbox": {"memo-comment": "{{user}} tem um comentário sobre {{memo}}.", "version-update": "A nova versão {{version}} já está disponível!"}, "markdown": {"checkbox": "Caixa de seleção", "code-block": "Bloco de código", "content-syntax": "Sintaxe do conteúdo"}, "memo": {"archived-at": "Arquivado em", "click-to-hide-nsfw-content": "Ocultar conteúdo <PERSON>róprio", "click-to-show-nsfw-content": "Mostrar conteúdo <PERSON>róprio", "code": "Código", "comment": {"self": "Comentários", "write-a-comment": "Escreva um comentário"}, "copy-link": "Copiar link", "count-memos-in-date": "{{count}} memos em {{date}}", "delete-confirm": "Tem certeza de que deseja deletar este memo?\n\nESTA AÇÃO É IRREVERSÍVEL", "direction": "<PERSON><PERSON><PERSON>", "direction-asc": "<PERSON><PERSON>", "direction-desc": "Decrescente", "display-time": "<PERSON><PERSON><PERSON><PERSON>", "filters": "<PERSON><PERSON><PERSON>", "links": "Links", "load-more": "<PERSON><PERSON><PERSON> mais", "no-archived-memos": "Nenhum memo arquivado.", "no-memos": "Nenhum memo.", "order-by": "Ordenar por", "remove-completed-task-list-items": "Remover concluídos", "remove-completed-task-list-items-confirm": "Você tem certeza de que deseja remover todos os itens concluídos? (Essa ação é irreversível)", "search-placeholder": "Pesquisar memos", "show-less": "<PERSON><PERSON> menos", "show-more": "<PERSON><PERSON> mais", "to-do": "<PERSON><PERSON><PERSON><PERSON>", "view-detail": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "visibility": {"disabled": "Memos públicos estão desabilitados", "private": "Privado (eu)", "protected": "Protegido (membros)", "public": "Público (todos)"}}, "message": {"archived-successfully": "Arquivado com êxito", "change-memo-created-time": "Alterar data de criação do memo", "copied": "Copiado", "deleted-successfully": "Êxito na exclusão", "description-is-required": "A descrição é necessária", "failed-to-embed-memo": "Falha ao incorporar o memo", "fill-all": "Por favor, preencha todos os campos.", "fill-all-required-fields": "Por favor, preencha todos os campos obrigatórios", "maximum-upload-size-is": "O tamanho máximo permitido para upload é {{size}} MiB", "memo-not-found": "Memo não encontrado.", "new-password-not-match": "As novas senhas não coincidem.", "no-data": "N<PERSON>hum dado encontrado.", "password-changed": "<PERSON><PERSON> alterada", "password-not-match": "As senhas não coincidem.", "remove-completed-task-list-items-successfully": "Remoção bem-sucedida!", "restored-successfully": "Restaurado com êxito", "succeed-copy-link": "Link copiado com êxito.", "update-succeed": "Atualizado com êxito", "user-not-found": "Usuário não encontrado"}, "reference": {"add-references": "Adici<PERSON><PERSON>", "embedded-usage": "Usar como Conteúdo Embutido", "no-memos-found": "Nenhum memo encontrado", "search-placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "resource": {"clear": "Limpar", "copy-link": "Copiar link", "create-dialog": {"external-link": {"file-name": "Nome do arquivo", "file-name-placeholder": "Nome do arquivo", "link": "Link", "link-placeholder": "https://o.link.para/seu/recurso", "option": "Link externo", "type": "Tipo", "type-placeholder": "Tipo do arquivo"}, "local-file": {"choose": "Escolher arquivo…", "option": "Arquivo local"}, "title": "<PERSON><PERSON><PERSON> recurso", "upload-method": "<PERSON><PERSON><PERSON><PERSON>"}, "delete-resource": "Deletar recurso", "delete-selected-resources": "Deletar recursos selecionados", "fetching-data": "Obtendo dados…", "file-drag-drop-prompt": "Arraste e solte o arquivo aqui para carregá-lo", "linked-amount": "Quantidade de vínculos", "no-files-selected": "Nenhum arquivo selecionado!", "no-resources": "<PERSON>enhum recurso.", "no-unused-resources": "Nenhum recurso não utilizado", "reset-link": "Redefinir link", "reset-link-prompt": "Tem certeza de que deseja redefinir o link?\nIs<PERSON> quebrar<PERSON> todos os vínculos atuais.\n\nESTA AÇÃO É IRREVERSÍVEL", "reset-resource-link": "Redefinir link do recurso"}, "router": {"back-to-top": "Voltar ao Topo", "go-to-home": "Ir ao in<PERSON>cio"}, "setting": {"access-token-section": {"access-token-copied-to-clipboard": "Token de acesso copiado para a área de transferência", "access-token-deletion": "Tem certeza de que deseja excluir o token de acesso {{accessToken}}? ESSA AÇÃO É IRREVERSÍVEL.", "create-dialog": {"create-access-token": "Criar <PERSON> de acesso", "created-at": "C<PERSON><PERSON> em", "description": "Descrição", "duration-1m": "1 Mês", "duration-8h": "8 Horas", "duration-never": "Nunca", "expiration": "Expiração", "expires-at": "Expira em", "some-description": "Alguma descrição…"}, "description": "Uma lista de todos os tokens de acesso de sua conta.", "title": "Tokens de acesso", "token": "Token"}, "account-section": {"change-password": "<PERSON><PERSON><PERSON><PERSON>", "email-note": "Opcional", "export-memos": "Exportar Memos", "nickname-note": "Exibido no banner", "openapi-reset": "Redefinir chave OpenAPI", "openapi-sample-post": "O<PERSON>á #memos em {{url}}", "openapi-title": "OpenAPI", "reset-api": "Redefinir API", "title": "Informação da conta", "update-information": "Atualizar informações", "username-note": "Usado para entrar"}, "appearance-option": {"dark": "Escuro", "light": "<PERSON><PERSON><PERSON>", "system": "Sistema"}, "member": "Membro", "member-list": "Lista de membros", "member-section": {"admin": "Admin", "archive-member": "<PERSON><PERSON><PERSON><PERSON> membro", "archive-warning": "! Tem certeza de que deseja arquivar {{username}}?", "create-a-member": "Criar um membro", "delete-member": "Deletar membro", "delete-warning": "Tem certeza de que deseja deletar {{username}}?\n\nESTA AÇÃO É IRREVERSÍVEL", "user": "<PERSON><PERSON><PERSON><PERSON>"}, "memo-related": "Memo", "memo-related-settings": {"content-lenght-limit": "Limite de tamanho do conteúdo (Bytes)", "enable-blur-nsfw-content": "<PERSON><PERSON><PERSON> con<PERSON>do impró<PERSON> (adicione as tags abaixo)", "enable-link-preview": "Prévias de link", "enable-memo-comments": "Comentários nos memos", "enable-memo-location": "Marcador de localização", "reactions": "Reações", "title": "Ajustes relacionados aos memos"}, "my-account": "Minha conta", "preference": "Preferências", "preference-section": {"default-memo-sort-option": "Exibição de tempo do memo", "default-memo-visibility": "Visibilidade padrão do memo", "theme": "<PERSON><PERSON>"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Endpoint de autenticação", "client-id": "ID do cliente", "client-secret": "Segredo do cliente", "confirm-delete": "Tem certeza de que deseja excluir a configuração SSO \"{{name}}\"?\n\nESTA AÇÃO É IRREVERSÍVEL", "create-sso": "Criar SSO", "custom": "Personalizado", "delete-sso": "Confirmar exclusão", "disabled-password-login-warning": "O login com senha está desabilitado. Tome cuidado ao remover provedores de identidade!", "display-name": "Nome de exibição", "identifier": "Identificador", "identifier-filter": "Filtro identificador", "no-sso-found": "Nenhum SSO encontrado.", "redirect-url": "URL de redirecionamento", "scopes": "Escopos", "single-sign-on": "Configurando login único (SSO) para autenticação", "sso-created": "SSO {{name}} criado", "sso-list": "Lista de SSOs (Login Único)", "sso-updated": "SSO {{name}} atualizado", "template": "<PERSON><PERSON>", "token-endpoint": "Endpoint de token", "update-sso": "Atualizar SSO", "user-endpoint": "Endpoint do usuário"}, "storage": "Armazenamento", "storage-section": {"accesskey": "<PERSON><PERSON>", "accesskey-placeholder": "Chave de acesso / <PERSON> de acesso", "bucket": "Bucket", "bucket-placeholder": "Nome do bucket", "create-a-service": "Criar um serviço", "create-storage": "<PERSON><PERSON><PERSON>", "current-storage": "Armazenamento de objetos atual", "delete-storage": "Deletar armazenamento", "endpoint": "Ponto de extremidade (endpoint)", "filepath-template": "Formato do caminho de arquivo", "local-storage-path": "Caminho do armazenamento local", "path": "Caminho do armazenamento", "path-description": "Voc<PERSON> pode usar as mesmas variáveis dinâmicas do armazenamento local, como {filename}", "path-placeholder": "caminho", "presign-placeholder": "URL pré-assinado, opcional", "region": "Região", "region-placeholder": "Nome da região", "s3-compatible-url": "URL compatível com S3", "secretkey": "Chave secreta", "secretkey-placeholder": "Chave secreta / Chave de <PERSON>sso", "storage-services": "Lista de serviços de armazenamento", "type-database": "Banco de dados", "type-local": "Arquivos locais", "update-a-service": "Atualizar um serviço", "update-local-path": "Atualizar caminho do armazenamento local", "update-local-path-description": "O caminho de armazenamento local é relativo ao seu banco de dados", "update-storage": "Atualizar armaz<PERSON>", "url-prefix": "Prefixo da URL", "url-prefix-placeholder": "Prefixo personalizado da URL, opcional", "url-suffix": "Sufixo da URL", "url-suffix-placeholder": "Sufixo personalizado da URL, opcional", "warning-text": "Tem certeza de que deseja deletar o serviço de armazenamento \"{{name}}\"?\n\nESTA AÇÃO É IRREVERSÍVEL"}, "system": "Sistema", "system-section": {"additional-script": "Script adicional", "additional-script-placeholder": "Código JavaScript adicional", "additional-style": "Estilo adicional", "additional-style-placeholder": "Código CSS adicional", "allow-user-signup": "Permitir registro de usuá<PERSON>", "customize-server": {"appearance": "Aparência da instância", "description": "Descrição", "icon-url": "URL do ícone", "locale": "Localização da instância", "title": "Personalizar instân<PERSON>"}, "disable-markdown-shortcuts-in-editor": "Desabilitar atalhos de Markdown no editor", "disable-password-login": "Desabilitar login com senha", "disable-password-login-final-warning": "Por favor, digite \"CONFIRM\" para prosseguir.", "disable-password-login-warning": "Isso desabilitará o login com senha para todos os usuários. Caso seus provedores de identidade (SSO) configurados falhem, não será possível fazer login sem reverter esse ajuste manualmente no banco de dados. Você também deverá tomar precauções adicionais ao remover qualquer provedor de identidade !", "disable-public-memos": "Desabilitar memos públicos", "display-with-updated-time": "Exibir hora de atualização nos memos", "enable-auto-compact": "Ativar exibição compacta automaticamente", "enable-double-click-to-edit": "Ativar clique duplo para editar", "enable-password-login": "Habilitar login com senha", "enable-password-login-warning": "Is<PERSON> permitirá o login com senha para todos os usuários. Continue apenas se desejar que os usuários possam fazer login usando SSO e senha local.", "max-upload-size": "Tamanho máximo de upload (MiB)", "max-upload-size-hint": "O valor recomendado é 30 MiB.", "removed-completed-task-list-items": "Remoção de itens concluídos da lista de tarefas", "server-name": "Nome da instância", "title": "G<PERSON>"}, "version": "Vers<PERSON>", "webhook-section": {"create-dialog": {"an-easy-to-remember-name": "Um nome fácil de lembrar", "create-webhook": "<PERSON><PERSON><PERSON> webhook", "edit-webhook": "<PERSON><PERSON> webhook", "payload-url": "URL do payload", "title": "Nome", "url-example-post-receive": "https://exemplo.com.br/pós-recebimento"}, "no-webhooks-found": "<PERSON><PERSON><PERSON> webhook encontrado.", "title": "Webhooks", "url": "URL"}, "workspace-section": {"disallow-change-nickname": "Impedir troca de apelido", "disallow-change-username": "Impedir troca de nome de usuário", "disallow-password-auth": "Impedir autenticação por senha", "disallow-user-registration": "Impedir registro de usuário", "monday": "Segunda-feira", "saturday": "Sábado", "sunday": "Domingo", "week-start-day": "Dia de início da <PERSON>mana"}}, "tag": {"all-tags": "<PERSON><PERSON> as Tags", "create-tag": "Criar Tag", "create-tags-guide": "Você pode criar tags inserindo `#tag`.", "delete-confirm": "Tem certeza de que deseja excluir esta tag?\\nTodos os memos relacionados serão arquivados.", "delete-tag": "Excluir Tag", "new-name": "Novo Nome", "no-tag-found": "Nenhuma tag encontrada", "old-name": "Nome Antigo", "rename-error-empty": "O nome da tag não pode ser vazio ou conter espaços", "rename-error-repeat": "O novo nome não pode ser o mesmo que o antigo", "rename-success": "Tag renomeada com êxito", "rename-tag": "Renomear tag", "rename-tip": "Todos seus memos com essa tag serão atualizados."}}