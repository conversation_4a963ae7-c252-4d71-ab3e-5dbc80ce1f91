// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/user_setting.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserSetting_Key int32

const (
	UserSetting_KEY_UNSPECIFIED UserSetting_Key = 0
	// General user settings.
	UserSetting_GENERAL UserSetting_Key = 1
	// User authentication sessions.
	UserSetting_SESSIONS UserSetting_Key = 2
	// Access tokens for the user.
	UserSetting_ACCESS_TOKENS UserSetting_Key = 3
	// The shortcuts of the user.
	UserSetting_SHORTCUTS UserSetting_Key = 4
	// The webhooks of the user.
	UserSetting_WEBHOOKS UserSetting_Key = 5
)

// Enum value maps for UserSetting_Key.
var (
	UserSetting_Key_name = map[int32]string{
		0: "KEY_UNSPECIFIED",
		1: "GENERAL",
		2: "SESSIONS",
		3: "ACCESS_TOKENS",
		4: "SHORTCUTS",
		5: "WEBHOOKS",
	}
	UserSetting_Key_value = map[string]int32{
		"KEY_UNSPECIFIED": 0,
		"GENERAL":         1,
		"SESSIONS":        2,
		"ACCESS_TOKENS":   3,
		"SHORTCUTS":       4,
		"WEBHOOKS":        5,
	}
)

func (x UserSetting_Key) Enum() *UserSetting_Key {
	p := new(UserSetting_Key)
	*p = x
	return p
}

func (x UserSetting_Key) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserSetting_Key) Descriptor() protoreflect.EnumDescriptor {
	return file_store_user_setting_proto_enumTypes[0].Descriptor()
}

func (UserSetting_Key) Type() protoreflect.EnumType {
	return &file_store_user_setting_proto_enumTypes[0]
}

func (x UserSetting_Key) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserSetting_Key.Descriptor instead.
func (UserSetting_Key) EnumDescriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{0, 0}
}

type UserSetting struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	UserId int32                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Key    UserSetting_Key        `protobuf:"varint,2,opt,name=key,proto3,enum=memos.store.UserSetting_Key" json:"key,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*UserSetting_General
	//	*UserSetting_Sessions
	//	*UserSetting_AccessTokens
	//	*UserSetting_Shortcuts
	//	*UserSetting_Webhooks
	Value         isUserSetting_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSetting) Reset() {
	*x = UserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSetting) ProtoMessage() {}

func (x *UserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSetting.ProtoReflect.Descriptor instead.
func (*UserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{0}
}

func (x *UserSetting) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSetting) GetKey() UserSetting_Key {
	if x != nil {
		return x.Key
	}
	return UserSetting_KEY_UNSPECIFIED
}

func (x *UserSetting) GetValue() isUserSetting_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *UserSetting) GetGeneral() *GeneralUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_General); ok {
			return x.General
		}
	}
	return nil
}

func (x *UserSetting) GetSessions() *SessionsUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Sessions); ok {
			return x.Sessions
		}
	}
	return nil
}

func (x *UserSetting) GetAccessTokens() *AccessTokensUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_AccessTokens); ok {
			return x.AccessTokens
		}
	}
	return nil
}

func (x *UserSetting) GetShortcuts() *ShortcutsUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Shortcuts); ok {
			return x.Shortcuts
		}
	}
	return nil
}

func (x *UserSetting) GetWebhooks() *WebhooksUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Webhooks); ok {
			return x.Webhooks
		}
	}
	return nil
}

type isUserSetting_Value interface {
	isUserSetting_Value()
}

type UserSetting_General struct {
	General *GeneralUserSetting `protobuf:"bytes,3,opt,name=general,proto3,oneof"`
}

type UserSetting_Sessions struct {
	Sessions *SessionsUserSetting `protobuf:"bytes,4,opt,name=sessions,proto3,oneof"`
}

type UserSetting_AccessTokens struct {
	AccessTokens *AccessTokensUserSetting `protobuf:"bytes,5,opt,name=access_tokens,json=accessTokens,proto3,oneof"`
}

type UserSetting_Shortcuts struct {
	Shortcuts *ShortcutsUserSetting `protobuf:"bytes,6,opt,name=shortcuts,proto3,oneof"`
}

type UserSetting_Webhooks struct {
	Webhooks *WebhooksUserSetting `protobuf:"bytes,7,opt,name=webhooks,proto3,oneof"`
}

func (*UserSetting_General) isUserSetting_Value() {}

func (*UserSetting_Sessions) isUserSetting_Value() {}

func (*UserSetting_AccessTokens) isUserSetting_Value() {}

func (*UserSetting_Shortcuts) isUserSetting_Value() {}

func (*UserSetting_Webhooks) isUserSetting_Value() {}

type GeneralUserSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The user's locale.
	Locale string `protobuf:"bytes,1,opt,name=locale,proto3" json:"locale,omitempty"`
	// The user's appearance setting.
	Appearance string `protobuf:"bytes,2,opt,name=appearance,proto3" json:"appearance,omitempty"`
	// The user's memo visibility setting.
	MemoVisibility string `protobuf:"bytes,3,opt,name=memo_visibility,json=memoVisibility,proto3" json:"memo_visibility,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GeneralUserSetting) Reset() {
	*x = GeneralUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneralUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneralUserSetting) ProtoMessage() {}

func (x *GeneralUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneralUserSetting.ProtoReflect.Descriptor instead.
func (*GeneralUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{1}
}

func (x *GeneralUserSetting) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *GeneralUserSetting) GetAppearance() string {
	if x != nil {
		return x.Appearance
	}
	return ""
}

func (x *GeneralUserSetting) GetMemoVisibility() string {
	if x != nil {
		return x.MemoVisibility
	}
	return ""
}

type SessionsUserSetting struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Sessions      []*SessionsUserSetting_Session `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionsUserSetting) Reset() {
	*x = SessionsUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionsUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionsUserSetting) ProtoMessage() {}

func (x *SessionsUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionsUserSetting.ProtoReflect.Descriptor instead.
func (*SessionsUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{2}
}

func (x *SessionsUserSetting) GetSessions() []*SessionsUserSetting_Session {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type AccessTokensUserSetting struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	AccessTokens  []*AccessTokensUserSetting_AccessToken `protobuf:"bytes,1,rep,name=access_tokens,json=accessTokens,proto3" json:"access_tokens,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccessTokensUserSetting) Reset() {
	*x = AccessTokensUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccessTokensUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTokensUserSetting) ProtoMessage() {}

func (x *AccessTokensUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTokensUserSetting.ProtoReflect.Descriptor instead.
func (*AccessTokensUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{3}
}

func (x *AccessTokensUserSetting) GetAccessTokens() []*AccessTokensUserSetting_AccessToken {
	if x != nil {
		return x.AccessTokens
	}
	return nil
}

type ShortcutsUserSetting struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Shortcuts     []*ShortcutsUserSetting_Shortcut `protobuf:"bytes,1,rep,name=shortcuts,proto3" json:"shortcuts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShortcutsUserSetting) Reset() {
	*x = ShortcutsUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShortcutsUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShortcutsUserSetting) ProtoMessage() {}

func (x *ShortcutsUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShortcutsUserSetting.ProtoReflect.Descriptor instead.
func (*ShortcutsUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{4}
}

func (x *ShortcutsUserSetting) GetShortcuts() []*ShortcutsUserSetting_Shortcut {
	if x != nil {
		return x.Shortcuts
	}
	return nil
}

type WebhooksUserSetting struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Webhooks      []*WebhooksUserSetting_Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhooksUserSetting) Reset() {
	*x = WebhooksUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhooksUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhooksUserSetting) ProtoMessage() {}

func (x *WebhooksUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhooksUserSetting.ProtoReflect.Descriptor instead.
func (*WebhooksUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{5}
}

func (x *WebhooksUserSetting) GetWebhooks() []*WebhooksUserSetting_Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

type SessionsUserSetting_Session struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique session identifier.
	SessionId string `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	// Timestamp when the session was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Timestamp when the session was last accessed.
	// Used for sliding expiration calculation (last_accessed_time + 2 weeks).
	LastAccessedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_accessed_time,json=lastAccessedTime,proto3" json:"last_accessed_time,omitempty"`
	// Client information associated with this session.
	ClientInfo    *SessionsUserSetting_ClientInfo `protobuf:"bytes,4,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionsUserSetting_Session) Reset() {
	*x = SessionsUserSetting_Session{}
	mi := &file_store_user_setting_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionsUserSetting_Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionsUserSetting_Session) ProtoMessage() {}

func (x *SessionsUserSetting_Session) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionsUserSetting_Session.ProtoReflect.Descriptor instead.
func (*SessionsUserSetting_Session) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SessionsUserSetting_Session) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionsUserSetting_Session) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *SessionsUserSetting_Session) GetLastAccessedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAccessedTime
	}
	return nil
}

func (x *SessionsUserSetting_Session) GetClientInfo() *SessionsUserSetting_ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

type SessionsUserSetting_ClientInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// User agent string of the client.
	UserAgent string `protobuf:"bytes,1,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// IP address of the client.
	IpAddress string `protobuf:"bytes,2,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	// Optional. Device type (e.g., "mobile", "desktop", "tablet").
	DeviceType string `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// Optional. Operating system (e.g., "iOS 17.0", "Windows 11").
	Os string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	// Optional. Browser name and version (e.g., "Chrome 119.0").
	Browser       string `protobuf:"bytes,5,opt,name=browser,proto3" json:"browser,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionsUserSetting_ClientInfo) Reset() {
	*x = SessionsUserSetting_ClientInfo{}
	mi := &file_store_user_setting_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionsUserSetting_ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionsUserSetting_ClientInfo) ProtoMessage() {}

func (x *SessionsUserSetting_ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionsUserSetting_ClientInfo.ProtoReflect.Descriptor instead.
func (*SessionsUserSetting_ClientInfo) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{2, 1}
}

func (x *SessionsUserSetting_ClientInfo) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *SessionsUserSetting_ClientInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *SessionsUserSetting_ClientInfo) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *SessionsUserSetting_ClientInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SessionsUserSetting_ClientInfo) GetBrowser() string {
	if x != nil {
		return x.Browser
	}
	return ""
}

type AccessTokensUserSetting_AccessToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The access token is a JWT token.
	// Including expiration time, issuer, etc.
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// A description for the access token.
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccessTokensUserSetting_AccessToken) Reset() {
	*x = AccessTokensUserSetting_AccessToken{}
	mi := &file_store_user_setting_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccessTokensUserSetting_AccessToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTokensUserSetting_AccessToken) ProtoMessage() {}

func (x *AccessTokensUserSetting_AccessToken) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTokensUserSetting_AccessToken.ProtoReflect.Descriptor instead.
func (*AccessTokensUserSetting_AccessToken) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AccessTokensUserSetting_AccessToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *AccessTokensUserSetting_AccessToken) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ShortcutsUserSetting_Shortcut struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Filter        string                 `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShortcutsUserSetting_Shortcut) Reset() {
	*x = ShortcutsUserSetting_Shortcut{}
	mi := &file_store_user_setting_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShortcutsUserSetting_Shortcut) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShortcutsUserSetting_Shortcut) ProtoMessage() {}

func (x *ShortcutsUserSetting_Shortcut) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShortcutsUserSetting_Shortcut.ProtoReflect.Descriptor instead.
func (*ShortcutsUserSetting_Shortcut) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ShortcutsUserSetting_Shortcut) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ShortcutsUserSetting_Shortcut) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ShortcutsUserSetting_Shortcut) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

type WebhooksUserSetting_Webhook struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the webhook
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Descriptive title for the webhook
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// The webhook URL endpoint
	Url           string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhooksUserSetting_Webhook) Reset() {
	*x = WebhooksUserSetting_Webhook{}
	mi := &file_store_user_setting_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhooksUserSetting_Webhook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhooksUserSetting_Webhook) ProtoMessage() {}

func (x *WebhooksUserSetting_Webhook) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhooksUserSetting_Webhook.ProtoReflect.Descriptor instead.
func (*WebhooksUserSetting_Webhook) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{5, 0}
}

func (x *WebhooksUserSetting_Webhook) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WebhooksUserSetting_Webhook) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WebhooksUserSetting_Webhook) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_store_user_setting_proto protoreflect.FileDescriptor

const file_store_user_setting_proto_rawDesc = "" +
	"\n" +
	"\x18store/user_setting.proto\x12\vmemos.store\x1a\x1fgoogle/protobuf/timestamp.proto\"\x93\x04\n" +
	"\vUserSetting\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x05R\x06userId\x12.\n" +
	"\x03key\x18\x02 \x01(\x0e2\x1c.memos.store.UserSetting.KeyR\x03key\x12;\n" +
	"\ageneral\x18\x03 \x01(\v2\x1f.memos.store.GeneralUserSettingH\x00R\ageneral\x12>\n" +
	"\bsessions\x18\x04 \x01(\v2 .memos.store.SessionsUserSettingH\x00R\bsessions\x12K\n" +
	"\raccess_tokens\x18\x05 \x01(\v2$.memos.store.AccessTokensUserSettingH\x00R\faccessTokens\x12A\n" +
	"\tshortcuts\x18\x06 \x01(\v2!.memos.store.ShortcutsUserSettingH\x00R\tshortcuts\x12>\n" +
	"\bwebhooks\x18\a \x01(\v2 .memos.store.WebhooksUserSettingH\x00R\bwebhooks\"e\n" +
	"\x03Key\x12\x13\n" +
	"\x0fKEY_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aGENERAL\x10\x01\x12\f\n" +
	"\bSESSIONS\x10\x02\x12\x11\n" +
	"\rACCESS_TOKENS\x10\x03\x12\r\n" +
	"\tSHORTCUTS\x10\x04\x12\f\n" +
	"\bWEBHOOKS\x10\x05B\a\n" +
	"\x05value\"u\n" +
	"\x12GeneralUserSetting\x12\x16\n" +
	"\x06locale\x18\x01 \x01(\tR\x06locale\x12\x1e\n" +
	"\n" +
	"appearance\x18\x02 \x01(\tR\n" +
	"appearance\x12'\n" +
	"\x0fmemo_visibility\x18\x03 \x01(\tR\x0ememoVisibility\"\xf3\x03\n" +
	"\x13SessionsUserSetting\x12D\n" +
	"\bsessions\x18\x01 \x03(\v2(.memos.store.SessionsUserSetting.SessionR\bsessions\x1a\xfd\x01\n" +
	"\aSession\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12;\n" +
	"\vcreate_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12H\n" +
	"\x12last_accessed_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x10lastAccessedTime\x12L\n" +
	"\vclient_info\x18\x04 \x01(\v2+.memos.store.SessionsUserSetting.ClientInfoR\n" +
	"clientInfo\x1a\x95\x01\n" +
	"\n" +
	"ClientInfo\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x01 \x01(\tR\tuserAgent\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x02 \x01(\tR\tipAddress\x12\x1f\n" +
	"\vdevice_type\x18\x03 \x01(\tR\n" +
	"deviceType\x12\x0e\n" +
	"\x02os\x18\x04 \x01(\tR\x02os\x12\x18\n" +
	"\abrowser\x18\x05 \x01(\tR\abrowser\"\xc4\x01\n" +
	"\x17AccessTokensUserSetting\x12U\n" +
	"\raccess_tokens\x18\x01 \x03(\v20.memos.store.AccessTokensUserSetting.AccessTokenR\faccessTokens\x1aR\n" +
	"\vAccessToken\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"\xaa\x01\n" +
	"\x14ShortcutsUserSetting\x12H\n" +
	"\tshortcuts\x18\x01 \x03(\v2*.memos.store.ShortcutsUserSetting.ShortcutR\tshortcuts\x1aH\n" +
	"\bShortcut\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x16\n" +
	"\x06filter\x18\x03 \x01(\tR\x06filter\"\x9e\x01\n" +
	"\x13WebhooksUserSetting\x12D\n" +
	"\bwebhooks\x18\x01 \x03(\v2(.memos.store.WebhooksUserSetting.WebhookR\bwebhooks\x1aA\n" +
	"\aWebhook\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03urlB\x9b\x01\n" +
	"\x0fcom.memos.storeB\x10UserSettingProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_user_setting_proto_rawDescOnce sync.Once
	file_store_user_setting_proto_rawDescData []byte
)

func file_store_user_setting_proto_rawDescGZIP() []byte {
	file_store_user_setting_proto_rawDescOnce.Do(func() {
		file_store_user_setting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_user_setting_proto_rawDesc), len(file_store_user_setting_proto_rawDesc)))
	})
	return file_store_user_setting_proto_rawDescData
}

var file_store_user_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_store_user_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_store_user_setting_proto_goTypes = []any{
	(UserSetting_Key)(0),                        // 0: memos.store.UserSetting.Key
	(*UserSetting)(nil),                         // 1: memos.store.UserSetting
	(*GeneralUserSetting)(nil),                  // 2: memos.store.GeneralUserSetting
	(*SessionsUserSetting)(nil),                 // 3: memos.store.SessionsUserSetting
	(*AccessTokensUserSetting)(nil),             // 4: memos.store.AccessTokensUserSetting
	(*ShortcutsUserSetting)(nil),                // 5: memos.store.ShortcutsUserSetting
	(*WebhooksUserSetting)(nil),                 // 6: memos.store.WebhooksUserSetting
	(*SessionsUserSetting_Session)(nil),         // 7: memos.store.SessionsUserSetting.Session
	(*SessionsUserSetting_ClientInfo)(nil),      // 8: memos.store.SessionsUserSetting.ClientInfo
	(*AccessTokensUserSetting_AccessToken)(nil), // 9: memos.store.AccessTokensUserSetting.AccessToken
	(*ShortcutsUserSetting_Shortcut)(nil),       // 10: memos.store.ShortcutsUserSetting.Shortcut
	(*WebhooksUserSetting_Webhook)(nil),         // 11: memos.store.WebhooksUserSetting.Webhook
	(*timestamppb.Timestamp)(nil),               // 12: google.protobuf.Timestamp
}
var file_store_user_setting_proto_depIdxs = []int32{
	0,  // 0: memos.store.UserSetting.key:type_name -> memos.store.UserSetting.Key
	2,  // 1: memos.store.UserSetting.general:type_name -> memos.store.GeneralUserSetting
	3,  // 2: memos.store.UserSetting.sessions:type_name -> memos.store.SessionsUserSetting
	4,  // 3: memos.store.UserSetting.access_tokens:type_name -> memos.store.AccessTokensUserSetting
	5,  // 4: memos.store.UserSetting.shortcuts:type_name -> memos.store.ShortcutsUserSetting
	6,  // 5: memos.store.UserSetting.webhooks:type_name -> memos.store.WebhooksUserSetting
	7,  // 6: memos.store.SessionsUserSetting.sessions:type_name -> memos.store.SessionsUserSetting.Session
	9,  // 7: memos.store.AccessTokensUserSetting.access_tokens:type_name -> memos.store.AccessTokensUserSetting.AccessToken
	10, // 8: memos.store.ShortcutsUserSetting.shortcuts:type_name -> memos.store.ShortcutsUserSetting.Shortcut
	11, // 9: memos.store.WebhooksUserSetting.webhooks:type_name -> memos.store.WebhooksUserSetting.Webhook
	12, // 10: memos.store.SessionsUserSetting.Session.create_time:type_name -> google.protobuf.Timestamp
	12, // 11: memos.store.SessionsUserSetting.Session.last_accessed_time:type_name -> google.protobuf.Timestamp
	8,  // 12: memos.store.SessionsUserSetting.Session.client_info:type_name -> memos.store.SessionsUserSetting.ClientInfo
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_store_user_setting_proto_init() }
func file_store_user_setting_proto_init() {
	if File_store_user_setting_proto != nil {
		return
	}
	file_store_user_setting_proto_msgTypes[0].OneofWrappers = []any{
		(*UserSetting_General)(nil),
		(*UserSetting_Sessions)(nil),
		(*UserSetting_AccessTokens)(nil),
		(*UserSetting_Shortcuts)(nil),
		(*UserSetting_Webhooks)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_user_setting_proto_rawDesc), len(file_store_user_setting_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_user_setting_proto_goTypes,
		DependencyIndexes: file_store_user_setting_proto_depIdxs,
		EnumInfos:         file_store_user_setting_proto_enumTypes,
		MessageInfos:      file_store_user_setting_proto_msgTypes,
	}.Build()
	File_store_user_setting_proto = out.File
	file_store_user_setting_proto_goTypes = nil
	file_store_user_setting_proto_depIdxs = nil
}
