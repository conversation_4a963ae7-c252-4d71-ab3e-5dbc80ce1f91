{"auth": {"create-your-account": "შექმენით თქვენი ანგარიში", "host-tip": "თქვენ რეგისტრირდებით როგორც საიტის მასპინძელი.", "new-password": "ახალი პაროლი", "repeat-new-password": "გაიმეორეთ ახალი პაროლი", "sign-in-tip": "უკვე გაქვთ ანგარიში?", "sign-up-tip": "ჯერ არ გაქვთ ანგარიში?"}, "common": {"about": "შესახებ", "add": "დამატება", "admin": "ადმინი", "archive": "არქივი", "archived": "დაარქივებული", "avatar": "ავატარი", "basic": "ძირითადი", "beta": "ბეტა", "cancel": "გაუქმება", "change": "ცვლილება", "clear": "გასუფთავება", "close": "დახურვა", "collapse": "შეკუმშვა", "confirm": "დადასტურება", "create": "შექმნა", "database": "მონაცემთა ბაზა", "days": "დღეები", "delete": "წაშლა", "description": "აღწერა", "edit": "რედაქტირება", "email": "ელ.ფოსტა", "expand": "გაფართოება", "explore": "კვლევა", "file": "ფაილი", "filter": "ფილტრი", "home": "მთავარი", "image": "სურათი", "inbox": "ინბოქსი", "language": "ენა", "learn-more": "გაიგეთ მეტი", "link": "ლინკი", "mark": "მონიშვნა", "memos": "მემოები", "name": "სახელი", "new": "ახალი", "nickname": "მეტსახელი", "null": "NULL", "or": "ან", "password": "პაროლი", "pin": "პინი", "pinned": "პინული", "preview": "წინასწარი ნახვა", "profile": "პროფილი", "remember-me": "დამიმახსოვრე", "rename": "გადარქმევა", "reset": "გადატვირთვა", "resources": "რესურსები", "restore": "აღდგენა", "role": "როლი", "save": "შენახვა", "search": "ძიება", "select": "არჩევა", "settings": "პარამეტრები", "share": "გაზიარება", "sign-in": "შესვლა", "sign-in-with": "შედით {{provider}}-ით", "sign-out": "გამოსვლა", "sign-up": "რეგისტრაცია", "statistics": "სტატისტიკა", "tags": "თეგები", "title": "სათაური", "type": "ტიპი", "unpin": "პინიდან ამოღება", "update": "განახლება", "upload": "ატვირთვა", "username": "მომხმარებლის სახელი", "version": "ვერსია", "visibility": "ხილვადობა", "yourself": "თქვენ"}, "days": {"fri": "პარ", "mon": "ორშ", "sat": "შაბ", "sun": "კვი", "thu": "ხუთ", "tue": "სამ", "wed": "ოთხ"}, "editor": {"add-your-comment-here": "დაამატეთ თქვენი კომენტარი აქ...", "any-thoughts": "რამე იდეები...", "save": "შენახვა"}, "inbox": {"memo-comment": "{{user}}-მა კომენტარი გაუკეთა თქვენს {{memo}}-ს.", "version-update": "ახალი ვერსია {{version}} უკვე ხელმისაწვდომია!"}, "memo": {"archived-at": "დაარქივებულია", "code": "კოდი", "comment": {"self": "კომენტარები", "write-a-comment": "კომენტარის დაწერა"}, "copy-link": "ლინკის კოპირება", "count-memos-in-date": "{{count}} მემოები {{date}}-ში", "delete-confirm": "დარწმუნებული ხართ, რომ გინდათ ამ მემოს წაშლა? ეს ქმედება შეუქცევადია", "links": "ლინკები", "load-more": "მეტის ჩატვირთვა", "no-archived-memos": "დაარქივებული მემოები არ არის.", "search-placeholder": "მემოების ძიება", "show-less": "ნაკლების ჩვენება", "show-more": "მეტის ჩვენება", "to-do": "სამოქმედო", "view-detail": "დეტალების ნახვა", "visibility": {"disabled": "საჯარო მემოები გამორთულია", "private": "პირადი", "protected": "სამუშაო სივრცე", "public": "საჯარო"}}, "message": {"archived-successfully": "წარმატებით დაარქივდა", "change-memo-created-time": "მემოს შექმნის დროის შეცვლა", "copied": "კოპირებული", "deleted-successfully": "წარმატებით წაიშალა", "fill-all": "გთხოვთ შეავსოთ ყველა ველი.", "maximum-upload-size-is": "მაქსიმალური ატვირთვის ზომა არის {{size}} MiB", "memo-not-found": "მემო ვერ მოიძებნა.", "new-password-not-match": "ახალი პაროლები არ ემთხვევა.", "no-data": "მონაცემები არ მოიძებნა.", "password-changed": "პაროლი შეიცვალა", "password-not-match": "პაროლები არ ემთხვევა.", "restored-successfully": "წარმატებით აღდგა", "succeed-copy-link": "ლინკი წარმატებით კოპირდა.", "update-succeed": "განახლება წარმატებით დასრულდა", "user-not-found": "მომხმარებელი ვერ მოიძებნა"}, "reference": {"add-references": "რეფერენციის დამატება", "embedded-usage": "გამოყენება ჩასმულ კონტენტად", "no-memos-found": "მემოები ვერ მოიძებნა", "search-placeholder": "კონტენტის ძიება"}, "resource": {"clear": "გასუფთავება", "copy-link": "ლინკის კოპირება", "create-dialog": {"external-link": {"file-name": "ფაილის სახელი", "file-name-placeholder": "ფაილის სახელი", "link": "ლინკი", "link-placeholder": "https://the.link.to/your/resource", "option": "გარე ლინკი", "type": "ტიპი", "type-placeholder": "ფაილის ტიპი"}, "local-file": {"choose": "აირჩიეთ ფაილი...", "option": "ლოკალური ფაილი"}, "title": "რესურსის შექმნა", "upload-method": "ატვირთვის მეთოდი"}, "delete-resource": "რესურსის წაშლა", "delete-selected-resources": "არჩეული რესურსების წაშლა", "fetching-data": "მონაცემების ჩატვირთვა...", "file-drag-drop-prompt": "გადმოაგდეთ ფაილი აქ, რომ ატვირთოთ", "linked-amount": "დაკავშირებული რაოდენობა", "no-files-selected": "არცერთი ფაილი არჩეული არ არის", "no-resources": "რესურსები არ არის.", "no-unused-resources": "გამოუყენებელი რესურსები არ არის", "reset-link": "ლინკის გადატვირთვა", "reset-link-prompt": "დარწმუნებული ხართ, რომ გინდათ ლინკის გადატვირთვა? ეს გაანადგურებს ყველა მიმდინარე ლინკის გამოყენებას. ეს ქმედება შეუქცევადია", "reset-resource-link": "რესურსის ლინკის გადატვირთვა"}, "router": {"back-to-top": "ზემოთ დაბრუნება", "go-to-home": "მთავარზე გადასვლა"}, "setting": {"account-section": {"change-password": "პაროლის შეცვლა", "email-note": "არასავალდებულო", "export-memos": "მემოების ექსპორტი", "nickname-note": "გამოჩენილი ბანერზე", "openapi-reset": "OpenAPI გასაღების გადატვირთვა", "openapi-sample-post": "გამარჯობა #მემოები {{url}}-დან", "openapi-title": "OpenAPI", "reset-api": "API-ს გადატვირთვა", "title": "ანგარიშის ინფორმაცია", "update-information": "ინფორმაციის განახლება", "username-note": "გამოიყენება შესასვლელად"}, "appearance-option": {"dark": "ყოველთვის ბნელი", "light": "ყოველთვის ნათელი", "system": "მიჰყვეს სისტემას"}, "member": "წევრი", "member-list": "წევრების სია", "member-section": {"archive-member": "წევრის არქივირება", "archive-warning": "დარწმუნებული ხართ, რომ გინდათ {{username}}-ის არქივირება?", "create-a-member": "წევრის შექმნა", "delete-member": "წევრის წაშლა", "delete-warning": "დარწმუნებული ხართ, რომ გინდათ {{username}}-ის წაშლა? ეს ქმედება შეუქცევადია"}, "memo-related": "მემო", "my-account": "ჩემი ანგარიში", "preference": "პარამეტრები", "preference-section": {"default-memo-sort-option": "მემოს ჩვენების დრო", "default-memo-visibility": "მემოს ნაგულისხმევი ხილვადობა", "theme": "თემა"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "ავტორიზაციის წერტილი", "client-id": "კლიენტის ID", "client-secret": "კლიენტის საიდუმლო", "confirm-delete": "დარწმუნებული ხართ, რომ გინდათ \"{{name}}\" SSO კონფიგურაციის წაშლა? ეს ქმედება შეუქცევადია", "create-sso": "SSO-ს შექმნა", "custom": "მორგებული", "delete-sso": "წაშლის დადასტურება", "disabled-password-login-warning": "პაროლის-შესვლის ფუნქცია გამორთულია, ყურადღებით იყავით იდენტობის პროვაიდერების წაშლისას", "display-name": "გამოჩენილი სახელი", "identifier": "იდენტიფიკატორი", "identifier-filter": "იდენტიფიკატორის ფილტრი", "redirect-url": "გადამისამართების URL", "scopes": "სკოპები", "sso-created": "SSO {{name}} შეიქმნა", "sso-list": "SSO სი<PERSON>", "sso-updated": "SSO {{name}} განახლდა", "template": "შაბლონი", "token-endpoint": "ტოკენის წერტილი", "update-sso": "SSO-ს განახლება", "user-endpoint": "მომხმარებლის წერტილი"}, "storage": "შენახვა", "storage-section": {"accesskey": "წვდომის გასაღები", "accesskey-placeholder": "წვდომის გასაღები / წვდომის ID", "bucket": "ბაკეტი", "bucket-placeholder": "ბაკეტის სახელი", "create-a-service": "სერვისის შექმნა", "create-storage": "შენახვის შექმნა", "current-storage": "მიმდინარე ობიექტის შენახვა", "delete-storage": "შენახვის წაშლა", "endpoint": "წერტილი", "local-storage-path": "ლოკალური შენახვის გზა", "path": "შენახვის გზა", "path-description": "შეგიძლიათ გამოიყენოთ იგივე დინამიური ცვლადები, როგორც ლოკალური შენახვისთვის, როგორიცაა {filename}", "path-placeholder": "მორგებული/გზა", "presign-placeholder": "წინასწარი ხელმოწერის URL, არასავალდებულო", "region": "რეგიონი", "region-placeholder": "რეგიონის სახელი", "s3-compatible-url": "S3 თავსებადი URL", "secretkey": "საიდუმლო გასაღები", "secretkey-placeholder": "საიდუმლო გასაღები / წვდომის გასაღები", "storage-services": "შენახვის სერვისები", "type-database": "მონაცემთა ბაზა", "type-local": "ლოკალური ფაილური სისტემა", "update-a-service": "სერვისის განახლება", "update-local-path": "ლოკალური შენახვის გზის განახლება", "update-local-path-description": "ლოკალური შენახვის გზა არის თქვენი მონაცემთა ბაზის ფაილის მიმართებითი გზა", "update-storage": "შენახვის განახლება", "url-prefix": "URL პრეფიქსი", "url-prefix-placeholder": "მორგებული URL პრეფიქსი, არასავალდებულო", "url-suffix": "URL სუფიქსი", "url-suffix-placeholder": "მორგებული URL სუფიქსი, არასავალდებულო", "warning-text": "დარწმუნებული ხართ, რომ გინდათ „{{name}}“ შენახვის სერვისის წაშლა? ეს ქმედება შეუქცევადია"}, "system": "სისტემა", "system-section": {"additional-script": "დამატებითი სკრიპტი", "additional-script-placeholder": "დამატებითი JavaScript კოდი", "additional-style": "დამატებითი სტილი", "additional-style-placeholder": "დამატებითი CSS კოდი", "allow-user-signup": "მომხმარებლის რეგისტრაციის ნებართვა", "customize-server": {"appearance": "სერვერის გამოჩენა", "description": "აღწერა", "icon-url": "ხატულას URL", "locale": "სერვერის ლოკალიზაცია", "title": "სერვერის მორგება"}, "disable-password-login": "პაროლის-შესვლის ფუნქციის გამორთვა", "disable-password-login-final-warning": "გთხოვთ აკრიფოთ „CONFIRM“, თუ იცით, რას აკეთებთ.", "disable-password-login-warning": "ეს გამორთავს პაროლის შესვლას ყველა მომხმარებლისთვის. შეუძლებელია შესვლა ამ პარამეტრის მონაცემთა ბაზაში დაბრუნების გარეშე, თუ თქვენი კონფიგურირებული იდენტობის პროვაიდერები ვერ იმუშავებენ. ასევე უნდა იყოთ ყურადღებით, როდესაც იდენტობის პროვაიდერს წაშლით", "disable-public-memos": "საჯარო მემოების გამორთვა", "display-with-updated-time": "განახლებული დროით ჩვენება", "enable-auto-compact": "ავტომატური კომპაქტირების ჩართვა", "enable-double-click-to-edit": "ორმაგი დაწკაპუნებით რედაქტირების ჩართვა", "enable-password-login": "პაროლით შესვლის ჩართვა", "enable-password-login-warning": "ეს ჩართავს პაროლით შესვლას ყველა მომხმარებლისთვის. გააგრძელეთ მხოლოდ იმ შემთხვევაში, თუ გინდათ, რომ მომხმარებლებს შეეძლოთ შესვლა როგორც SSO-ს, ასევე პაროლით", "max-upload-size": "მაქსიმალური ატვირთვის ზომა (MiB)", "max-upload-size-hint": "რეკომენდირებული ზომა არის 32 MiB.", "server-name": "სერვერის სახელი"}}, "tag": {"all-tags": "ყველა თეგი", "create-tag": "თეგის შექმნა", "create-tags-guide": "თეგების შექმნა შეგიძლიათ `#tag`-ის შეყვანით.", "delete-confirm": "დარწმუნებული ხართ, რომ გინდათ ამ თეგის წაშლა? ყველა დაკავშირებული მემო არქივირდება.", "delete-tag": "თეგის წაშლა", "no-tag-found": "თეგი ვერ მოიძებნა"}}