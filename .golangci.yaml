version: "2"

linters:
  enable:
    - revive
    - govet
    - staticcheck
    - misspell
    - gocritic
    - sqlclosecheck
    - rowserrcheck
    - nilerr
    - godot
    - forbidigo
    - mirror
    - bodyclose
  disable:
    - errcheck
  settings:
    exhaustive:
      explicit-exhaustive-switch: false
    staticcheck:
      checks:
        - all
        - -ST1000
        - -ST1003
        - -ST1021
        - -QF1003
    revive:
      # Default to run all linters so that new rules in the future could automatically be added to the static check.
      enable-all-rules: true
      rules:
        # The following rules are too strict and make coding harder. We do not enable them for now.
        - name: file-header
          disabled: true
        - name: line-length-limit
          disabled: true
        - name: function-length
          disabled: true
        - name: max-public-structs
          disabled: true
        - name: function-result-limit
          disabled: true
        - name: banned-characters
          disabled: true
        - name: argument-limit
          disabled: true
        - name: cognitive-complexity
          disabled: true
        - name: cyclomatic
          disabled: true
        - name: confusing-results
          disabled: true
        - name: add-constant
          disabled: true
        - name: flag-parameter
          disabled: true
        - name: nested-structs
          disabled: true
        - name: import-shadowing
          disabled: true
        - name: early-return
          disabled: true
        - name: use-any
          disabled: true
        - name: exported
          disabled: true
        - name: unhandled-error
          disabled: true
        - name: if-return
          disabled: true
        - name: max-control-nesting
          disabled: true
        - name: redefines-builtin-id
          disabled: true
        - name: package-comments
          disabled: true
    gocritic:
      disabled-checks:
        - ifElseChain
    govet:
      settings:
        printf: # The name of the analyzer, run `go tool vet help` to see the list of all analyzers
          funcs: # Run `go tool vet help printf` to see the full configuration of `printf`.
            - common.Errorf
      enable-all: true
      disable:
        - fieldalignment
        - shadow
    forbidigo:
      forbid:
        - pattern: 'fmt\.Errorf(# Please use errors\.Wrap\|Wrapf\|Errorf instead)?'
        - pattern: 'ioutil\.ReadDir(# Please use os\.ReadDir)?'

formatters:
  enable:
    - goimports
  settings:
    goimports:
      local-prefixes:
        - github.com/usememos/memos
