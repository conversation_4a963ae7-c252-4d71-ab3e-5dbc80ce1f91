// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/webhook_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Webhook struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the webhook.
	// Format: users/{user}/webhooks/{webhook}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The display name of the webhook.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// The target URL for the webhook.
	Url           string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Webhook) Reset() {
	*x = Webhook{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Webhook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Webhook) ProtoMessage() {}

func (x *Webhook) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Webhook.ProtoReflect.Descriptor instead.
func (*Webhook) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{0}
}

func (x *Webhook) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Webhook) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Webhook) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ListWebhooksRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource where webhooks are listed.
	// Format: users/{user}
	Parent        string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWebhooksRequest) Reset() {
	*x = ListWebhooksRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWebhooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksRequest) ProtoMessage() {}

func (x *ListWebhooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListWebhooksRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

type ListWebhooksResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of webhooks.
	Webhooks      []*Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWebhooksResponse) Reset() {
	*x = ListWebhooksResponse{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWebhooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksResponse) ProtoMessage() {}

func (x *ListWebhooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListWebhooksResponse) GetWebhooks() []*Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

type GetWebhookRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the webhook to retrieve.
	// Format: users/{user}/webhooks/{webhook}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWebhookRequest) Reset() {
	*x = GetWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookRequest) ProtoMessage() {}

func (x *GetWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetWebhookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreateWebhookRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The parent resource where this webhook will be created.
	// Format: users/{user}
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The webhook to create.
	Webhook *Webhook `protobuf:"bytes,2,opt,name=webhook,proto3" json:"webhook,omitempty"`
	// Optional. If set, validate the request, but do not actually create the webhook.
	ValidateOnly  bool `protobuf:"varint,3,opt,name=validate_only,json=validateOnly,proto3" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWebhookRequest) Reset() {
	*x = CreateWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWebhookRequest) ProtoMessage() {}

func (x *CreateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWebhookRequest.ProtoReflect.Descriptor instead.
func (*CreateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateWebhookRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateWebhookRequest) GetWebhook() *Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

func (x *CreateWebhookRequest) GetValidateOnly() bool {
	if x != nil {
		return x.ValidateOnly
	}
	return false
}

type UpdateWebhookRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The webhook resource which replaces the resource on the server.
	Webhook *Webhook `protobuf:"bytes,1,opt,name=webhook,proto3" json:"webhook,omitempty"`
	// Optional. The list of fields to update.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWebhookRequest) Reset() {
	*x = UpdateWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookRequest) ProtoMessage() {}

func (x *UpdateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookRequest.ProtoReflect.Descriptor instead.
func (*UpdateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWebhookRequest) GetWebhook() *Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

func (x *UpdateWebhookRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteWebhookRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The resource name of the webhook to delete.
	// Format: users/{user}/webhooks/{webhook}
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWebhookRequest) Reset() {
	*x = DeleteWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWebhookRequest) ProtoMessage() {}

func (x *DeleteWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWebhookRequest.ProtoReflect.Descriptor instead.
func (*DeleteWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteWebhookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_webhook_service_proto protoreflect.FileDescriptor

const file_api_v1_webhook_service_proto_rawDesc = "" +
	"\n" +
	"\x1capi/v1/webhook_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\"\xb0\x01\n" +
	"\aWebhook\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\bR\x04name\x12&\n" +
	"\fdisplay_name\x18\x02 \x01(\tB\x03\xe0A\x02R\vdisplayName\x12\x15\n" +
	"\x03url\x18\x03 \x01(\tB\x03\xe0A\x02R\x03url:M\xeaAJ\n" +
	"\x14memos.api.v1/Webhook\x12\x1fusers/{user}/webhooks/{webhook}*\bwebhooks2\awebhook\"K\n" +
	"\x13ListWebhooksRequest\x124\n" +
	"\x06parent\x18\x01 \x01(\tB\x1c\xe0A\x02\xfaA\x16\x12\x14memos.api.v1/WebhookR\x06parent\"I\n" +
	"\x14ListWebhooksResponse\x121\n" +
	"\bwebhooks\x18\x01 \x03(\v2\x15.memos.api.v1.WebhookR\bwebhooks\"E\n" +
	"\x11GetWebhookRequest\x120\n" +
	"\x04name\x18\x01 \x01(\tB\x1c\xe0A\x02\xfaA\x16\n" +
	"\x14memos.api.v1/WebhookR\x04name\"\xac\x01\n" +
	"\x14CreateWebhookRequest\x124\n" +
	"\x06parent\x18\x01 \x01(\tB\x1c\xe0A\x02\xfaA\x16\x12\x14memos.api.v1/WebhookR\x06parent\x124\n" +
	"\awebhook\x18\x02 \x01(\v2\x15.memos.api.v1.WebhookB\x03\xe0A\x02R\awebhook\x12(\n" +
	"\rvalidate_only\x18\x03 \x01(\bB\x03\xe0A\x01R\fvalidateOnly\"\x8e\x01\n" +
	"\x14UpdateWebhookRequest\x124\n" +
	"\awebhook\x18\x01 \x01(\v2\x15.memos.api.v1.WebhookB\x03\xe0A\x02R\awebhook\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x01R\n" +
	"updateMask\"H\n" +
	"\x14DeleteWebhookRequest\x120\n" +
	"\x04name\x18\x01 \x01(\tB\x1c\xe0A\x02\xfaA\x16\n" +
	"\x14memos.api.v1/WebhookR\x04name2\xc4\x05\n" +
	"\x0eWebhookService\x12\x89\x01\n" +
	"\fListWebhooks\x12!.memos.api.v1.ListWebhooksRequest\x1a\".memos.api.v1.ListWebhooksResponse\"2\xdaA\x06parent\x82\xd3\xe4\x93\x02#\x12!/api/v1/{parent=users/*}/webhooks\x12v\n" +
	"\n" +
	"GetWebhook\x12\x1f.memos.api.v1.GetWebhookRequest\x1a\x15.memos.api.v1.Webhook\"0\xdaA\x04name\x82\xd3\xe4\x93\x02#\x12!/api/v1/{name=users/*/webhooks/*}\x12\x8f\x01\n" +
	"\rCreateWebhook\x12\".memos.api.v1.CreateWebhookRequest\x1a\x15.memos.api.v1.Webhook\"C\xdaA\x0eparent,webhook\x82\xd3\xe4\x93\x02,:\awebhook\"!/api/v1/{parent=users/*}/webhooks\x12\x9c\x01\n" +
	"\rUpdateWebhook\x12\".memos.api.v1.UpdateWebhookRequest\x1a\x15.memos.api.v1.Webhook\"P\xdaA\x13webhook,update_mask\x82\xd3\xe4\x93\x024:\awebhook2)/api/v1/{webhook.name=users/*/webhooks/*}\x12}\n" +
	"\rDeleteWebhook\x12\".memos.api.v1.DeleteWebhookRequest\x1a\x16.google.protobuf.Empty\"0\xdaA\x04name\x82\xd3\xe4\x93\x02#*!/api/v1/{name=users/*/webhooks/*}B\xab\x01\n" +
	"\x10com.memos.api.v1B\x13WebhookServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_webhook_service_proto_rawDescOnce sync.Once
	file_api_v1_webhook_service_proto_rawDescData []byte
)

func file_api_v1_webhook_service_proto_rawDescGZIP() []byte {
	file_api_v1_webhook_service_proto_rawDescOnce.Do(func() {
		file_api_v1_webhook_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_webhook_service_proto_rawDesc), len(file_api_v1_webhook_service_proto_rawDesc)))
	})
	return file_api_v1_webhook_service_proto_rawDescData
}

var file_api_v1_webhook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_v1_webhook_service_proto_goTypes = []any{
	(*Webhook)(nil),               // 0: memos.api.v1.Webhook
	(*ListWebhooksRequest)(nil),   // 1: memos.api.v1.ListWebhooksRequest
	(*ListWebhooksResponse)(nil),  // 2: memos.api.v1.ListWebhooksResponse
	(*GetWebhookRequest)(nil),     // 3: memos.api.v1.GetWebhookRequest
	(*CreateWebhookRequest)(nil),  // 4: memos.api.v1.CreateWebhookRequest
	(*UpdateWebhookRequest)(nil),  // 5: memos.api.v1.UpdateWebhookRequest
	(*DeleteWebhookRequest)(nil),  // 6: memos.api.v1.DeleteWebhookRequest
	(*fieldmaskpb.FieldMask)(nil), // 7: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),         // 8: google.protobuf.Empty
}
var file_api_v1_webhook_service_proto_depIdxs = []int32{
	0, // 0: memos.api.v1.ListWebhooksResponse.webhooks:type_name -> memos.api.v1.Webhook
	0, // 1: memos.api.v1.CreateWebhookRequest.webhook:type_name -> memos.api.v1.Webhook
	0, // 2: memos.api.v1.UpdateWebhookRequest.webhook:type_name -> memos.api.v1.Webhook
	7, // 3: memos.api.v1.UpdateWebhookRequest.update_mask:type_name -> google.protobuf.FieldMask
	1, // 4: memos.api.v1.WebhookService.ListWebhooks:input_type -> memos.api.v1.ListWebhooksRequest
	3, // 5: memos.api.v1.WebhookService.GetWebhook:input_type -> memos.api.v1.GetWebhookRequest
	4, // 6: memos.api.v1.WebhookService.CreateWebhook:input_type -> memos.api.v1.CreateWebhookRequest
	5, // 7: memos.api.v1.WebhookService.UpdateWebhook:input_type -> memos.api.v1.UpdateWebhookRequest
	6, // 8: memos.api.v1.WebhookService.DeleteWebhook:input_type -> memos.api.v1.DeleteWebhookRequest
	2, // 9: memos.api.v1.WebhookService.ListWebhooks:output_type -> memos.api.v1.ListWebhooksResponse
	0, // 10: memos.api.v1.WebhookService.GetWebhook:output_type -> memos.api.v1.Webhook
	0, // 11: memos.api.v1.WebhookService.CreateWebhook:output_type -> memos.api.v1.Webhook
	0, // 12: memos.api.v1.WebhookService.UpdateWebhook:output_type -> memos.api.v1.Webhook
	8, // 13: memos.api.v1.WebhookService.DeleteWebhook:output_type -> google.protobuf.Empty
	9, // [9:14] is the sub-list for method output_type
	4, // [4:9] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_v1_webhook_service_proto_init() }
func file_api_v1_webhook_service_proto_init() {
	if File_api_v1_webhook_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_webhook_service_proto_rawDesc), len(file_api_v1_webhook_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_webhook_service_proto_goTypes,
		DependencyIndexes: file_api_v1_webhook_service_proto_depIdxs,
		MessageInfos:      file_api_v1_webhook_service_proto_msgTypes,
	}.Build()
	File_api_v1_webhook_service_proto = out.File
	file_api_v1_webhook_service_proto_goTypes = nil
	file_api_v1_webhook_service_proto_depIdxs = nil
}
