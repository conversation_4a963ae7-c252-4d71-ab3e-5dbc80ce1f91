// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/user_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { HttpBody } from "../../google/api/httpbody";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";
import { State, stateFromJSON, stateToNumber } from "./common";

export const protobufPackage = "memos.api.v1";

export interface User {
  /**
   * The resource name of the user.
   * Format: users/{user}
   */
  name: string;
  /** The role of the user. */
  role: User_Role;
  /** Required. The unique username for login. */
  username: string;
  /** Optional. The email address of the user. */
  email: string;
  /** Optional. The display name of the user. */
  displayName: string;
  /** Optional. The avatar URL of the user. */
  avatarUrl: string;
  /** Optional. The description of the user. */
  description: string;
  /** Input only. The password for the user. */
  password: string;
  /** The state of the user. */
  state: State;
  /** Output only. The creation timestamp. */
  createTime?:
    | Date
    | undefined;
  /** Output only. The last update timestamp. */
  updateTime?: Date | undefined;
}

/** User role enumeration. */
export enum User_Role {
  /** ROLE_UNSPECIFIED - Unspecified role. */
  ROLE_UNSPECIFIED = "ROLE_UNSPECIFIED",
  /** HOST - Host role with full system access. */
  HOST = "HOST",
  /** ADMIN - Admin role with administrative privileges. */
  ADMIN = "ADMIN",
  /** USER - Regular user role. */
  USER = "USER",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function user_RoleFromJSON(object: any): User_Role {
  switch (object) {
    case 0:
    case "ROLE_UNSPECIFIED":
      return User_Role.ROLE_UNSPECIFIED;
    case 1:
    case "HOST":
      return User_Role.HOST;
    case 2:
    case "ADMIN":
      return User_Role.ADMIN;
    case 3:
    case "USER":
      return User_Role.USER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return User_Role.UNRECOGNIZED;
  }
}

export function user_RoleToNumber(object: User_Role): number {
  switch (object) {
    case User_Role.ROLE_UNSPECIFIED:
      return 0;
    case User_Role.HOST:
      return 1;
    case User_Role.ADMIN:
      return 2;
    case User_Role.USER:
      return 3;
    case User_Role.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface ListUsersRequest {
  /**
   * Optional. The maximum number of users to return.
   * The service may return fewer than this value.
   * If unspecified, at most 50 users will be returned.
   * The maximum value is 1000; values above 1000 will be coerced to 1000.
   */
  pageSize: number;
  /**
   * Optional. A page token, received from a previous `ListUsers` call.
   * Provide this to retrieve the subsequent page.
   */
  pageToken: string;
  /**
   * Optional. Filter to apply to the list results.
   * Example: "state=ACTIVE" or "role=USER" or "email:@example.com"
   * Supported operators: =, !=, <, <=, >, >=, :
   * Supported fields: username, email, role, state, create_time, update_time
   */
  filter: string;
  /**
   * Optional. The order to sort results by.
   * Example: "create_time desc" or "username asc"
   */
  orderBy: string;
  /** Optional. If true, show deleted users in the response. */
  showDeleted: boolean;
}

export interface ListUsersResponse {
  /** The list of users. */
  users: User[];
  /**
   * A token that can be sent as `page_token` to retrieve the next page.
   * If this field is omitted, there are no subsequent pages.
   */
  nextPageToken: string;
  /** The total count of users (may be approximate). */
  totalSize: number;
}

export interface GetUserRequest {
  /**
   * Required. The resource name of the user.
   * Format: users/{user}
   */
  name: string;
  /**
   * Optional. The fields to return in the response.
   * If not specified, all fields are returned.
   */
  readMask?: string[] | undefined;
}

export interface CreateUserRequest {
  /** Required. The user to create. */
  user?:
    | User
    | undefined;
  /**
   * Optional. The user ID to use for this user.
   * If empty, a unique ID will be generated.
   * Must match the pattern [a-z0-9-]+
   */
  userId: string;
  /** Optional. If set, validate the request but don't actually create the user. */
  validateOnly: boolean;
  /**
   * Optional. An idempotency token that can be used to ensure that multiple
   * requests to create a user have the same result.
   */
  requestId: string;
}

export interface UpdateUserRequest {
  /** Required. The user to update. */
  user?:
    | User
    | undefined;
  /** Required. The list of fields to update. */
  updateMask?:
    | string[]
    | undefined;
  /** Optional. If set to true, allows updating sensitive fields. */
  allowMissing: boolean;
}

export interface DeleteUserRequest {
  /**
   * Required. The resource name of the user to delete.
   * Format: users/{user}
   */
  name: string;
  /** Optional. If set to true, the user will be deleted even if they have associated data. */
  force: boolean;
}

export interface SearchUsersRequest {
  /** Required. The search query. */
  query: string;
  /** Optional. The maximum number of users to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface SearchUsersResponse {
  /** The list of users matching the search query. */
  users: User[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of matching users. */
  totalSize: number;
}

export interface GetUserAvatarRequest {
  /**
   * Required. The resource name of the user.
   * Format: users/{user}
   */
  name: string;
}

/** User statistics messages */
export interface UserStats {
  /**
   * The resource name of the user whose stats these are.
   * Format: users/{user}
   */
  name: string;
  /** The timestamps when the memos were displayed. */
  memoDisplayTimestamps: Date[];
  /** The stats of memo types. */
  memoTypeStats?:
    | UserStats_MemoTypeStats
    | undefined;
  /** The count of tags. */
  tagCount: { [key: string]: number };
  /** The pinned memos of the user. */
  pinnedMemos: string[];
  /** Total memo count. */
  totalMemoCount: number;
}

export interface UserStats_TagCountEntry {
  key: string;
  value: number;
}

/** Memo type statistics. */
export interface UserStats_MemoTypeStats {
  linkCount: number;
  codeCount: number;
  todoCount: number;
  undoCount: number;
}

export interface GetUserStatsRequest {
  /**
   * Required. The resource name of the user.
   * Format: users/{user}
   */
  name: string;
}

/** User settings message */
export interface UserSetting {
  /**
   * The resource name of the user whose setting this is.
   * Format: users/{user}
   */
  name: string;
  /** The preferred locale of the user. */
  locale: string;
  /** The preferred appearance of the user. */
  appearance: string;
  /** The default visibility of the memo. */
  memoVisibility: string;
}

export interface GetUserSettingRequest {
  /**
   * Required. The resource name of the user.
   * Format: users/{user}
   */
  name: string;
}

export interface UpdateUserSettingRequest {
  /** Required. The user setting to update. */
  setting?:
    | UserSetting
    | undefined;
  /** Required. The list of fields to update. */
  updateMask?: string[] | undefined;
}

/** User access token message */
export interface UserAccessToken {
  /**
   * The resource name of the access token.
   * Format: users/{user}/accessTokens/{access_token}
   */
  name: string;
  /** Output only. The access token value. */
  accessToken: string;
  /** The description of the access token. */
  description: string;
  /** Output only. The issued timestamp. */
  issuedAt?:
    | Date
    | undefined;
  /** Optional. The expiration timestamp. */
  expiresAt?: Date | undefined;
}

export interface ListUserAccessTokensRequest {
  /**
   * Required. The parent resource whose access tokens will be listed.
   * Format: users/{user}
   */
  parent: string;
  /** Optional. The maximum number of access tokens to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface ListUserAccessTokensResponse {
  /** The list of access tokens. */
  accessTokens: UserAccessToken[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of access tokens. */
  totalSize: number;
}

export interface CreateUserAccessTokenRequest {
  /**
   * Required. The parent resource where this access token will be created.
   * Format: users/{user}
   */
  parent: string;
  /** Required. The access token to create. */
  accessToken?:
    | UserAccessToken
    | undefined;
  /** Optional. The access token ID to use. */
  accessTokenId: string;
}

export interface DeleteUserAccessTokenRequest {
  /**
   * Required. The resource name of the access token to delete.
   * Format: users/{user}/accessTokens/{access_token}
   */
  name: string;
}

export interface UserSession {
  /**
   * The resource name of the session.
   * Format: users/{user}/sessions/{session}
   */
  name: string;
  /** The session ID. */
  sessionId: string;
  /** The timestamp when the session was created. */
  createTime?:
    | Date
    | undefined;
  /**
   * The timestamp when the session was last accessed.
   * Used for sliding expiration calculation (last_accessed_time + 2 weeks).
   */
  lastAccessedTime?:
    | Date
    | undefined;
  /** Client information associated with this session. */
  clientInfo?: UserSession_ClientInfo | undefined;
}

export interface UserSession_ClientInfo {
  /** User agent string of the client. */
  userAgent: string;
  /** IP address of the client. */
  ipAddress: string;
  /** Optional. Device type (e.g., "mobile", "desktop", "tablet"). */
  deviceType: string;
  /** Optional. Operating system (e.g., "iOS 17.0", "Windows 11"). */
  os: string;
  /** Optional. Browser name and version (e.g., "Chrome 119.0"). */
  browser: string;
}

export interface ListUserSessionsRequest {
  /**
   * Required. The resource name of the parent.
   * Format: users/{user}
   */
  parent: string;
}

export interface ListUserSessionsResponse {
  /** The list of user sessions. */
  sessions: UserSession[];
}

export interface RevokeUserSessionRequest {
  /**
   * Required. The resource name of the session to revoke.
   * Format: users/{user}/sessions/{session}
   */
  name: string;
}

export interface ListAllUserStatsRequest {
  /** Optional. The maximum number of user stats to return. */
  pageSize: number;
  /** Optional. A page token for pagination. */
  pageToken: string;
}

export interface ListAllUserStatsResponse {
  /** The list of user statistics. */
  userStats: UserStats[];
  /** A token for the next page of results. */
  nextPageToken: string;
  /** The total count of user statistics. */
  totalSize: number;
}

function createBaseUser(): User {
  return {
    name: "",
    role: User_Role.ROLE_UNSPECIFIED,
    username: "",
    email: "",
    displayName: "",
    avatarUrl: "",
    description: "",
    password: "",
    state: State.STATE_UNSPECIFIED,
    createTime: undefined,
    updateTime: undefined,
  };
}

export const User: MessageFns<User> = {
  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.role !== User_Role.ROLE_UNSPECIFIED) {
      writer.uint32(16).int32(user_RoleToNumber(message.role));
    }
    if (message.username !== "") {
      writer.uint32(26).string(message.username);
    }
    if (message.email !== "") {
      writer.uint32(34).string(message.email);
    }
    if (message.displayName !== "") {
      writer.uint32(42).string(message.displayName);
    }
    if (message.avatarUrl !== "") {
      writer.uint32(50).string(message.avatarUrl);
    }
    if (message.description !== "") {
      writer.uint32(58).string(message.description);
    }
    if (message.password !== "") {
      writer.uint32(66).string(message.password);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(72).int32(stateToNumber(message.state));
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(82).fork()).join();
    }
    if (message.updateTime !== undefined) {
      Timestamp.encode(toTimestamp(message.updateTime), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): User {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.role = user_RoleFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.displayName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.avatarUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.updateTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<User>): User {
    return User.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<User>): User {
    const message = createBaseUser();
    message.name = object.name ?? "";
    message.role = object.role ?? User_Role.ROLE_UNSPECIFIED;
    message.username = object.username ?? "";
    message.email = object.email ?? "";
    message.displayName = object.displayName ?? "";
    message.avatarUrl = object.avatarUrl ?? "";
    message.description = object.description ?? "";
    message.password = object.password ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.createTime = object.createTime ?? undefined;
    message.updateTime = object.updateTime ?? undefined;
    return message;
  },
};

function createBaseListUsersRequest(): ListUsersRequest {
  return { pageSize: 0, pageToken: "", filter: "", orderBy: "", showDeleted: false };
}

export const ListUsersRequest: MessageFns<ListUsersRequest> = {
  encode(message: ListUsersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(18).string(message.pageToken);
    }
    if (message.filter !== "") {
      writer.uint32(26).string(message.filter);
    }
    if (message.orderBy !== "") {
      writer.uint32(34).string(message.orderBy);
    }
    if (message.showDeleted !== false) {
      writer.uint32(40).bool(message.showDeleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUsersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUsersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.orderBy = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.showDeleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUsersRequest>): ListUsersRequest {
    return ListUsersRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUsersRequest>): ListUsersRequest {
    const message = createBaseListUsersRequest();
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.filter = object.filter ?? "";
    message.orderBy = object.orderBy ?? "";
    message.showDeleted = object.showDeleted ?? false;
    return message;
  },
};

function createBaseListUsersResponse(): ListUsersResponse {
  return { users: [], nextPageToken: "", totalSize: 0 };
}

export const ListUsersResponse: MessageFns<ListUsersResponse> = {
  encode(message: ListUsersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.users) {
      User.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUsersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUsersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.users.push(User.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUsersResponse>): ListUsersResponse {
    return ListUsersResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUsersResponse>): ListUsersResponse {
    const message = createBaseListUsersResponse();
    message.users = object.users?.map((e) => User.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseGetUserRequest(): GetUserRequest {
  return { name: "", readMask: undefined };
}

export const GetUserRequest: MessageFns<GetUserRequest> = {
  encode(message: GetUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.readMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.readMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserRequest>): GetUserRequest {
    return GetUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserRequest>): GetUserRequest {
    const message = createBaseGetUserRequest();
    message.name = object.name ?? "";
    message.readMask = object.readMask ?? undefined;
    return message;
  },
};

function createBaseCreateUserRequest(): CreateUserRequest {
  return { user: undefined, userId: "", validateOnly: false, requestId: "" };
}

export const CreateUserRequest: MessageFns<CreateUserRequest> = {
  encode(message: CreateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.validateOnly !== false) {
      writer.uint32(24).bool(message.validateOnly);
    }
    if (message.requestId !== "") {
      writer.uint32(34).string(message.requestId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.validateOnly = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateUserRequest>): CreateUserRequest {
    return CreateUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateUserRequest>): CreateUserRequest {
    const message = createBaseCreateUserRequest();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    message.userId = object.userId ?? "";
    message.validateOnly = object.validateOnly ?? false;
    message.requestId = object.requestId ?? "";
    return message;
  },
};

function createBaseUpdateUserRequest(): UpdateUserRequest {
  return { user: undefined, updateMask: undefined, allowMissing: false };
}

export const UpdateUserRequest: MessageFns<UpdateUserRequest> = {
  encode(message: UpdateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    if (message.allowMissing !== false) {
      writer.uint32(24).bool(message.allowMissing);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.allowMissing = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateUserRequest>): UpdateUserRequest {
    return UpdateUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateUserRequest>): UpdateUserRequest {
    const message = createBaseUpdateUserRequest();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    message.updateMask = object.updateMask ?? undefined;
    message.allowMissing = object.allowMissing ?? false;
    return message;
  },
};

function createBaseDeleteUserRequest(): DeleteUserRequest {
  return { name: "", force: false };
}

export const DeleteUserRequest: MessageFns<DeleteUserRequest> = {
  encode(message: DeleteUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.force !== false) {
      writer.uint32(16).bool(message.force);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.force = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteUserRequest>): DeleteUserRequest {
    return DeleteUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteUserRequest>): DeleteUserRequest {
    const message = createBaseDeleteUserRequest();
    message.name = object.name ?? "";
    message.force = object.force ?? false;
    return message;
  },
};

function createBaseSearchUsersRequest(): SearchUsersRequest {
  return { query: "", pageSize: 0, pageToken: "" };
}

export const SearchUsersRequest: MessageFns<SearchUsersRequest> = {
  encode(message: SearchUsersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.query !== "") {
      writer.uint32(10).string(message.query);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchUsersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchUsersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.query = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SearchUsersRequest>): SearchUsersRequest {
    return SearchUsersRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SearchUsersRequest>): SearchUsersRequest {
    const message = createBaseSearchUsersRequest();
    message.query = object.query ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseSearchUsersResponse(): SearchUsersResponse {
  return { users: [], nextPageToken: "", totalSize: 0 };
}

export const SearchUsersResponse: MessageFns<SearchUsersResponse> = {
  encode(message: SearchUsersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.users) {
      User.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchUsersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchUsersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.users.push(User.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SearchUsersResponse>): SearchUsersResponse {
    return SearchUsersResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SearchUsersResponse>): SearchUsersResponse {
    const message = createBaseSearchUsersResponse();
    message.users = object.users?.map((e) => User.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseGetUserAvatarRequest(): GetUserAvatarRequest {
  return { name: "" };
}

export const GetUserAvatarRequest: MessageFns<GetUserAvatarRequest> = {
  encode(message: GetUserAvatarRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserAvatarRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserAvatarRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserAvatarRequest>): GetUserAvatarRequest {
    return GetUserAvatarRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserAvatarRequest>): GetUserAvatarRequest {
    const message = createBaseGetUserAvatarRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUserStats(): UserStats {
  return {
    name: "",
    memoDisplayTimestamps: [],
    memoTypeStats: undefined,
    tagCount: {},
    pinnedMemos: [],
    totalMemoCount: 0,
  };
}

export const UserStats: MessageFns<UserStats> = {
  encode(message: UserStats, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.memoDisplayTimestamps) {
      Timestamp.encode(toTimestamp(v!), writer.uint32(18).fork()).join();
    }
    if (message.memoTypeStats !== undefined) {
      UserStats_MemoTypeStats.encode(message.memoTypeStats, writer.uint32(26).fork()).join();
    }
    Object.entries(message.tagCount).forEach(([key, value]) => {
      UserStats_TagCountEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    for (const v of message.pinnedMemos) {
      writer.uint32(42).string(v!);
    }
    if (message.totalMemoCount !== 0) {
      writer.uint32(48).int32(message.totalMemoCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memoDisplayTimestamps.push(fromTimestamp(Timestamp.decode(reader, reader.uint32())));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.memoTypeStats = UserStats_MemoTypeStats.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = UserStats_TagCountEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.tagCount[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pinnedMemos.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.totalMemoCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats>): UserStats {
    return UserStats.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats>): UserStats {
    const message = createBaseUserStats();
    message.name = object.name ?? "";
    message.memoDisplayTimestamps = object.memoDisplayTimestamps?.map((e) => e) || [];
    message.memoTypeStats = (object.memoTypeStats !== undefined && object.memoTypeStats !== null)
      ? UserStats_MemoTypeStats.fromPartial(object.memoTypeStats)
      : undefined;
    message.tagCount = Object.entries(object.tagCount ?? {}).reduce<{ [key: string]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.pinnedMemos = object.pinnedMemos?.map((e) => e) || [];
    message.totalMemoCount = object.totalMemoCount ?? 0;
    return message;
  },
};

function createBaseUserStats_TagCountEntry(): UserStats_TagCountEntry {
  return { key: "", value: 0 };
}

export const UserStats_TagCountEntry: MessageFns<UserStats_TagCountEntry> = {
  encode(message: UserStats_TagCountEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats_TagCountEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats_TagCountEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats_TagCountEntry>): UserStats_TagCountEntry {
    return UserStats_TagCountEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats_TagCountEntry>): UserStats_TagCountEntry {
    const message = createBaseUserStats_TagCountEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseUserStats_MemoTypeStats(): UserStats_MemoTypeStats {
  return { linkCount: 0, codeCount: 0, todoCount: 0, undoCount: 0 };
}

export const UserStats_MemoTypeStats: MessageFns<UserStats_MemoTypeStats> = {
  encode(message: UserStats_MemoTypeStats, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.linkCount !== 0) {
      writer.uint32(8).int32(message.linkCount);
    }
    if (message.codeCount !== 0) {
      writer.uint32(16).int32(message.codeCount);
    }
    if (message.todoCount !== 0) {
      writer.uint32(24).int32(message.todoCount);
    }
    if (message.undoCount !== 0) {
      writer.uint32(32).int32(message.undoCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats_MemoTypeStats {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats_MemoTypeStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.linkCount = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.codeCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.todoCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.undoCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats_MemoTypeStats>): UserStats_MemoTypeStats {
    return UserStats_MemoTypeStats.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats_MemoTypeStats>): UserStats_MemoTypeStats {
    const message = createBaseUserStats_MemoTypeStats();
    message.linkCount = object.linkCount ?? 0;
    message.codeCount = object.codeCount ?? 0;
    message.todoCount = object.todoCount ?? 0;
    message.undoCount = object.undoCount ?? 0;
    return message;
  },
};

function createBaseGetUserStatsRequest(): GetUserStatsRequest {
  return { name: "" };
}

export const GetUserStatsRequest: MessageFns<GetUserStatsRequest> = {
  encode(message: GetUserStatsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserStatsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserStatsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserStatsRequest>): GetUserStatsRequest {
    return GetUserStatsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserStatsRequest>): GetUserStatsRequest {
    const message = createBaseGetUserStatsRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUserSetting(): UserSetting {
  return { name: "", locale: "", appearance: "", memoVisibility: "" };
}

export const UserSetting: MessageFns<UserSetting> = {
  encode(message: UserSetting, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.locale !== "") {
      writer.uint32(18).string(message.locale);
    }
    if (message.appearance !== "") {
      writer.uint32(26).string(message.appearance);
    }
    if (message.memoVisibility !== "") {
      writer.uint32(34).string(message.memoVisibility);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSetting {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSetting();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.locale = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.appearance = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.memoVisibility = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserSetting>): UserSetting {
    return UserSetting.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserSetting>): UserSetting {
    const message = createBaseUserSetting();
    message.name = object.name ?? "";
    message.locale = object.locale ?? "";
    message.appearance = object.appearance ?? "";
    message.memoVisibility = object.memoVisibility ?? "";
    return message;
  },
};

function createBaseGetUserSettingRequest(): GetUserSettingRequest {
  return { name: "" };
}

export const GetUserSettingRequest: MessageFns<GetUserSettingRequest> = {
  encode(message: GetUserSettingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserSettingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserSettingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserSettingRequest>): GetUserSettingRequest {
    return GetUserSettingRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserSettingRequest>): GetUserSettingRequest {
    const message = createBaseGetUserSettingRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUpdateUserSettingRequest(): UpdateUserSettingRequest {
  return { setting: undefined, updateMask: undefined };
}

export const UpdateUserSettingRequest: MessageFns<UpdateUserSettingRequest> = {
  encode(message: UpdateUserSettingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.setting !== undefined) {
      UserSetting.encode(message.setting, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserSettingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserSettingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.setting = UserSetting.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateUserSettingRequest>): UpdateUserSettingRequest {
    return UpdateUserSettingRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateUserSettingRequest>): UpdateUserSettingRequest {
    const message = createBaseUpdateUserSettingRequest();
    message.setting = (object.setting !== undefined && object.setting !== null)
      ? UserSetting.fromPartial(object.setting)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseUserAccessToken(): UserAccessToken {
  return { name: "", accessToken: "", description: "", issuedAt: undefined, expiresAt: undefined };
}

export const UserAccessToken: MessageFns<UserAccessToken> = {
  encode(message: UserAccessToken, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.accessToken !== "") {
      writer.uint32(18).string(message.accessToken);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.issuedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.issuedAt), writer.uint32(34).fork()).join();
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserAccessToken {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserAccessToken();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.issuedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserAccessToken>): UserAccessToken {
    return UserAccessToken.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserAccessToken>): UserAccessToken {
    const message = createBaseUserAccessToken();
    message.name = object.name ?? "";
    message.accessToken = object.accessToken ?? "";
    message.description = object.description ?? "";
    message.issuedAt = object.issuedAt ?? undefined;
    message.expiresAt = object.expiresAt ?? undefined;
    return message;
  },
};

function createBaseListUserAccessTokensRequest(): ListUserAccessTokensRequest {
  return { parent: "", pageSize: 0, pageToken: "" };
}

export const ListUserAccessTokensRequest: MessageFns<ListUserAccessTokensRequest> = {
  encode(message: ListUserAccessTokensRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserAccessTokensRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserAccessTokensRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserAccessTokensRequest>): ListUserAccessTokensRequest {
    return ListUserAccessTokensRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserAccessTokensRequest>): ListUserAccessTokensRequest {
    const message = createBaseListUserAccessTokensRequest();
    message.parent = object.parent ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListUserAccessTokensResponse(): ListUserAccessTokensResponse {
  return { accessTokens: [], nextPageToken: "", totalSize: 0 };
}

export const ListUserAccessTokensResponse: MessageFns<ListUserAccessTokensResponse> = {
  encode(message: ListUserAccessTokensResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accessTokens) {
      UserAccessToken.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserAccessTokensResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserAccessTokensResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accessTokens.push(UserAccessToken.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserAccessTokensResponse>): ListUserAccessTokensResponse {
    return ListUserAccessTokensResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserAccessTokensResponse>): ListUserAccessTokensResponse {
    const message = createBaseListUserAccessTokensResponse();
    message.accessTokens = object.accessTokens?.map((e) => UserAccessToken.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseCreateUserAccessTokenRequest(): CreateUserAccessTokenRequest {
  return { parent: "", accessToken: undefined, accessTokenId: "" };
}

export const CreateUserAccessTokenRequest: MessageFns<CreateUserAccessTokenRequest> = {
  encode(message: CreateUserAccessTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.accessToken !== undefined) {
      UserAccessToken.encode(message.accessToken, writer.uint32(18).fork()).join();
    }
    if (message.accessTokenId !== "") {
      writer.uint32(26).string(message.accessTokenId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserAccessTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserAccessTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = UserAccessToken.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accessTokenId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateUserAccessTokenRequest>): CreateUserAccessTokenRequest {
    return CreateUserAccessTokenRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateUserAccessTokenRequest>): CreateUserAccessTokenRequest {
    const message = createBaseCreateUserAccessTokenRequest();
    message.parent = object.parent ?? "";
    message.accessToken = (object.accessToken !== undefined && object.accessToken !== null)
      ? UserAccessToken.fromPartial(object.accessToken)
      : undefined;
    message.accessTokenId = object.accessTokenId ?? "";
    return message;
  },
};

function createBaseDeleteUserAccessTokenRequest(): DeleteUserAccessTokenRequest {
  return { name: "" };
}

export const DeleteUserAccessTokenRequest: MessageFns<DeleteUserAccessTokenRequest> = {
  encode(message: DeleteUserAccessTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserAccessTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserAccessTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteUserAccessTokenRequest>): DeleteUserAccessTokenRequest {
    return DeleteUserAccessTokenRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteUserAccessTokenRequest>): DeleteUserAccessTokenRequest {
    const message = createBaseDeleteUserAccessTokenRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUserSession(): UserSession {
  return { name: "", sessionId: "", createTime: undefined, lastAccessedTime: undefined, clientInfo: undefined };
}

export const UserSession: MessageFns<UserSession> = {
  encode(message: UserSession, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.sessionId !== "") {
      writer.uint32(18).string(message.sessionId);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(26).fork()).join();
    }
    if (message.lastAccessedTime !== undefined) {
      Timestamp.encode(toTimestamp(message.lastAccessedTime), writer.uint32(34).fork()).join();
    }
    if (message.clientInfo !== undefined) {
      UserSession_ClientInfo.encode(message.clientInfo, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSession {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSession();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sessionId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastAccessedTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.clientInfo = UserSession_ClientInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserSession>): UserSession {
    return UserSession.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserSession>): UserSession {
    const message = createBaseUserSession();
    message.name = object.name ?? "";
    message.sessionId = object.sessionId ?? "";
    message.createTime = object.createTime ?? undefined;
    message.lastAccessedTime = object.lastAccessedTime ?? undefined;
    message.clientInfo = (object.clientInfo !== undefined && object.clientInfo !== null)
      ? UserSession_ClientInfo.fromPartial(object.clientInfo)
      : undefined;
    return message;
  },
};

function createBaseUserSession_ClientInfo(): UserSession_ClientInfo {
  return { userAgent: "", ipAddress: "", deviceType: "", os: "", browser: "" };
}

export const UserSession_ClientInfo: MessageFns<UserSession_ClientInfo> = {
  encode(message: UserSession_ClientInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userAgent !== "") {
      writer.uint32(10).string(message.userAgent);
    }
    if (message.ipAddress !== "") {
      writer.uint32(18).string(message.ipAddress);
    }
    if (message.deviceType !== "") {
      writer.uint32(26).string(message.deviceType);
    }
    if (message.os !== "") {
      writer.uint32(34).string(message.os);
    }
    if (message.browser !== "") {
      writer.uint32(42).string(message.browser);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSession_ClientInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSession_ClientInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.deviceType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.os = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.browser = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserSession_ClientInfo>): UserSession_ClientInfo {
    return UserSession_ClientInfo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserSession_ClientInfo>): UserSession_ClientInfo {
    const message = createBaseUserSession_ClientInfo();
    message.userAgent = object.userAgent ?? "";
    message.ipAddress = object.ipAddress ?? "";
    message.deviceType = object.deviceType ?? "";
    message.os = object.os ?? "";
    message.browser = object.browser ?? "";
    return message;
  },
};

function createBaseListUserSessionsRequest(): ListUserSessionsRequest {
  return { parent: "" };
}

export const ListUserSessionsRequest: MessageFns<ListUserSessionsRequest> = {
  encode(message: ListUserSessionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserSessionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserSessionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserSessionsRequest>): ListUserSessionsRequest {
    return ListUserSessionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserSessionsRequest>): ListUserSessionsRequest {
    const message = createBaseListUserSessionsRequest();
    message.parent = object.parent ?? "";
    return message;
  },
};

function createBaseListUserSessionsResponse(): ListUserSessionsResponse {
  return { sessions: [] };
}

export const ListUserSessionsResponse: MessageFns<ListUserSessionsResponse> = {
  encode(message: ListUserSessionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.sessions) {
      UserSession.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserSessionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserSessionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sessions.push(UserSession.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserSessionsResponse>): ListUserSessionsResponse {
    return ListUserSessionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserSessionsResponse>): ListUserSessionsResponse {
    const message = createBaseListUserSessionsResponse();
    message.sessions = object.sessions?.map((e) => UserSession.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRevokeUserSessionRequest(): RevokeUserSessionRequest {
  return { name: "" };
}

export const RevokeUserSessionRequest: MessageFns<RevokeUserSessionRequest> = {
  encode(message: RevokeUserSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RevokeUserSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRevokeUserSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RevokeUserSessionRequest>): RevokeUserSessionRequest {
    return RevokeUserSessionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RevokeUserSessionRequest>): RevokeUserSessionRequest {
    const message = createBaseRevokeUserSessionRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListAllUserStatsRequest(): ListAllUserStatsRequest {
  return { pageSize: 0, pageToken: "" };
}

export const ListAllUserStatsRequest: MessageFns<ListAllUserStatsRequest> = {
  encode(message: ListAllUserStatsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(18).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAllUserStatsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAllUserStatsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAllUserStatsRequest>): ListAllUserStatsRequest {
    return ListAllUserStatsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListAllUserStatsRequest>): ListAllUserStatsRequest {
    const message = createBaseListAllUserStatsRequest();
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListAllUserStatsResponse(): ListAllUserStatsResponse {
  return { userStats: [], nextPageToken: "", totalSize: 0 };
}

export const ListAllUserStatsResponse: MessageFns<ListAllUserStatsResponse> = {
  encode(message: ListAllUserStatsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.userStats) {
      UserStats.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAllUserStatsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAllUserStatsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userStats.push(UserStats.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAllUserStatsResponse>): ListAllUserStatsResponse {
    return ListAllUserStatsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListAllUserStatsResponse>): ListAllUserStatsResponse {
    const message = createBaseListAllUserStatsResponse();
    message.userStats = object.userStats?.map((e) => UserStats.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

export type UserServiceDefinition = typeof UserServiceDefinition;
export const UserServiceDefinition = {
  name: "UserService",
  fullName: "memos.api.v1.UserService",
  methods: {
    /** ListUsers returns a list of users. */
    listUsers: {
      name: "ListUsers",
      requestType: ListUsersRequest,
      requestStream: false,
      responseType: ListUsersResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [new Uint8Array([15, 18, 13, 47, 97, 112, 105, 47, 118, 49, 47, 117, 115, 101, 114, 115])],
        },
      },
    },
    /** GetUser gets a user by name. */
    getUser: {
      name: "GetUser",
      requestType: GetUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** CreateUser creates a new user. */
    createUser: {
      name: "CreateUser",
      requestType: CreateUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 117, 115, 101, 114])],
          578365826: [
            new Uint8Array([
              21,
              58,
              4,
              117,
              115,
              101,
              114,
              34,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
            ]),
          ],
        },
      },
    },
    /** UpdateUser updates a user. */
    updateUser: {
      name: "UpdateUser",
      requestType: UpdateUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([16, 117, 115, 101, 114, 44, 117, 112, 100, 97, 116, 101, 95, 109, 97, 115, 107])],
          578365826: [
            new Uint8Array([
              35,
              58,
              4,
              117,
              115,
              101,
              114,
              50,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              117,
              115,
              101,
              114,
              46,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteUser deletes a user. */
    deleteUser: {
      name: "DeleteUser",
      requestType: DeleteUserRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              42,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** SearchUsers searches for users based on query. */
    searchUsers: {
      name: "SearchUsers",
      requestType: SearchUsersRequest,
      requestStream: false,
      responseType: SearchUsersResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([5, 113, 117, 101, 114, 121])],
          578365826: [
            new Uint8Array([
              22,
              18,
              20,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
              58,
              115,
              101,
              97,
              114,
              99,
              104,
            ]),
          ],
        },
      },
    },
    /** GetUserAvatar gets the avatar of a user. */
    getUserAvatar: {
      name: "GetUserAvatar",
      requestType: GetUserAvatarRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              118,
              97,
              116,
              97,
              114,
            ]),
          ],
        },
      },
    },
    /** ListAllUserStats returns statistics for all users. */
    listAllUserStats: {
      name: "ListAllUserStats",
      requestType: ListAllUserStatsRequest,
      requestStream: false,
      responseType: ListAllUserStatsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              21,
              18,
              19,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
              58,
              115,
              116,
              97,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetUserStats returns statistics for a specific user. */
    getUserStats: {
      name: "GetUserStats",
      requestType: GetUserStatsRequest,
      requestStream: false,
      responseType: UserStats,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              33,
              18,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              58,
              103,
              101,
              116,
              83,
              116,
              97,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetUserSetting returns the user setting. */
    getUserSetting: {
      name: "GetUserSetting",
      requestType: GetUserSettingRequest,
      requestStream: false,
      responseType: UserSetting,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              35,
              18,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              58,
              103,
              101,
              116,
              83,
              101,
              116,
              116,
              105,
              110,
              103,
            ]),
          ],
        },
      },
    },
    /** UpdateUserSetting updates the user setting. */
    updateUserSetting: {
      name: "UpdateUserSetting",
      requestType: UpdateUserSettingRequest,
      requestStream: false,
      responseType: UserSetting,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              19,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              55,
              58,
              7,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              50,
              44,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              46,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              58,
              117,
              112,
              100,
              97,
              116,
              101,
              83,
              101,
              116,
              116,
              105,
              110,
              103,
            ]),
          ],
        },
      },
    },
    /** ListUserAccessTokens returns a list of access tokens for a user. */
    listUserAccessTokens: {
      name: "ListUserAccessTokens",
      requestType: ListUserAccessTokensRequest,
      requestStream: false,
      responseType: ListUserAccessTokensResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([6, 112, 97, 114, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              39,
              18,
              37,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              84,
              111,
              107,
              101,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** CreateUserAccessToken creates a new access token for a user. */
    createUserAccessToken: {
      name: "CreateUserAccessToken",
      requestType: CreateUserAccessTokenRequest,
      requestStream: false,
      responseType: UserAccessToken,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              19,
              112,
              97,
              114,
              101,
              110,
              116,
              44,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
            ]),
          ],
          578365826: [
            new Uint8Array([
              53,
              58,
              12,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
              34,
              37,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              84,
              111,
              107,
              101,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** DeleteUserAccessToken deletes an access token. */
    deleteUserAccessToken: {
      name: "DeleteUserAccessToken",
      requestType: DeleteUserAccessTokenRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              39,
              42,
              37,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              84,
              111,
              107,
              101,
              110,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** ListUserSessions returns a list of active sessions for a user. */
    listUserSessions: {
      name: "ListUserSessions",
      requestType: ListUserSessionsRequest,
      requestStream: false,
      responseType: ListUserSessionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([6, 112, 97, 114, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              35,
              18,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              101,
              115,
              115,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** RevokeUserSession revokes a specific session for a user. */
    revokeUserSession: {
      name: "RevokeUserSession",
      requestType: RevokeUserSessionRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              35,
              42,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              115,
              101,
              115,
              115,
              105,
              111,
              110,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
