syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "gen/api/v1";

service IdentityProviderService {
  // ListIdentityProviders lists identity providers.
  rpc ListIdentityProviders(ListIdentityProvidersRequest) returns (ListIdentityProvidersResponse) {
    option (google.api.http) = {get: "/api/v1/identityProviders"};
  }

  // GetIdentityProvider gets an identity provider.
  rpc GetIdentityProvider(GetIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {get: "/api/v1/{name=identityProviders/*}"};
    option (google.api.method_signature) = "name";
  }

  // CreateIdentityProvider creates an identity provider.
  rpc CreateIdentityProvider(CreateIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {
      post: "/api/v1/identityProviders"
      body: "identity_provider"
    };
    option (google.api.method_signature) = "identity_provider";
  }

  // UpdateIdentityProvider updates an identity provider.
  rpc UpdateIdentityProvider(UpdateIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {
      patch: "/api/v1/{identity_provider.name=identityProviders/*}"
      body: "identity_provider"
    };
    option (google.api.method_signature) = "identity_provider,update_mask";
  }

  // DeleteIdentityProvider deletes an identity provider.
  rpc DeleteIdentityProvider(DeleteIdentityProviderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/api/v1/{name=identityProviders/*}"};
    option (google.api.method_signature) = "name";
  }
}

message IdentityProvider {
  option (google.api.resource) = {
    type: "memos.api.v1/IdentityProvider"
    pattern: "identityProviders/{idp}"
    name_field: "name"
    singular: "identityProvider"
    plural: "identityProviders"
  };

  // The resource name of the identity provider.
  // Format: identityProviders/{idp}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The type of the identity provider.
  Type type = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The display title of the identity provider.
  string title = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Filter applied to user identifiers.
  string identifier_filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Configuration for the identity provider.
  IdentityProviderConfig config = 5 [(google.api.field_behavior) = REQUIRED];

  enum Type {
    TYPE_UNSPECIFIED = 0;
    // OAuth2 identity provider.
    OAUTH2 = 1;
  }
}

message IdentityProviderConfig {
  oneof config {
    OAuth2Config oauth2_config = 1;
  }
}

message FieldMapping {
  string identifier = 1;
  string display_name = 2;
  string email = 3;
  string avatar_url = 4;
}

message OAuth2Config {
  string client_id = 1;
  string client_secret = 2;
  string auth_url = 3;
  string token_url = 4;
  string user_info_url = 5;
  repeated string scopes = 6;
  FieldMapping field_mapping = 7;
}

message ListIdentityProvidersRequest {}

message ListIdentityProvidersResponse {
  // The list of identity providers.
  repeated IdentityProvider identity_providers = 1;
}

message GetIdentityProviderRequest {
  // Required. The resource name of the identity provider to get.
  // Format: identityProviders/{idp}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/IdentityProvider"}
  ];
}

message CreateIdentityProviderRequest {
  // Required. The identity provider to create.
  IdentityProvider identity_provider = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The ID to use for the identity provider, which will become the final component of the resource name.
  // If not provided, the system will generate one.
  string identity_provider_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

message UpdateIdentityProviderRequest {
  // Required. The identity provider to update.
  IdentityProvider identity_provider = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. Only the top level fields of
  // IdentityProvider are supported.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

message DeleteIdentityProviderRequest {
  // Required. The resource name of the identity provider to delete.
  // Format: identityProviders/{idp}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/IdentityProvider"}
  ];
}
