swagger: "2.0"
info:
  title: api/v1/activity_service.proto
  version: version not set
tags:
  - name: ActivityService
  - name: AttachmentService
  - name: UserService
  - name: AuthService
  - name: IdentityProviderService
  - name: InboxService
  - name: MarkdownService
  - name: MemoService
  - name: ShortcutService
  - name: WebhookService
  - name: WorkspaceService
consumes:
  - application/json
produces:
  - application/json
paths:
  /api/v1/activities:
    get:
      summary: ListActivities returns a list of activities.
      operationId: ActivityService_ListActivities
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListActivitiesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: pageSize
          description: |-
            The maximum number of activities to return.
            The service may return fewer than this value.
            If unspecified, at most 100 activities will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            A page token, received from a previous `ListActivities` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
      tags:
        - ActivityService
  /api/v1/attachments:
    get:
      summary: ListAttachments lists all attachments.
      operationId: AttachmentService_ListAttachments
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListAttachmentsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: pageSize
          description: |-
            Optional. The maximum number of attachments to return.
            The service may return fewer than this value.
            If unspecified, at most 50 attachments will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            Optional. A page token, received from a previous `ListAttachments` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: filter
          description: |-
            Optional. Filter to apply to the list results.
            Example: "type=image/png" or "filename:*.jpg"
            Supported operators: =, !=, <, <=, >, >=, :
            Supported fields: filename, type, size, create_time, memo
          in: query
          required: false
          type: string
        - name: orderBy
          description: |-
            Optional. The order to sort results by.
            Example: "create_time desc" or "filename asc"
          in: query
          required: false
          type: string
      tags:
        - AttachmentService
    post:
      summary: CreateAttachment creates a new attachment.
      operationId: AttachmentService_CreateAttachment
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Attachment'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: attachment
          description: Required. The attachment to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1Attachment'
            required:
              - attachment
        - name: attachmentId
          description: |-
            Optional. The attachment ID to use for this attachment.
            If empty, a unique ID will be generated.
          in: query
          required: false
          type: string
      tags:
        - AttachmentService
  /api/v1/auth/sessions:
    post:
      summary: |-
        CreateSession authenticates a user and creates a new session.
        Returns the authenticated user information upon successful authentication.
      operationId: AuthService_CreateSession
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1CreateSessionResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1CreateSessionRequest'
      tags:
        - AuthService
  /api/v1/auth/sessions/current:
    get:
      summary: |-
        GetCurrentSession returns the current active session information.
        This method is idempotent and safe, suitable for checking current session state.
      operationId: AuthService_GetCurrentSession
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1GetCurrentSessionResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - AuthService
    delete:
      summary: |-
        DeleteSession terminates the current user session.
        This is an idempotent operation that invalidates the user's authentication.
      operationId: AuthService_DeleteSession
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - AuthService
  /api/v1/identityProviders:
    get:
      summary: ListIdentityProviders lists identity providers.
      operationId: IdentityProviderService_ListIdentityProviders
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListIdentityProvidersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - IdentityProviderService
    post:
      summary: CreateIdentityProvider creates an identity provider.
      operationId: IdentityProviderService_CreateIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: identityProvider
          description: Required. The identity provider to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
            required:
              - identityProvider
        - name: identityProviderId
          description: |-
            Optional. The ID to use for the identity provider, which will become the final component of the resource name.
            If not provided, the system will generate one.
          in: query
          required: false
          type: string
      tags:
        - IdentityProviderService
  /api/v1/markdown/links:getMetadata:
    get:
      summary: |-
        GetLinkMetadata returns metadata for a given link.
        This is useful for generating link previews.
      operationId: MarkdownService_GetLinkMetadata
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1LinkMetadata'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: link
          description: The link URL to get metadata for.
          in: query
          required: true
          type: string
      tags:
        - MarkdownService
  /api/v1/markdown:parse:
    post:
      summary: |-
        ParseMarkdown parses the given markdown content and returns a list of nodes.
        This is a utility method that transforms markdown text into structured nodes.
      operationId: MarkdownService_ParseMarkdown
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ParseMarkdownResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1ParseMarkdownRequest'
      tags:
        - MarkdownService
  /api/v1/markdown:restore:
    post:
      summary: |-
        RestoreMarkdownNodes restores the given nodes to markdown content.
        This is the inverse operation of ParseMarkdown.
      operationId: MarkdownService_RestoreMarkdownNodes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1RestoreMarkdownNodesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1RestoreMarkdownNodesRequest'
      tags:
        - MarkdownService
  /api/v1/markdown:stringify:
    post:
      summary: |-
        StringifyMarkdownNodes stringify the given nodes to plain text content.
        This removes all markdown formatting and returns plain text.
      operationId: MarkdownService_StringifyMarkdownNodes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1StringifyMarkdownNodesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1StringifyMarkdownNodesRequest'
      tags:
        - MarkdownService
  /api/v1/memos:
    get:
      summary: ListMemos lists memos with pagination and filter.
      operationId: MemoService_ListMemos
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemosResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Optional. The parent is the owner of the memos.
            If not specified or `users/-`, it will list all memos.
            Format: users/{user}
          in: query
          required: false
          type: string
        - name: pageSize
          description: |-
            Optional. The maximum number of memos to return.
            The service may return fewer than this value.
            If unspecified, at most 50 memos will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            Optional. A page token, received from a previous `ListMemos` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: state
          description: |-
            Optional. The state of the memos to list.
            Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
          in: query
          required: false
          type: string
          enum:
            - STATE_UNSPECIFIED
            - NORMAL
            - ARCHIVED
          default: STATE_UNSPECIFIED
        - name: orderBy
          description: |-
            Optional. The order to sort results by.
            Default to "display_time desc".
            Example: "display_time desc" or "create_time asc"
          in: query
          required: false
          type: string
        - name: filter
          description: |-
            Optional. Filter to apply to the list results.
            Filter is a CEL expression to filter memos.
            Refer to `Shortcut.filter`.
          in: query
          required: false
          type: string
        - name: showDeleted
          description: Optional. If true, show deleted memos in the response.
          in: query
          required: false
          type: boolean
        - name: oldFilter
          description: |-
            [Deprecated] Old filter contains some specific conditions to filter memos.
            Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
          in: query
          required: false
          type: string
      tags:
        - MemoService
    post:
      summary: CreateMemo creates a memo.
      operationId: MemoService_CreateMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: memo
          description: Required. The memo to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Memo'
            required:
              - memo
        - name: memoId
          description: |-
            Optional. The memo ID to use for this memo.
            If empty, a unique ID will be generated.
          in: query
          required: false
          type: string
        - name: validateOnly
          description: Optional. If set, validate the request but don't actually create the memo.
          in: query
          required: false
          type: boolean
        - name: requestId
          description: Optional. An idempotency token.
          in: query
          required: false
          type: string
      tags:
        - MemoService
  /api/v1/users:
    get:
      summary: ListUsers returns a list of users.
      operationId: UserService_ListUsers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListUsersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: pageSize
          description: |-
            Optional. The maximum number of users to return.
            The service may return fewer than this value.
            If unspecified, at most 50 users will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            Optional. A page token, received from a previous `ListUsers` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: filter
          description: |-
            Optional. Filter to apply to the list results.
            Example: "state=ACTIVE" or "role=USER" or "email:@example.com"
            Supported operators: =, !=, <, <=, >, >=, :
            Supported fields: username, email, role, state, create_time, update_time
          in: query
          required: false
          type: string
        - name: orderBy
          description: |-
            Optional. The order to sort results by.
            Example: "create_time desc" or "username asc"
          in: query
          required: false
          type: string
        - name: showDeleted
          description: Optional. If true, show deleted users in the response.
          in: query
          required: false
          type: boolean
      tags:
        - UserService
    post:
      summary: CreateUser creates a new user.
      operationId: UserService_CreateUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: user
          description: Required. The user to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1User'
            required:
              - user
        - name: userId
          description: |-
            Optional. The user ID to use for this user.
            If empty, a unique ID will be generated.
            Must match the pattern [a-z0-9-]+
          in: query
          required: false
          type: string
        - name: validateOnly
          description: Optional. If set, validate the request but don't actually create the user.
          in: query
          required: false
          type: boolean
        - name: requestId
          description: |-
            Optional. An idempotency token that can be used to ensure that multiple
            requests to create a user have the same result.
          in: query
          required: false
          type: string
      tags:
        - UserService
  /api/v1/users:search:
    get:
      summary: SearchUsers searches for users based on query.
      operationId: UserService_SearchUsers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1SearchUsersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: query
          description: Required. The search query.
          in: query
          required: true
          type: string
        - name: pageSize
          description: Optional. The maximum number of users to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - UserService
  /api/v1/users:stats:
    get:
      summary: ListAllUserStats returns statistics for all users.
      operationId: UserService_ListAllUserStats
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListAllUserStatsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: pageSize
          description: Optional. The maximum number of user stats to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - UserService
  /api/v1/workspace/profile:
    get:
      summary: Gets the workspace profile.
      operationId: WorkspaceService_GetWorkspaceProfile
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1WorkspaceProfile'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - WorkspaceService
  /api/v1/{attachment.name}:
    patch:
      summary: UpdateAttachment updates a attachment.
      operationId: AttachmentService_UpdateAttachment
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Attachment'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: attachment.name
          description: |-
            The name of the attachment.
            Format: attachments/{attachment}
          in: path
          required: true
          type: string
          pattern: attachments/[^/]+
        - name: attachment
          description: Required. The attachment which replaces the attachment on the server.
          in: body
          required: true
          schema:
            type: object
            properties:
              createTime:
                type: string
                format: date-time
                description: Output only. The creation timestamp.
                readOnly: true
              filename:
                type: string
                description: The filename of the attachment.
              content:
                type: string
                format: byte
                description: Input only. The content of the attachment.
              externalLink:
                type: string
                description: Optional. The external link of the attachment.
              type:
                type: string
                description: The MIME type of the attachment.
              size:
                type: string
                format: int64
                description: Output only. The size of the attachment in bytes.
                readOnly: true
              memo:
                type: string
                title: |-
                  Optional. The related memo. Refer to `Memo.name`.
                  Format: memos/{memo}
            title: Required. The attachment which replaces the attachment on the server.
            required:
              - filename
              - type
              - attachment
      tags:
        - AttachmentService
  /api/v1/{identityProvider.name}:
    patch:
      summary: UpdateIdentityProvider updates an identity provider.
      operationId: IdentityProviderService_UpdateIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: identityProvider.name
          description: |-
            The resource name of the identity provider.
            Format: identityProviders/{idp}
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
        - name: identityProvider
          description: Required. The identity provider to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              type:
                $ref: '#/definitions/apiv1IdentityProviderType'
                description: Required. The type of the identity provider.
              title:
                type: string
                description: Required. The display title of the identity provider.
              identifierFilter:
                type: string
                description: Optional. Filter applied to user identifiers.
              config:
                $ref: '#/definitions/apiv1IdentityProviderConfig'
                description: Required. Configuration for the identity provider.
            title: Required. The identity provider to update.
            required:
              - type
              - title
              - config
              - identityProvider
      tags:
        - IdentityProviderService
  /api/v1/{inbox.name}:
    patch:
      summary: UpdateInbox updates an inbox.
      operationId: InboxService_UpdateInbox
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Inbox'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: inbox.name
          description: |-
            The resource name of the inbox.
            Format: inboxes/{inbox}
          in: path
          required: true
          type: string
          pattern: inboxes/[^/]+
        - name: inbox
          description: Required. The inbox to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              sender:
                type: string
                title: |-
                  The sender of the inbox notification.
                  Format: users/{user}
                readOnly: true
              receiver:
                type: string
                title: |-
                  The receiver of the inbox notification.
                  Format: users/{user}
                readOnly: true
              status:
                $ref: '#/definitions/v1InboxStatus'
                description: The status of the inbox notification.
              createTime:
                type: string
                format: date-time
                description: Output only. The creation timestamp.
                readOnly: true
              type:
                $ref: '#/definitions/v1InboxType'
                description: The type of the inbox notification.
                readOnly: true
              activityId:
                type: integer
                format: int32
                description: Optional. The activity ID associated with this inbox notification.
            title: Required. The inbox to update.
            required:
              - inbox
        - name: allowMissing
          description: Optional. If set to true, allows updating missing fields.
          in: query
          required: false
          type: boolean
      tags:
        - InboxService
  /api/v1/{memo.name}:
    patch:
      summary: UpdateMemo updates a memo.
      operationId: MemoService_UpdateMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: memo.name
          description: |-
            The resource name of the memo.
            Format: memos/{memo}, memo is the user defined id or uuid.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: memo
          description: |-
            Required. The memo to update.
            The `name` field is required.
          in: body
          required: true
          schema:
            type: object
            properties:
              state:
                $ref: '#/definitions/v1State'
                description: The state of the memo.
              creator:
                type: string
                title: |-
                  The name of the creator.
                  Format: users/{user}
                readOnly: true
              createTime:
                type: string
                format: date-time
                description: Output only. The creation timestamp.
                readOnly: true
              updateTime:
                type: string
                format: date-time
                description: Output only. The last update timestamp.
                readOnly: true
              displayTime:
                type: string
                format: date-time
                description: The display timestamp of the memo.
              content:
                type: string
                description: Required. The content of the memo in Markdown format.
              nodes:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Node'
                description: Output only. The parsed nodes from the content.
                readOnly: true
              visibility:
                $ref: '#/definitions/v1Visibility'
                description: The visibility of the memo.
              tags:
                type: array
                items:
                  type: string
                description: Output only. The tags extracted from the content.
                readOnly: true
              pinned:
                type: boolean
                description: Whether the memo is pinned.
              attachments:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Attachment'
                description: Optional. The attachments of the memo.
              relations:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1MemoRelation'
                description: Optional. The relations of the memo.
              reactions:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Reaction'
                description: Output only. The reactions to the memo.
                readOnly: true
              property:
                $ref: '#/definitions/v1MemoProperty'
                description: Output only. The computed properties of the memo.
                readOnly: true
              parent:
                type: string
                title: |-
                  Output only. The name of the parent memo.
                  Format: memos/{memo}
                readOnly: true
              snippet:
                type: string
                description: Output only. The snippet of the memo content. Plain text only.
                readOnly: true
              location:
                $ref: '#/definitions/apiv1Location'
                description: Optional. The location of the memo.
            title: |-
              Required. The memo to update.
              The `name` field is required.
            required:
              - state
              - content
              - visibility
              - memo
        - name: allowMissing
          description: Optional. If set to true, allows updating sensitive fields.
          in: query
          required: false
          type: boolean
      tags:
        - MemoService
  /api/v1/{name_1}:
    get:
      summary: GetAttachment returns a attachment by name.
      operationId: AttachmentService_GetAttachment
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Attachment'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_1
          description: |-
            Required. The attachment name of the attachment to retrieve.
            Format: attachments/{attachment}
          in: path
          required: true
          type: string
          pattern: attachments/[^/]+
      tags:
        - AttachmentService
    delete:
      summary: DeleteUser deletes a user.
      operationId: UserService_DeleteUser
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_1
          description: |-
            Required. The resource name of the user to delete.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: force
          description: Optional. If set to true, the user will be deleted even if they have associated data.
          in: query
          required: false
          type: boolean
      tags:
        - UserService
  /api/v1/{name_2}:
    get:
      summary: GetUser gets a user by name.
      operationId: UserService_GetUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_2
          description: |-
            Required. The resource name of the user.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: readMask
          description: |-
            Optional. The fields to return in the response.
            If not specified, all fields are returned.
          in: query
          required: false
          type: string
      tags:
        - UserService
    delete:
      summary: DeleteUserAccessToken deletes an access token.
      operationId: UserService_DeleteUserAccessToken
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_2
          description: |-
            Required. The resource name of the access token to delete.
            Format: users/{user}/accessTokens/{access_token}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/accessTokens/[^/]+
      tags:
        - UserService
  /api/v1/{name_3}:
    get:
      summary: GetIdentityProvider gets an identity provider.
      operationId: IdentityProviderService_GetIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_3
          description: |-
            Required. The resource name of the identity provider to get.
            Format: identityProviders/{idp}
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
      tags:
        - IdentityProviderService
    delete:
      summary: RevokeUserSession revokes a specific session for a user.
      operationId: UserService_RevokeUserSession
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_3
          description: |-
            Required. The resource name of the session to revoke.
            Format: users/{user}/sessions/{session}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/sessions/[^/]+
      tags:
        - UserService
  /api/v1/{name_4}:
    get:
      summary: GetMemo gets a memo.
      operationId: MemoService_GetMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_4
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: readMask
          description: |-
            Optional. The fields to return in the response.
            If not specified, all fields are returned.
          in: query
          required: false
          type: string
      tags:
        - MemoService
    delete:
      summary: DeleteIdentityProvider deletes an identity provider.
      operationId: IdentityProviderService_DeleteIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_4
          description: |-
            Required. The resource name of the identity provider to delete.
            Format: identityProviders/{idp}
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
      tags:
        - IdentityProviderService
  /api/v1/{name_5}:
    get:
      summary: GetShortcut gets a shortcut by name.
      operationId: ShortcutService_GetShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_5
          description: |-
            Required. The resource name of the shortcut to retrieve.
            Format: users/{user}/shortcuts/{shortcut}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/shortcuts/[^/]+
      tags:
        - ShortcutService
    delete:
      summary: DeleteInbox deletes an inbox.
      operationId: InboxService_DeleteInbox
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_5
          description: |-
            Required. The resource name of the inbox to delete.
            Format: inboxes/{inbox}
          in: path
          required: true
          type: string
          pattern: inboxes/[^/]+
      tags:
        - InboxService
  /api/v1/{name_6}:
    get:
      summary: GetWebhook gets a webhook by name.
      operationId: WebhookService_GetWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_6
          description: |-
            Required. The resource name of the webhook to retrieve.
            Format: users/{user}/webhooks/{webhook}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/webhooks/[^/]+
      tags:
        - WebhookService
    delete:
      summary: DeleteMemo deletes a memo.
      operationId: MemoService_DeleteMemo
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_6
          description: |-
            Required. The resource name of the memo to delete.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: force
          description: Optional. If set to true, the memo will be deleted even if it has associated data.
          in: query
          required: false
          type: boolean
      tags:
        - MemoService
  /api/v1/{name_7}:
    get:
      summary: Gets a workspace setting.
      operationId: WorkspaceService_GetWorkspaceSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1WorkspaceSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_7
          description: |-
            The resource name of the workspace setting.
            Format: workspace/settings/{setting}
          in: path
          required: true
          type: string
          pattern: workspace/settings/[^/]+
      tags:
        - WorkspaceService
    delete:
      summary: DeleteMemoReaction deletes a reaction for a memo.
      operationId: MemoService_DeleteMemoReaction
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_7
          description: |-
            Required. The resource name of the reaction to delete.
            Format: reactions/{reaction}
          in: path
          required: true
          type: string
          pattern: reactions/[^/]+
      tags:
        - MemoService
  /api/v1/{name_8}:
    delete:
      summary: DeleteShortcut deletes a shortcut for a user.
      operationId: ShortcutService_DeleteShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_8
          description: |-
            Required. The resource name of the shortcut to delete.
            Format: users/{user}/shortcuts/{shortcut}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/shortcuts/[^/]+
      tags:
        - ShortcutService
  /api/v1/{name_9}:
    delete:
      summary: DeleteWebhook deletes a webhook for a user.
      operationId: WebhookService_DeleteWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_9
          description: |-
            Required. The resource name of the webhook to delete.
            Format: users/{user}/webhooks/{webhook}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/webhooks/[^/]+
      tags:
        - WebhookService
  /api/v1/{name}:
    get:
      summary: GetActivity returns the activity with the given id.
      operationId: ActivityService_GetActivity
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Activity'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            The name of the activity.
            Format: activities/{id}, id is the system generated auto-incremented id.
          in: path
          required: true
          type: string
          pattern: activities/[^/]+
      tags:
        - ActivityService
    delete:
      summary: DeleteAttachment deletes a attachment by name.
      operationId: AttachmentService_DeleteAttachment
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The attachment name of the attachment to delete.
            Format: attachments/{attachment}
          in: path
          required: true
          type: string
          pattern: attachments/[^/]+
      tags:
        - AttachmentService
  /api/v1/{name}/attachments:
    get:
      summary: ListMemoAttachments lists attachments for a memo.
      operationId: MemoService_ListMemoAttachments
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoAttachmentsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: pageSize
          description: Optional. The maximum number of attachments to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - MemoService
    patch:
      summary: SetMemoAttachments sets attachments for a memo.
      operationId: MemoService_SetMemoAttachments
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceSetMemoAttachmentsBody'
      tags:
        - MemoService
  /api/v1/{name}/avatar:
    get:
      summary: GetUserAvatar gets the avatar of a user.
      operationId: UserService_GetUserAvatar
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiHttpBody'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the user.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{name}/comments:
    get:
      summary: ListMemoComments lists comments for a memo.
      operationId: MemoService_ListMemoComments
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoCommentsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: pageSize
          description: Optional. The maximum number of comments to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
        - name: orderBy
          description: Optional. The order to sort results by.
          in: query
          required: false
          type: string
      tags:
        - MemoService
    post:
      summary: CreateMemoComment creates a comment for a memo.
      operationId: MemoService_CreateMemoComment
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: comment
          description: Required. The comment to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Memo'
            required:
              - comment
        - name: commentId
          description: Optional. The comment ID to use.
          in: query
          required: false
          type: string
      tags:
        - MemoService
  /api/v1/{name}/reactions:
    get:
      summary: ListMemoReactions lists reactions for a memo.
      operationId: MemoService_ListMemoReactions
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoReactionsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: pageSize
          description: Optional. The maximum number of reactions to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - MemoService
    post:
      summary: UpsertMemoReaction upserts a reaction for a memo.
      operationId: MemoService_UpsertMemoReaction
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Reaction'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceUpsertMemoReactionBody'
      tags:
        - MemoService
  /api/v1/{name}/relations:
    get:
      summary: ListMemoRelations lists relations for a memo.
      operationId: MemoService_ListMemoRelations
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoRelationsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: pageSize
          description: Optional. The maximum number of relations to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - MemoService
    patch:
      summary: SetMemoRelations sets relations for a memo.
      operationId: MemoService_SetMemoRelations
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the memo.
            Format: memos/{memo}
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceSetMemoRelationsBody'
      tags:
        - MemoService
  /api/v1/{name}:getSetting:
    get:
      summary: GetUserSetting returns the user setting.
      operationId: UserService_GetUserSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1UserSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the user.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{name}:getStats:
    get:
      summary: GetUserStats returns statistics for a specific user.
      operationId: UserService_GetUserStats
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1UserStats'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The resource name of the user.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{parent}/accessTokens:
    get:
      summary: ListUserAccessTokens returns a list of access tokens for a user.
      operationId: UserService_ListUserAccessTokens
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListUserAccessTokensResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource whose access tokens will be listed.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: pageSize
          description: Optional. The maximum number of access tokens to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Optional. A page token for pagination.
          in: query
          required: false
          type: string
      tags:
        - UserService
    post:
      summary: CreateUserAccessToken creates a new access token for a user.
      operationId: UserService_CreateUserAccessToken
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1UserAccessToken'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource where this access token will be created.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: accessToken
          description: Required. The access token to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1UserAccessToken'
            required:
              - accessToken
        - name: accessTokenId
          description: Optional. The access token ID to use.
          in: query
          required: false
          type: string
      tags:
        - UserService
  /api/v1/{parent}/inboxes:
    get:
      summary: ListInboxes lists inboxes for a user.
      operationId: InboxService_ListInboxes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListInboxesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource whose inboxes will be listed.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: pageSize
          description: |-
            Optional. The maximum number of inboxes to return.
            The service may return fewer than this value.
            If unspecified, at most 50 inboxes will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            Optional. A page token, received from a previous `ListInboxes` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: filter
          description: |-
            Optional. Filter to apply to the list results.
            Example: "status=UNREAD" or "type=MEMO_COMMENT"
            Supported operators: =, !=
            Supported fields: status, type, sender, create_time
          in: query
          required: false
          type: string
        - name: orderBy
          description: |-
            Optional. The order to sort results by.
            Example: "create_time desc" or "status asc"
          in: query
          required: false
          type: string
      tags:
        - InboxService
  /api/v1/{parent}/memos:
    get:
      summary: ListMemos lists memos with pagination and filter.
      operationId: MemoService_ListMemos2
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemosResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Optional. The parent is the owner of the memos.
            If not specified or `users/-`, it will list all memos.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: pageSize
          description: |-
            Optional. The maximum number of memos to return.
            The service may return fewer than this value.
            If unspecified, at most 50 memos will be returned.
            The maximum value is 1000; values above 1000 will be coerced to 1000.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            Optional. A page token, received from a previous `ListMemos` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: state
          description: |-
            Optional. The state of the memos to list.
            Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
          in: query
          required: false
          type: string
          enum:
            - STATE_UNSPECIFIED
            - NORMAL
            - ARCHIVED
          default: STATE_UNSPECIFIED
        - name: orderBy
          description: |-
            Optional. The order to sort results by.
            Default to "display_time desc".
            Example: "display_time desc" or "create_time asc"
          in: query
          required: false
          type: string
        - name: filter
          description: |-
            Optional. Filter to apply to the list results.
            Filter is a CEL expression to filter memos.
            Refer to `Shortcut.filter`.
          in: query
          required: false
          type: string
        - name: showDeleted
          description: Optional. If true, show deleted memos in the response.
          in: query
          required: false
          type: boolean
        - name: oldFilter
          description: |-
            [Deprecated] Old filter contains some specific conditions to filter memos.
            Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
          in: query
          required: false
          type: string
      tags:
        - MemoService
  /api/v1/{parent}/sessions:
    get:
      summary: ListUserSessions returns a list of active sessions for a user.
      operationId: UserService_ListUserSessions
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListUserSessionsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The resource name of the parent.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{parent}/shortcuts:
    get:
      summary: ListShortcuts returns a list of shortcuts for a user.
      operationId: ShortcutService_ListShortcuts
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListShortcutsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource where shortcuts are listed.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - ShortcutService
    post:
      summary: CreateShortcut creates a new shortcut for a user.
      operationId: ShortcutService_CreateShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource where this shortcut will be created.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: shortcut
          description: Required. The shortcut to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Shortcut'
            required:
              - shortcut
        - name: validateOnly
          description: Optional. If set, validate the request, but do not actually create the shortcut.
          in: query
          required: false
          type: boolean
      tags:
        - ShortcutService
  /api/v1/{parent}/tags/{tag}:
    delete:
      summary: DeleteMemoTag deletes a tag for a memo.
      operationId: MemoService_DeleteMemoTag
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent, who owns the tags.
            Format: memos/{memo}. Use "memos/-" to delete all tags.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: tag
          description: Required. The tag name to delete.
          in: path
          required: true
          type: string
        - name: deleteRelatedMemos
          description: Optional. Whether to delete related memos.
          in: query
          required: false
          type: boolean
      tags:
        - MemoService
  /api/v1/{parent}/tags:rename:
    patch:
      summary: RenameMemoTag renames a tag for a memo.
      operationId: MemoService_RenameMemoTag
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent, who owns the tags.
            Format: memos/{memo}. Use "memos/-" to rename all tags.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceRenameMemoTagBody'
      tags:
        - MemoService
  /api/v1/{parent}/webhooks:
    get:
      summary: ListWebhooks returns a list of webhooks for a user.
      operationId: WebhookService_ListWebhooks
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListWebhooksResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource where webhooks are listed.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - WebhookService
    post:
      summary: CreateWebhook creates a new webhook for a user.
      operationId: WebhookService_CreateWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            Required. The parent resource where this webhook will be created.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: webhook
          description: Required. The webhook to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Webhook'
            required:
              - webhook
        - name: validateOnly
          description: Optional. If set, validate the request, but do not actually create the webhook.
          in: query
          required: false
          type: boolean
      tags:
        - WebhookService
  /api/v1/{setting.name}:
    patch:
      summary: Updates a workspace setting.
      operationId: WorkspaceService_UpdateWorkspaceSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1WorkspaceSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: setting.name
          description: |-
            The name of the workspace setting.
            Format: workspace/settings/{setting}
          in: path
          required: true
          type: string
          pattern: workspace/settings/[^/]+
        - name: setting
          description: The workspace setting resource which replaces the resource on the server.
          in: body
          required: true
          schema:
            type: object
            properties:
              generalSetting:
                $ref: '#/definitions/apiv1WorkspaceGeneralSetting'
              storageSetting:
                $ref: '#/definitions/apiv1WorkspaceStorageSetting'
              memoRelatedSetting:
                $ref: '#/definitions/apiv1WorkspaceMemoRelatedSetting'
            title: The workspace setting resource which replaces the resource on the server.
            required:
              - setting
      tags:
        - WorkspaceService
  /api/v1/{setting.name}:updateSetting:
    patch:
      summary: UpdateUserSetting updates the user setting.
      operationId: UserService_UpdateUserSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1UserSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: setting.name
          description: |-
            The resource name of the user whose setting this is.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: setting
          description: Required. The user setting to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              locale:
                type: string
                description: The preferred locale of the user.
              appearance:
                type: string
                description: The preferred appearance of the user.
              memoVisibility:
                type: string
                description: The default visibility of the memo.
            title: Required. The user setting to update.
            required:
              - setting
      tags:
        - UserService
  /api/v1/{shortcut.name}:
    patch:
      summary: UpdateShortcut updates a shortcut for a user.
      operationId: ShortcutService_UpdateShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: shortcut.name
          description: |-
            The resource name of the shortcut.
            Format: users/{user}/shortcuts/{shortcut}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/shortcuts/[^/]+
        - name: shortcut
          description: Required. The shortcut resource which replaces the resource on the server.
          in: body
          required: true
          schema:
            type: object
            properties:
              title:
                type: string
                description: The title of the shortcut.
              filter:
                type: string
                description: The filter expression for the shortcut.
            title: Required. The shortcut resource which replaces the resource on the server.
            required:
              - title
              - shortcut
      tags:
        - ShortcutService
  /api/v1/{user.name}:
    patch:
      summary: UpdateUser updates a user.
      operationId: UserService_UpdateUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: user.name
          description: |-
            The resource name of the user.
            Format: users/{user}
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: user
          description: Required. The user to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              role:
                $ref: '#/definitions/UserRole'
                description: The role of the user.
              username:
                type: string
                description: Required. The unique username for login.
              email:
                type: string
                description: Optional. The email address of the user.
              displayName:
                type: string
                description: Optional. The display name of the user.
              avatarUrl:
                type: string
                description: Optional. The avatar URL of the user.
              description:
                type: string
                description: Optional. The description of the user.
              password:
                type: string
                description: Input only. The password for the user.
              state:
                $ref: '#/definitions/v1State'
                description: The state of the user.
              createTime:
                type: string
                format: date-time
                description: Output only. The creation timestamp.
                readOnly: true
              updateTime:
                type: string
                format: date-time
                description: Output only. The last update timestamp.
                readOnly: true
            title: Required. The user to update.
            required:
              - role
              - username
              - state
              - user
        - name: allowMissing
          description: Optional. If set to true, allows updating sensitive fields.
          in: query
          required: false
          type: boolean
      tags:
        - UserService
  /api/v1/{webhook.name}:
    patch:
      summary: UpdateWebhook updates a webhook for a user.
      operationId: WebhookService_UpdateWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: webhook.name
          description: |-
            The resource name of the webhook.
            Format: users/{user}/webhooks/{webhook}
          in: path
          required: true
          type: string
          pattern: users/[^/]+/webhooks/[^/]+
        - name: webhook
          description: Required. The webhook resource which replaces the resource on the server.
          in: body
          required: true
          schema:
            type: object
            properties:
              displayName:
                type: string
                description: The display name of the webhook.
              url:
                type: string
                description: The target URL for the webhook.
            title: Required. The webhook resource which replaces the resource on the server.
            required:
              - displayName
              - url
              - webhook
      tags:
        - WebhookService
  /file/{name}/{filename}:
    get:
      summary: GetAttachmentBinary returns a attachment binary by name.
      operationId: AttachmentService_GetAttachmentBinary
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiHttpBody'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            Required. The attachment name of the attachment.
            Format: attachments/{attachment}
          in: path
          required: true
          type: string
          pattern: attachments/[^/]+
        - name: filename
          description: The filename of the attachment. Mainly used for downloading.
          in: path
          required: true
          type: string
        - name: thumbnail
          description: Optional. A flag indicating if the thumbnail version of the attachment should be returned.
          in: query
          required: false
          type: boolean
      tags:
        - AttachmentService
definitions:
  ActivityLevel:
    type: string
    enum:
      - LEVEL_UNSPECIFIED
      - INFO
      - WARN
      - ERROR
    default: LEVEL_UNSPECIFIED
    description: |-
      Activity levels.

       - LEVEL_UNSPECIFIED: Unspecified level.
       - INFO: Info level.
       - WARN: Warn level.
       - ERROR: Error level.
  CreateSessionRequestPasswordCredentials:
    type: object
    properties:
      username:
        type: string
        description: |-
          The username to sign in with.
          Required field for password-based authentication.
      password:
        type: string
        description: |-
          The password to sign in with.
          Required field for password-based authentication.
    description: Nested message for password-based authentication credentials.
    required:
      - username
      - password
  CreateSessionRequestSSOCredentials:
    type: object
    properties:
      idpId:
        type: integer
        format: int32
        description: |-
          The ID of the SSO provider.
          Required field to identify the SSO provider.
      code:
        type: string
        description: |-
          The authorization code from the SSO provider.
          Required field for completing the SSO flow.
      redirectUri:
        type: string
        description: |-
          The redirect URI used in the SSO flow.
          Required field for security validation.
    description: Nested message for SSO authentication credentials.
    required:
      - idpId
      - code
      - redirectUri
  ListNodeKind:
    type: string
    enum:
      - KIND_UNSPECIFIED
      - ORDERED
      - UNORDERED
      - DESCRIPTION
    default: KIND_UNSPECIFIED
  MemoServiceRenameMemoTagBody:
    type: object
    properties:
      oldTag:
        type: string
        description: Required. The old tag name to rename.
      newTag:
        type: string
        description: Required. The new tag name.
    required:
      - oldTag
      - newTag
  MemoServiceSetMemoAttachmentsBody:
    type: object
    properties:
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Attachment'
        description: Required. The attachments to set for the memo.
    required:
      - attachments
  MemoServiceSetMemoRelationsBody:
    type: object
    properties:
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
        description: Required. The relations to set for the memo.
    required:
      - relations
  MemoServiceUpsertMemoReactionBody:
    type: object
    properties:
      reaction:
        $ref: '#/definitions/v1Reaction'
        description: Required. The reaction to upsert.
    required:
      - reaction
  TableNodeRow:
    type: object
    properties:
      cells:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  UserRole:
    type: string
    enum:
      - ROLE_UNSPECIFIED
      - HOST
      - ADMIN
      - USER
    default: ROLE_UNSPECIFIED
    description: |-
      User role enumeration.

       - ROLE_UNSPECIFIED: Unspecified role.
       - HOST: Host role with full system access.
       - ADMIN: Admin role with administrative privileges.
       - USER: Regular user role.
  UserStatsMemoTypeStats:
    type: object
    properties:
      linkCount:
        type: integer
        format: int32
      codeCount:
        type: integer
        format: int32
      todoCount:
        type: integer
        format: int32
      undoCount:
        type: integer
        format: int32
    description: Memo type statistics.
  WorkspaceStorageSettingS3Config:
    type: object
    properties:
      accessKeyId:
        type: string
      accessKeySecret:
        type: string
      endpoint:
        type: string
      region:
        type: string
      bucket:
        type: string
      usePathStyle:
        type: boolean
    title: 'Reference: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-go/'
  apiHttpBody:
    type: object
    properties:
      contentType:
        type: string
        description: The HTTP Content-Type header value specifying the content type of the body.
      data:
        type: string
        format: byte
        description: The HTTP request/response body as raw binary.
      extensions:
        type: array
        items:
          type: object
          $ref: '#/definitions/protobufAny'
        description: |-
          Application specific response metadata. Must be set in the first response
          for streaming APIs.
    description: |-
      Message that represents an arbitrary HTTP body. It should only be used for
      payload formats that can't be represented as JSON, such as raw binary or
      an HTML page.


      This message can be used both in streaming and non-streaming API methods in
      the request as well as the response.

      It can be used as a top-level request field, which is convenient if one
      wants to extract parameters from either the URL or HTTP template into the
      request fields and also want access to the raw HTTP body.

      Example:

          message GetResourceRequest {
            // A unique request id.
            string request_id = 1;

            // The raw HTTP body is bound to this field.
            google.api.HttpBody http_body = 2;

          }

          service ResourceService {
            rpc GetResource(GetResourceRequest)
              returns (google.api.HttpBody);
            rpc UpdateResource(google.api.HttpBody)
              returns (google.protobuf.Empty);

          }

      Example with streaming methods:

          service CaldavService {
            rpc GetCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);
            rpc UpdateCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);

          }

      Use of this type only changes how the request and response bodies are
      handled, all other features will continue to work unchanged.
  apiv1ActivityMemoCommentPayload:
    type: object
    properties:
      memo:
        type: string
        title: |-
          The memo name of comment.
          Format: memos/{memo}
      relatedMemo:
        type: string
        title: |-
          The name of related memo.
          Format: memos/{memo}
    description: ActivityMemoCommentPayload represents the payload of a memo comment activity.
  apiv1ActivityPayload:
    type: object
    properties:
      memoComment:
        $ref: '#/definitions/apiv1ActivityMemoCommentPayload'
        description: Memo comment activity payload.
  apiv1FieldMapping:
    type: object
    properties:
      identifier:
        type: string
      displayName:
        type: string
      email:
        type: string
      avatarUrl:
        type: string
  apiv1IdentityProvider:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the identity provider.
          Format: identityProviders/{idp}
      type:
        $ref: '#/definitions/apiv1IdentityProviderType'
        description: Required. The type of the identity provider.
      title:
        type: string
        description: Required. The display title of the identity provider.
      identifierFilter:
        type: string
        description: Optional. Filter applied to user identifiers.
      config:
        $ref: '#/definitions/apiv1IdentityProviderConfig'
        description: Required. Configuration for the identity provider.
    required:
      - type
      - title
      - config
  apiv1IdentityProviderConfig:
    type: object
    properties:
      oauth2Config:
        $ref: '#/definitions/apiv1OAuth2Config'
  apiv1IdentityProviderType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - OAUTH2
    default: TYPE_UNSPECIFIED
    description: ' - OAUTH2: OAuth2 identity provider.'
  apiv1Location:
    type: object
    properties:
      placeholder:
        type: string
        description: A placeholder text for the location.
      latitude:
        type: number
        format: double
        description: The latitude of the location.
      longitude:
        type: number
        format: double
        description: The longitude of the location.
  apiv1Memo:
    type: object
    properties:
      name:
        type: string
        description: |-
          The resource name of the memo.
          Format: memos/{memo}, memo is the user defined id or uuid.
      state:
        $ref: '#/definitions/v1State'
        description: The state of the memo.
      creator:
        type: string
        title: |-
          The name of the creator.
          Format: users/{user}
        readOnly: true
      createTime:
        type: string
        format: date-time
        description: Output only. The creation timestamp.
        readOnly: true
      updateTime:
        type: string
        format: date-time
        description: Output only. The last update timestamp.
        readOnly: true
      displayTime:
        type: string
        format: date-time
        description: The display timestamp of the memo.
      content:
        type: string
        description: Required. The content of the memo in Markdown format.
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
        description: Output only. The parsed nodes from the content.
        readOnly: true
      visibility:
        $ref: '#/definitions/v1Visibility'
        description: The visibility of the memo.
      tags:
        type: array
        items:
          type: string
        description: Output only. The tags extracted from the content.
        readOnly: true
      pinned:
        type: boolean
        description: Whether the memo is pinned.
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Attachment'
        description: Optional. The attachments of the memo.
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
        description: Optional. The relations of the memo.
      reactions:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Reaction'
        description: Output only. The reactions to the memo.
        readOnly: true
      property:
        $ref: '#/definitions/v1MemoProperty'
        description: Output only. The computed properties of the memo.
        readOnly: true
      parent:
        type: string
        title: |-
          Output only. The name of the parent memo.
          Format: memos/{memo}
        readOnly: true
      snippet:
        type: string
        description: Output only. The snippet of the memo content. Plain text only.
        readOnly: true
      location:
        $ref: '#/definitions/apiv1Location'
        description: Optional. The location of the memo.
    required:
      - state
      - content
      - visibility
  apiv1OAuth2Config:
    type: object
    properties:
      clientId:
        type: string
      clientSecret:
        type: string
      authUrl:
        type: string
      tokenUrl:
        type: string
      userInfoUrl:
        type: string
      scopes:
        type: array
        items:
          type: string
      fieldMapping:
        $ref: '#/definitions/apiv1FieldMapping'
  apiv1Shortcut:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the shortcut.
          Format: users/{user}/shortcuts/{shortcut}
      title:
        type: string
        description: The title of the shortcut.
      filter:
        type: string
        description: The filter expression for the shortcut.
    required:
      - title
  apiv1UserSetting:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the user whose setting this is.
          Format: users/{user}
      locale:
        type: string
        description: The preferred locale of the user.
      appearance:
        type: string
        description: The preferred appearance of the user.
      memoVisibility:
        type: string
        description: The default visibility of the memo.
    title: User settings message
  apiv1Webhook:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the webhook.
          Format: users/{user}/webhooks/{webhook}
      displayName:
        type: string
        description: The display name of the webhook.
      url:
        type: string
        description: The target URL for the webhook.
    required:
      - displayName
      - url
  apiv1WorkspaceCustomProfile:
    type: object
    properties:
      title:
        type: string
      description:
        type: string
      logoUrl:
        type: string
      locale:
        type: string
      appearance:
        type: string
  apiv1WorkspaceGeneralSetting:
    type: object
    properties:
      disallowUserRegistration:
        type: boolean
        description: disallow_user_registration disallows user registration.
      disallowPasswordAuth:
        type: boolean
        description: disallow_password_auth disallows password authentication.
      additionalScript:
        type: string
        description: additional_script is the additional script.
      additionalStyle:
        type: string
        description: additional_style is the additional style.
      customProfile:
        $ref: '#/definitions/apiv1WorkspaceCustomProfile'
        description: custom_profile is the custom profile.
      weekStartDayOffset:
        type: integer
        format: int32
        description: |-
          week_start_day_offset is the week start day offset from Sunday.
          0: Sunday, 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday
          Default is Sunday.
      disallowChangeUsername:
        type: boolean
        description: disallow_change_username disallows changing username.
      disallowChangeNickname:
        type: boolean
        description: disallow_change_nickname disallows changing nickname.
  apiv1WorkspaceMemoRelatedSetting:
    type: object
    properties:
      disallowPublicVisibility:
        type: boolean
        description: disallow_public_visibility disallows set memo as public visibility.
      displayWithUpdateTime:
        type: boolean
        description: display_with_update_time orders and displays memo with update time.
      contentLengthLimit:
        type: integer
        format: int32
        description: content_length_limit is the limit of content length. Unit is byte.
      enableDoubleClickEdit:
        type: boolean
        description: enable_double_click_edit enables editing on double click.
      enableLinkPreview:
        type: boolean
        description: enable_link_preview enables links preview.
      enableComment:
        type: boolean
        description: enable_comment enables comment.
      reactions:
        type: array
        items:
          type: string
        description: reactions is the list of reactions.
      disableMarkdownShortcuts:
        type: boolean
        description: disable_markdown_shortcuts disallow the registration of markdown shortcuts.
      enableBlurNsfwContent:
        type: boolean
        description: enable_blur_nsfw_content enables blurring of content marked as not safe for work (NSFW).
      nsfwTags:
        type: array
        items:
          type: string
        description: nsfw_tags is the list of tags that mark content as NSFW for blurring.
  apiv1WorkspaceSetting:
    type: object
    properties:
      name:
        type: string
        title: |-
          The name of the workspace setting.
          Format: workspace/settings/{setting}
      generalSetting:
        $ref: '#/definitions/apiv1WorkspaceGeneralSetting'
      storageSetting:
        $ref: '#/definitions/apiv1WorkspaceStorageSetting'
      memoRelatedSetting:
        $ref: '#/definitions/apiv1WorkspaceMemoRelatedSetting'
    description: A workspace setting resource.
  apiv1WorkspaceStorageSetting:
    type: object
    properties:
      storageType:
        $ref: '#/definitions/apiv1WorkspaceStorageSettingStorageType'
        description: storage_type is the storage type.
      filepathTemplate:
        type: string
        title: |-
          The template of file path.
          e.g. assets/{timestamp}_{filename}
      uploadSizeLimitMb:
        type: string
        format: int64
        description: The max upload size in megabytes.
      s3Config:
        $ref: '#/definitions/WorkspaceStorageSettingS3Config'
        description: The S3 config.
  apiv1WorkspaceStorageSettingStorageType:
    type: string
    enum:
      - STORAGE_TYPE_UNSPECIFIED
      - DATABASE
      - LOCAL
      - S3
    default: STORAGE_TYPE_UNSPECIFIED
    description: |2-
       - DATABASE: DATABASE is the database storage type.
       - LOCAL: LOCAL is the local storage type.
       - S3: S3 is the S3 storage type.
  googlerpcStatus:
    type: object
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      details:
        type: array
        items:
          type: object
          $ref: '#/definitions/protobufAny'
  protobufAny:
    type: object
    properties:
      '@type':
        type: string
        description: |-
          A URL/resource name that uniquely identifies the type of the serialized
          protocol buffer message. This string must contain at least
          one "/" character. The last segment of the URL's path must represent
          the fully qualified name of the type (as in
          `path/google.protobuf.Duration`). The name should be in a canonical form
          (e.g., leading "." is not accepted).

          In practice, teams usually precompile into the binary all types that they
          expect it to use in the context of Any. However, for URLs which use the
          scheme `http`, `https`, or no scheme, one can optionally set up a type
          server that maps type URLs to message definitions as follows:

          * If no scheme is provided, `https` is assumed.
          * An HTTP GET on the URL must yield a [google.protobuf.Type][]
            value in binary format, or produce an error.
          * Applications are allowed to cache lookup results based on the
            URL, or have them precompiled into a binary to avoid any
            lookup. Therefore, binary compatibility needs to be preserved
            on changes to types. (Use versioned type names to manage
            breaking changes.)

          Note: this functionality is not currently available in the official
          protobuf release, and it is not used for type URLs beginning with
          type.googleapis.com. As of May 2023, there are no widely used type server
          implementations and no plans to implement one.

          Schemes other than `http`, `https` (or the empty scheme) might be
          used with implementation specific semantics.
    additionalProperties: {}
    description: |-
      `Any` contains an arbitrary serialized protocol buffer message along with a
      URL that describes the type of the serialized message.

      Protobuf library provides support to pack/unpack Any values in the form
      of utility functions or additional generated methods of the Any type.

      Example 1: Pack and unpack a message in C++.

          Foo foo = ...;
          Any any;
          any.PackFrom(foo);
          ...
          if (any.UnpackTo(&foo)) {
            ...
          }

      Example 2: Pack and unpack a message in Java.

          Foo foo = ...;
          Any any = Any.pack(foo);
          ...
          if (any.is(Foo.class)) {
            foo = any.unpack(Foo.class);
          }
          // or ...
          if (any.isSameTypeAs(Foo.getDefaultInstance())) {
            foo = any.unpack(Foo.getDefaultInstance());
          }

       Example 3: Pack and unpack a message in Python.

          foo = Foo(...)
          any = Any()
          any.Pack(foo)
          ...
          if any.Is(Foo.DESCRIPTOR):
            any.Unpack(foo)
            ...

       Example 4: Pack and unpack a message in Go

           foo := &pb.Foo{...}
           any, err := anypb.New(foo)
           if err != nil {
             ...
           }
           ...
           foo := &pb.Foo{}
           if err := any.UnmarshalTo(foo); err != nil {
             ...
           }

      The pack methods provided by protobuf library will by default use
      'type.googleapis.com/full.type.name' as the type URL and the unpack
      methods only use the fully qualified type name after the last '/'
      in the type URL, for example "foo.bar.com/x/y.z" will yield type
      name "y.z".

      JSON
      ====
      The JSON representation of an `Any` value uses the regular
      representation of the deserialized, embedded message, with an
      additional field `@type` which contains the type URL. Example:

          package google.profile;
          message Person {
            string first_name = 1;
            string last_name = 2;
          }

          {
            "@type": "type.googleapis.com/google.profile.Person",
            "firstName": <string>,
            "lastName": <string>
          }

      If the embedded message type is well-known and has a custom JSON
      representation, that representation will be embedded adding a field
      `value` which holds the custom JSON in addition to the `@type`
      field. Example (for message [google.protobuf.Duration][]):

          {
            "@type": "type.googleapis.com/google.protobuf.Duration",
            "value": "1.212s"
          }
  v1Activity:
    type: object
    properties:
      name:
        type: string
        title: |-
          The name of the activity.
          Format: activities/{id}
        readOnly: true
      creator:
        type: string
        title: |-
          The name of the creator.
          Format: users/{user}
        readOnly: true
      type:
        $ref: '#/definitions/v1ActivityType'
        description: The type of the activity.
        readOnly: true
      level:
        $ref: '#/definitions/ActivityLevel'
        description: The level of the activity.
        readOnly: true
      createTime:
        type: string
        format: date-time
        description: The create time of the activity.
        readOnly: true
      payload:
        $ref: '#/definitions/apiv1ActivityPayload'
        description: The payload of the activity.
        readOnly: true
  v1ActivityType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - MEMO_COMMENT
      - VERSION_UPDATE
    default: TYPE_UNSPECIFIED
    description: |-
      Activity types.

       - TYPE_UNSPECIFIED: Unspecified type.
       - MEMO_COMMENT: Memo comment activity.
       - VERSION_UPDATE: Version update activity.
  v1Attachment:
    type: object
    properties:
      name:
        type: string
        title: |-
          The name of the attachment.
          Format: attachments/{attachment}
      createTime:
        type: string
        format: date-time
        description: Output only. The creation timestamp.
        readOnly: true
      filename:
        type: string
        description: The filename of the attachment.
      content:
        type: string
        format: byte
        description: Input only. The content of the attachment.
      externalLink:
        type: string
        description: Optional. The external link of the attachment.
      type:
        type: string
        description: The MIME type of the attachment.
      size:
        type: string
        format: int64
        description: Output only. The size of the attachment in bytes.
        readOnly: true
      memo:
        type: string
        title: |-
          Optional. The related memo. Refer to `Memo.name`.
          Format: memos/{memo}
    required:
      - filename
      - type
  v1AutoLinkNode:
    type: object
    properties:
      url:
        type: string
      isRawText:
        type: boolean
  v1BlockquoteNode:
    type: object
    properties:
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1BoldItalicNode:
    type: object
    properties:
      symbol:
        type: string
      content:
        type: string
  v1BoldNode:
    type: object
    properties:
      symbol:
        type: string
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1CodeBlockNode:
    type: object
    properties:
      language:
        type: string
      content:
        type: string
  v1CodeNode:
    type: object
    properties:
      content:
        type: string
  v1CreateSessionRequest:
    type: object
    properties:
      passwordCredentials:
        $ref: '#/definitions/CreateSessionRequestPasswordCredentials'
        description: Username and password authentication method.
      ssoCredentials:
        $ref: '#/definitions/CreateSessionRequestSSOCredentials'
        description: SSO provider authentication method.
  v1CreateSessionResponse:
    type: object
    properties:
      user:
        $ref: '#/definitions/v1User'
        description: The authenticated user information.
      lastAccessedAt:
        type: string
        format: date-time
        description: |-
          Last time the session was accessed.
          Used for sliding expiration calculation (last_accessed_time + 2 weeks).
  v1EmbeddedContentNode:
    type: object
    properties:
      resourceName:
        type: string
        description: The resource name of the embedded content.
      params:
        type: string
        description: Additional parameters for the embedded content.
  v1EscapingCharacterNode:
    type: object
    properties:
      symbol:
        type: string
  v1GetCurrentSessionResponse:
    type: object
    properties:
      user:
        $ref: '#/definitions/v1User'
      lastAccessedAt:
        type: string
        format: date-time
        description: |-
          Last time the session was accessed.
          Used for sliding expiration calculation (last_accessed_time + 2 weeks).
  v1HTMLElementNode:
    type: object
    properties:
      tagName:
        type: string
      attributes:
        type: object
        additionalProperties:
          type: string
  v1HeadingNode:
    type: object
    properties:
      level:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1HighlightNode:
    type: object
    properties:
      content:
        type: string
  v1HorizontalRuleNode:
    type: object
    properties:
      symbol:
        type: string
  v1ImageNode:
    type: object
    properties:
      altText:
        type: string
      url:
        type: string
  v1Inbox:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the inbox.
          Format: inboxes/{inbox}
      sender:
        type: string
        title: |-
          The sender of the inbox notification.
          Format: users/{user}
        readOnly: true
      receiver:
        type: string
        title: |-
          The receiver of the inbox notification.
          Format: users/{user}
        readOnly: true
      status:
        $ref: '#/definitions/v1InboxStatus'
        description: The status of the inbox notification.
      createTime:
        type: string
        format: date-time
        description: Output only. The creation timestamp.
        readOnly: true
      type:
        $ref: '#/definitions/v1InboxType'
        description: The type of the inbox notification.
        readOnly: true
      activityId:
        type: integer
        format: int32
        description: Optional. The activity ID associated with this inbox notification.
  v1InboxStatus:
    type: string
    enum:
      - STATUS_UNSPECIFIED
      - UNREAD
      - ARCHIVED
    default: STATUS_UNSPECIFIED
    description: |-
      Status enumeration for inbox notifications.

       - STATUS_UNSPECIFIED: Unspecified status.
       - UNREAD: The notification is unread.
       - ARCHIVED: The notification is archived.
  v1InboxType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - MEMO_COMMENT
      - VERSION_UPDATE
    default: TYPE_UNSPECIFIED
    description: |-
      Type enumeration for inbox notifications.

       - TYPE_UNSPECIFIED: Unspecified type.
       - MEMO_COMMENT: Memo comment notification.
       - VERSION_UPDATE: Version update notification.
  v1ItalicNode:
    type: object
    properties:
      symbol:
        type: string
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1LineBreakNode:
    type: object
  v1LinkMetadata:
    type: object
    properties:
      title:
        type: string
        description: The title of the linked page.
      description:
        type: string
        description: The description of the linked page.
      image:
        type: string
        description: The URL of the preview image for the linked page.
  v1LinkNode:
    type: object
    properties:
      content:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
      url:
        type: string
  v1ListActivitiesResponse:
    type: object
    properties:
      activities:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Activity'
        description: The activities.
      nextPageToken:
        type: string
        description: |-
          A token to retrieve the next page of results.
          Pass this value in the page_token field in the subsequent call to `ListActivities`
          method to retrieve the next page of results.
  v1ListAllUserStatsResponse:
    type: object
    properties:
      userStats:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1UserStats'
        description: The list of user statistics.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of user statistics.
  v1ListAttachmentsResponse:
    type: object
    properties:
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Attachment'
        description: The list of attachments.
      nextPageToken:
        type: string
        description: |-
          A token that can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
      totalSize:
        type: integer
        format: int32
        description: The total count of attachments (may be approximate).
  v1ListIdentityProvidersResponse:
    type: object
    properties:
      identityProviders:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1IdentityProvider'
        description: The list of identity providers.
  v1ListInboxesResponse:
    type: object
    properties:
      inboxes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Inbox'
        description: The list of inboxes.
      nextPageToken:
        type: string
        description: |-
          A token that can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
      totalSize:
        type: integer
        format: int32
        description: The total count of inboxes (may be approximate).
  v1ListMemoAttachmentsResponse:
    type: object
    properties:
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Attachment'
        description: The list of attachments.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of attachments.
  v1ListMemoCommentsResponse:
    type: object
    properties:
      memos:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Memo'
        description: The list of comment memos.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of comments.
  v1ListMemoReactionsResponse:
    type: object
    properties:
      reactions:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Reaction'
        description: The list of reactions.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of reactions.
  v1ListMemoRelationsResponse:
    type: object
    properties:
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
        description: The list of relations.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of relations.
  v1ListMemosResponse:
    type: object
    properties:
      memos:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Memo'
        description: The list of memos.
      nextPageToken:
        type: string
        description: |-
          A token that can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
      totalSize:
        type: integer
        format: int32
        description: The total count of memos (may be approximate).
  v1ListNode:
    type: object
    properties:
      kind:
        $ref: '#/definitions/ListNodeKind'
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ListShortcutsResponse:
    type: object
    properties:
      shortcuts:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Shortcut'
        description: The list of shortcuts.
  v1ListUserAccessTokensResponse:
    type: object
    properties:
      accessTokens:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1UserAccessToken'
        description: The list of access tokens.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of access tokens.
  v1ListUserSessionsResponse:
    type: object
    properties:
      sessions:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1UserSession'
        description: The list of user sessions.
  v1ListUsersResponse:
    type: object
    properties:
      users:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1User'
        description: The list of users.
      nextPageToken:
        type: string
        description: |-
          A token that can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
      totalSize:
        type: integer
        format: int32
        description: The total count of users (may be approximate).
  v1ListWebhooksResponse:
    type: object
    properties:
      webhooks:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Webhook'
        description: The list of webhooks.
  v1MathBlockNode:
    type: object
    properties:
      content:
        type: string
  v1MathNode:
    type: object
    properties:
      content:
        type: string
  v1MemoProperty:
    type: object
    properties:
      hasLink:
        type: boolean
      hasTaskList:
        type: boolean
      hasCode:
        type: boolean
      hasIncompleteTasks:
        type: boolean
    description: Computed properties of a memo.
  v1MemoRelation:
    type: object
    properties:
      memo:
        $ref: '#/definitions/v1MemoRelationMemo'
        description: The memo in the relation.
      relatedMemo:
        $ref: '#/definitions/v1MemoRelationMemo'
        description: The related memo.
      type:
        $ref: '#/definitions/v1MemoRelationType'
    required:
      - memo
      - relatedMemo
      - type
  v1MemoRelationMemo:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the memo.
          Format: memos/{memo}
      snippet:
        type: string
        description: Output only. The snippet of the memo content. Plain text only.
        readOnly: true
    description: Memo reference in relations.
    required:
      - name
  v1MemoRelationType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - REFERENCE
      - COMMENT
    default: TYPE_UNSPECIFIED
    description: The type of the relation.
  v1Node:
    type: object
    properties:
      type:
        $ref: '#/definitions/v1NodeType'
      lineBreakNode:
        $ref: '#/definitions/v1LineBreakNode'
        description: Block nodes.
      paragraphNode:
        $ref: '#/definitions/v1ParagraphNode'
      codeBlockNode:
        $ref: '#/definitions/v1CodeBlockNode'
      headingNode:
        $ref: '#/definitions/v1HeadingNode'
      horizontalRuleNode:
        $ref: '#/definitions/v1HorizontalRuleNode'
      blockquoteNode:
        $ref: '#/definitions/v1BlockquoteNode'
      listNode:
        $ref: '#/definitions/v1ListNode'
      orderedListItemNode:
        $ref: '#/definitions/v1OrderedListItemNode'
      unorderedListItemNode:
        $ref: '#/definitions/v1UnorderedListItemNode'
      taskListItemNode:
        $ref: '#/definitions/v1TaskListItemNode'
      mathBlockNode:
        $ref: '#/definitions/v1MathBlockNode'
      tableNode:
        $ref: '#/definitions/v1TableNode'
      embeddedContentNode:
        $ref: '#/definitions/v1EmbeddedContentNode'
      textNode:
        $ref: '#/definitions/v1TextNode'
        description: Inline nodes.
      boldNode:
        $ref: '#/definitions/v1BoldNode'
      italicNode:
        $ref: '#/definitions/v1ItalicNode'
      boldItalicNode:
        $ref: '#/definitions/v1BoldItalicNode'
      codeNode:
        $ref: '#/definitions/v1CodeNode'
      imageNode:
        $ref: '#/definitions/v1ImageNode'
      linkNode:
        $ref: '#/definitions/v1LinkNode'
      autoLinkNode:
        $ref: '#/definitions/v1AutoLinkNode'
      tagNode:
        $ref: '#/definitions/v1TagNode'
      strikethroughNode:
        $ref: '#/definitions/v1StrikethroughNode'
      escapingCharacterNode:
        $ref: '#/definitions/v1EscapingCharacterNode'
      mathNode:
        $ref: '#/definitions/v1MathNode'
      highlightNode:
        $ref: '#/definitions/v1HighlightNode'
      subscriptNode:
        $ref: '#/definitions/v1SubscriptNode'
      superscriptNode:
        $ref: '#/definitions/v1SuperscriptNode'
      referencedContentNode:
        $ref: '#/definitions/v1ReferencedContentNode'
      spoilerNode:
        $ref: '#/definitions/v1SpoilerNode'
      htmlElementNode:
        $ref: '#/definitions/v1HTMLElementNode'
  v1NodeType:
    type: string
    enum:
      - NODE_UNSPECIFIED
      - LINE_BREAK
      - PARAGRAPH
      - CODE_BLOCK
      - HEADING
      - HORIZONTAL_RULE
      - BLOCKQUOTE
      - LIST
      - ORDERED_LIST_ITEM
      - UNORDERED_LIST_ITEM
      - TASK_LIST_ITEM
      - MATH_BLOCK
      - TABLE
      - EMBEDDED_CONTENT
      - TEXT
      - BOLD
      - ITALIC
      - BOLD_ITALIC
      - CODE
      - IMAGE
      - LINK
      - AUTO_LINK
      - TAG
      - STRIKETHROUGH
      - ESCAPING_CHARACTER
      - MATH
      - HIGHLIGHT
      - SUBSCRIPT
      - SUPERSCRIPT
      - REFERENCED_CONTENT
      - SPOILER
      - HTML_ELEMENT
    default: NODE_UNSPECIFIED
    description: |2-
       - LINE_BREAK: Block nodes.
       - TEXT: Inline nodes.
  v1OrderedListItemNode:
    type: object
    properties:
      number:
        type: string
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ParagraphNode:
    type: object
    properties:
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ParseMarkdownRequest:
    type: object
    properties:
      markdown:
        type: string
        description: The markdown content to parse.
    required:
      - markdown
  v1ParseMarkdownResponse:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
        description: The parsed markdown nodes.
  v1Reaction:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the reaction.
          Format: reactions/{reaction}
        readOnly: true
      creator:
        type: string
        title: |-
          The resource name of the creator.
          Format: users/{user}
        readOnly: true
      contentId:
        type: string
        title: |-
          The resource name of the content.
          For memo reactions, this should be the memo's resource name.
          Format: memos/{memo}
      reactionType:
        type: string
        description: "Required. The type of reaction (e.g., \"\U0001F44D\", \"❤️\", \"\U0001F604\")."
      createTime:
        type: string
        format: date-time
        description: Output only. The creation timestamp.
        readOnly: true
    required:
      - contentId
      - reactionType
  v1ReferencedContentNode:
    type: object
    properties:
      resourceName:
        type: string
        description: The resource name of the referenced content.
      params:
        type: string
        description: Additional parameters for the referenced content.
  v1RestoreMarkdownNodesRequest:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
        description: The nodes to restore to markdown content.
    required:
      - nodes
  v1RestoreMarkdownNodesResponse:
    type: object
    properties:
      markdown:
        type: string
        description: The restored markdown content.
  v1SearchUsersResponse:
    type: object
    properties:
      users:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1User'
        description: The list of users matching the search query.
      nextPageToken:
        type: string
        description: A token for the next page of results.
      totalSize:
        type: integer
        format: int32
        description: The total count of matching users.
  v1SpoilerNode:
    type: object
    properties:
      content:
        type: string
  v1State:
    type: string
    enum:
      - STATE_UNSPECIFIED
      - NORMAL
      - ARCHIVED
    default: STATE_UNSPECIFIED
  v1StrikethroughNode:
    type: object
    properties:
      content:
        type: string
  v1StringifyMarkdownNodesRequest:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
        description: The nodes to stringify to plain text.
    required:
      - nodes
  v1StringifyMarkdownNodesResponse:
    type: object
    properties:
      plainText:
        type: string
        description: The plain text content.
  v1SubscriptNode:
    type: object
    properties:
      content:
        type: string
  v1SuperscriptNode:
    type: object
    properties:
      content:
        type: string
  v1TableNode:
    type: object
    properties:
      header:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
      delimiter:
        type: array
        items:
          type: string
      rows:
        type: array
        items:
          type: object
          $ref: '#/definitions/TableNodeRow'
  v1TagNode:
    type: object
    properties:
      content:
        type: string
  v1TaskListItemNode:
    type: object
    properties:
      symbol:
        type: string
      indent:
        type: integer
        format: int32
      complete:
        type: boolean
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1TextNode:
    type: object
    properties:
      content:
        type: string
  v1UnorderedListItemNode:
    type: object
    properties:
      symbol:
        type: string
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1User:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the user.
          Format: users/{user}
      role:
        $ref: '#/definitions/UserRole'
        description: The role of the user.
      username:
        type: string
        description: Required. The unique username for login.
      email:
        type: string
        description: Optional. The email address of the user.
      displayName:
        type: string
        description: Optional. The display name of the user.
      avatarUrl:
        type: string
        description: Optional. The avatar URL of the user.
      description:
        type: string
        description: Optional. The description of the user.
      password:
        type: string
        description: Input only. The password for the user.
      state:
        $ref: '#/definitions/v1State'
        description: The state of the user.
      createTime:
        type: string
        format: date-time
        description: Output only. The creation timestamp.
        readOnly: true
      updateTime:
        type: string
        format: date-time
        description: Output only. The last update timestamp.
        readOnly: true
    required:
      - role
      - username
      - state
  v1UserAccessToken:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the access token.
          Format: users/{user}/accessTokens/{access_token}
      accessToken:
        type: string
        description: Output only. The access token value.
        readOnly: true
      description:
        type: string
        description: The description of the access token.
      issuedAt:
        type: string
        format: date-time
        description: Output only. The issued timestamp.
        readOnly: true
      expiresAt:
        type: string
        format: date-time
        description: Optional. The expiration timestamp.
    title: User access token message
  v1UserSession:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the session.
          Format: users/{user}/sessions/{session}
      sessionId:
        type: string
        description: The session ID.
        readOnly: true
      createTime:
        type: string
        format: date-time
        description: The timestamp when the session was created.
        readOnly: true
      lastAccessedTime:
        type: string
        format: date-time
        description: |-
          The timestamp when the session was last accessed.
          Used for sliding expiration calculation (last_accessed_time + 2 weeks).
        readOnly: true
      clientInfo:
        $ref: '#/definitions/v1UserSessionClientInfo'
        description: Client information associated with this session.
        readOnly: true
  v1UserSessionClientInfo:
    type: object
    properties:
      userAgent:
        type: string
        description: User agent string of the client.
      ipAddress:
        type: string
        description: IP address of the client.
      deviceType:
        type: string
        description: Optional. Device type (e.g., "mobile", "desktop", "tablet").
      os:
        type: string
        description: Optional. Operating system (e.g., "iOS 17.0", "Windows 11").
      browser:
        type: string
        description: Optional. Browser name and version (e.g., "Chrome 119.0").
  v1UserStats:
    type: object
    properties:
      name:
        type: string
        title: |-
          The resource name of the user whose stats these are.
          Format: users/{user}
      memoDisplayTimestamps:
        type: array
        items:
          type: string
          format: date-time
        description: The timestamps when the memos were displayed.
      memoTypeStats:
        $ref: '#/definitions/UserStatsMemoTypeStats'
        description: The stats of memo types.
      tagCount:
        type: object
        additionalProperties:
          type: integer
          format: int32
        description: The count of tags.
      pinnedMemos:
        type: array
        items:
          type: string
        description: The pinned memos of the user.
      totalMemoCount:
        type: integer
        format: int32
        description: Total memo count.
    title: User statistics messages
  v1Visibility:
    type: string
    enum:
      - VISIBILITY_UNSPECIFIED
      - PRIVATE
      - PROTECTED
      - PUBLIC
    default: VISIBILITY_UNSPECIFIED
  v1WorkspaceProfile:
    type: object
    properties:
      owner:
        type: string
        title: |-
          The name of instance owner.
          Format: users/{user}
      version:
        type: string
        description: Version is the current version of instance.
      mode:
        type: string
        description: Mode is the instance mode (e.g. "prod", "dev" or "demo").
      instanceUrl:
        type: string
        description: Instance URL is the URL of the instance.
    description: Workspace profile message containing basic workspace information.
