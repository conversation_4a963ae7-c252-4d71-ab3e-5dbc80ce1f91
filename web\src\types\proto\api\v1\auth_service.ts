// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/auth_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "../../google/protobuf/empty";
import { Timestamp } from "../../google/protobuf/timestamp";
import { User } from "./user_service";

export const protobufPackage = "memos.api.v1";

export interface GetCurrentSessionRequest {
}

export interface GetCurrentSessionResponse {
  user?:
    | User
    | undefined;
  /**
   * Last time the session was accessed.
   * Used for sliding expiration calculation (last_accessed_time + 2 weeks).
   */
  lastAccessedAt?: Date | undefined;
}

export interface CreateSessionRequest {
  /** Username and password authentication method. */
  passwordCredentials?:
    | CreateSessionRequest_PasswordCredentials
    | undefined;
  /** SSO provider authentication method. */
  ssoCredentials?: CreateSessionRequest_SSOCredentials | undefined;
}

/** Nested message for password-based authentication credentials. */
export interface CreateSessionRequest_PasswordCredentials {
  /**
   * The username to sign in with.
   * Required field for password-based authentication.
   */
  username: string;
  /**
   * The password to sign in with.
   * Required field for password-based authentication.
   */
  password: string;
}

/** Nested message for SSO authentication credentials. */
export interface CreateSessionRequest_SSOCredentials {
  /**
   * The ID of the SSO provider.
   * Required field to identify the SSO provider.
   */
  idpId: number;
  /**
   * The authorization code from the SSO provider.
   * Required field for completing the SSO flow.
   */
  code: string;
  /**
   * The redirect URI used in the SSO flow.
   * Required field for security validation.
   */
  redirectUri: string;
}

export interface CreateSessionResponse {
  /** The authenticated user information. */
  user?:
    | User
    | undefined;
  /**
   * Last time the session was accessed.
   * Used for sliding expiration calculation (last_accessed_time + 2 weeks).
   */
  lastAccessedAt?: Date | undefined;
}

export interface DeleteSessionRequest {
}

function createBaseGetCurrentSessionRequest(): GetCurrentSessionRequest {
  return {};
}

export const GetCurrentSessionRequest: MessageFns<GetCurrentSessionRequest> = {
  encode(_: GetCurrentSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCurrentSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCurrentSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetCurrentSessionRequest>): GetCurrentSessionRequest {
    return GetCurrentSessionRequest.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<GetCurrentSessionRequest>): GetCurrentSessionRequest {
    const message = createBaseGetCurrentSessionRequest();
    return message;
  },
};

function createBaseGetCurrentSessionResponse(): GetCurrentSessionResponse {
  return { user: undefined, lastAccessedAt: undefined };
}

export const GetCurrentSessionResponse: MessageFns<GetCurrentSessionResponse> = {
  encode(message: GetCurrentSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    if (message.lastAccessedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.lastAccessedAt), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCurrentSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCurrentSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastAccessedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetCurrentSessionResponse>): GetCurrentSessionResponse {
    return GetCurrentSessionResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetCurrentSessionResponse>): GetCurrentSessionResponse {
    const message = createBaseGetCurrentSessionResponse();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    message.lastAccessedAt = object.lastAccessedAt ?? undefined;
    return message;
  },
};

function createBaseCreateSessionRequest(): CreateSessionRequest {
  return { passwordCredentials: undefined, ssoCredentials: undefined };
}

export const CreateSessionRequest: MessageFns<CreateSessionRequest> = {
  encode(message: CreateSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.passwordCredentials !== undefined) {
      CreateSessionRequest_PasswordCredentials.encode(message.passwordCredentials, writer.uint32(10).fork()).join();
    }
    if (message.ssoCredentials !== undefined) {
      CreateSessionRequest_SSOCredentials.encode(message.ssoCredentials, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.passwordCredentials = CreateSessionRequest_PasswordCredentials.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ssoCredentials = CreateSessionRequest_SSOCredentials.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateSessionRequest>): CreateSessionRequest {
    return CreateSessionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateSessionRequest>): CreateSessionRequest {
    const message = createBaseCreateSessionRequest();
    message.passwordCredentials = (object.passwordCredentials !== undefined && object.passwordCredentials !== null)
      ? CreateSessionRequest_PasswordCredentials.fromPartial(object.passwordCredentials)
      : undefined;
    message.ssoCredentials = (object.ssoCredentials !== undefined && object.ssoCredentials !== null)
      ? CreateSessionRequest_SSOCredentials.fromPartial(object.ssoCredentials)
      : undefined;
    return message;
  },
};

function createBaseCreateSessionRequest_PasswordCredentials(): CreateSessionRequest_PasswordCredentials {
  return { username: "", password: "" };
}

export const CreateSessionRequest_PasswordCredentials: MessageFns<CreateSessionRequest_PasswordCredentials> = {
  encode(message: CreateSessionRequest_PasswordCredentials, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSessionRequest_PasswordCredentials {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSessionRequest_PasswordCredentials();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateSessionRequest_PasswordCredentials>): CreateSessionRequest_PasswordCredentials {
    return CreateSessionRequest_PasswordCredentials.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateSessionRequest_PasswordCredentials>): CreateSessionRequest_PasswordCredentials {
    const message = createBaseCreateSessionRequest_PasswordCredentials();
    message.username = object.username ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

function createBaseCreateSessionRequest_SSOCredentials(): CreateSessionRequest_SSOCredentials {
  return { idpId: 0, code: "", redirectUri: "" };
}

export const CreateSessionRequest_SSOCredentials: MessageFns<CreateSessionRequest_SSOCredentials> = {
  encode(message: CreateSessionRequest_SSOCredentials, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.idpId !== 0) {
      writer.uint32(8).int32(message.idpId);
    }
    if (message.code !== "") {
      writer.uint32(18).string(message.code);
    }
    if (message.redirectUri !== "") {
      writer.uint32(26).string(message.redirectUri);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSessionRequest_SSOCredentials {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSessionRequest_SSOCredentials();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.idpId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.redirectUri = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateSessionRequest_SSOCredentials>): CreateSessionRequest_SSOCredentials {
    return CreateSessionRequest_SSOCredentials.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateSessionRequest_SSOCredentials>): CreateSessionRequest_SSOCredentials {
    const message = createBaseCreateSessionRequest_SSOCredentials();
    message.idpId = object.idpId ?? 0;
    message.code = object.code ?? "";
    message.redirectUri = object.redirectUri ?? "";
    return message;
  },
};

function createBaseCreateSessionResponse(): CreateSessionResponse {
  return { user: undefined, lastAccessedAt: undefined };
}

export const CreateSessionResponse: MessageFns<CreateSessionResponse> = {
  encode(message: CreateSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    if (message.lastAccessedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.lastAccessedAt), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastAccessedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateSessionResponse>): CreateSessionResponse {
    return CreateSessionResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateSessionResponse>): CreateSessionResponse {
    const message = createBaseCreateSessionResponse();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    message.lastAccessedAt = object.lastAccessedAt ?? undefined;
    return message;
  },
};

function createBaseDeleteSessionRequest(): DeleteSessionRequest {
  return {};
}

export const DeleteSessionRequest: MessageFns<DeleteSessionRequest> = {
  encode(_: DeleteSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteSessionRequest>): DeleteSessionRequest {
    return DeleteSessionRequest.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<DeleteSessionRequest>): DeleteSessionRequest {
    const message = createBaseDeleteSessionRequest();
    return message;
  },
};

export type AuthServiceDefinition = typeof AuthServiceDefinition;
export const AuthServiceDefinition = {
  name: "AuthService",
  fullName: "memos.api.v1.AuthService",
  methods: {
    /**
     * GetCurrentSession returns the current active session information.
     * This method is idempotent and safe, suitable for checking current session state.
     */
    getCurrentSession: {
      name: "GetCurrentSession",
      requestType: GetCurrentSessionRequest,
      requestStream: false,
      responseType: GetCurrentSessionResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              117,
              116,
              104,
              47,
              115,
              101,
              115,
              115,
              105,
              111,
              110,
              115,
              47,
              99,
              117,
              114,
              114,
              101,
              110,
              116,
            ]),
          ],
        },
      },
    },
    /**
     * CreateSession authenticates a user and creates a new session.
     * Returns the authenticated user information upon successful authentication.
     */
    createSession: {
      name: "CreateSession",
      requestType: CreateSessionRequest,
      requestStream: false,
      responseType: CreateSessionResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              26,
              58,
              1,
              42,
              34,
              21,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              117,
              116,
              104,
              47,
              115,
              101,
              115,
              115,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /**
     * DeleteSession terminates the current user session.
     * This is an idempotent operation that invalidates the user's authentication.
     */
    deleteSession: {
      name: "DeleteSession",
      requestType: DeleteSessionRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              31,
              42,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              117,
              116,
              104,
              47,
              115,
              101,
              115,
              115,
              105,
              111,
              110,
              115,
              47,
              99,
              117,
              114,
              114,
              101,
              110,
              116,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
