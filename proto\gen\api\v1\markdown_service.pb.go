// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/markdown_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeType int32

const (
	NodeType_NODE_UNSPECIFIED NodeType = 0
	// Block nodes.
	NodeType_LINE_BREAK          NodeType = 1
	NodeType_PARAGRAPH           NodeType = 2
	NodeType_CODE_BLOCK          NodeType = 3
	NodeType_HEADING             NodeType = 4
	NodeType_HORIZONTAL_RULE     NodeType = 5
	NodeType_BLOCKQUOTE          NodeType = 6
	NodeType_LIST                NodeType = 7
	NodeType_ORDERED_LIST_ITEM   NodeType = 8
	NodeType_UNORDERED_LIST_ITEM NodeType = 9
	NodeType_TASK_LIST_ITEM      NodeType = 10
	NodeType_MATH_BLOCK          NodeType = 11
	NodeType_TABLE               NodeType = 12
	NodeType_EMBEDDED_CONTENT    NodeType = 13
	// Inline nodes.
	NodeType_TEXT               NodeType = 51
	NodeType_BOLD               NodeType = 52
	NodeType_ITALIC             NodeType = 53
	NodeType_BOLD_ITALIC        NodeType = 54
	NodeType_CODE               NodeType = 55
	NodeType_IMAGE              NodeType = 56
	NodeType_LINK               NodeType = 57
	NodeType_AUTO_LINK          NodeType = 58
	NodeType_TAG                NodeType = 59
	NodeType_STRIKETHROUGH      NodeType = 60
	NodeType_ESCAPING_CHARACTER NodeType = 61
	NodeType_MATH               NodeType = 62
	NodeType_HIGHLIGHT          NodeType = 63
	NodeType_SUBSCRIPT          NodeType = 64
	NodeType_SUPERSCRIPT        NodeType = 65
	NodeType_REFERENCED_CONTENT NodeType = 66
	NodeType_SPOILER            NodeType = 67
	NodeType_HTML_ELEMENT       NodeType = 68
)

// Enum value maps for NodeType.
var (
	NodeType_name = map[int32]string{
		0:  "NODE_UNSPECIFIED",
		1:  "LINE_BREAK",
		2:  "PARAGRAPH",
		3:  "CODE_BLOCK",
		4:  "HEADING",
		5:  "HORIZONTAL_RULE",
		6:  "BLOCKQUOTE",
		7:  "LIST",
		8:  "ORDERED_LIST_ITEM",
		9:  "UNORDERED_LIST_ITEM",
		10: "TASK_LIST_ITEM",
		11: "MATH_BLOCK",
		12: "TABLE",
		13: "EMBEDDED_CONTENT",
		51: "TEXT",
		52: "BOLD",
		53: "ITALIC",
		54: "BOLD_ITALIC",
		55: "CODE",
		56: "IMAGE",
		57: "LINK",
		58: "AUTO_LINK",
		59: "TAG",
		60: "STRIKETHROUGH",
		61: "ESCAPING_CHARACTER",
		62: "MATH",
		63: "HIGHLIGHT",
		64: "SUBSCRIPT",
		65: "SUPERSCRIPT",
		66: "REFERENCED_CONTENT",
		67: "SPOILER",
		68: "HTML_ELEMENT",
	}
	NodeType_value = map[string]int32{
		"NODE_UNSPECIFIED":    0,
		"LINE_BREAK":          1,
		"PARAGRAPH":           2,
		"CODE_BLOCK":          3,
		"HEADING":             4,
		"HORIZONTAL_RULE":     5,
		"BLOCKQUOTE":          6,
		"LIST":                7,
		"ORDERED_LIST_ITEM":   8,
		"UNORDERED_LIST_ITEM": 9,
		"TASK_LIST_ITEM":      10,
		"MATH_BLOCK":          11,
		"TABLE":               12,
		"EMBEDDED_CONTENT":    13,
		"TEXT":                51,
		"BOLD":                52,
		"ITALIC":              53,
		"BOLD_ITALIC":         54,
		"CODE":                55,
		"IMAGE":               56,
		"LINK":                57,
		"AUTO_LINK":           58,
		"TAG":                 59,
		"STRIKETHROUGH":       60,
		"ESCAPING_CHARACTER":  61,
		"MATH":                62,
		"HIGHLIGHT":           63,
		"SUBSCRIPT":           64,
		"SUPERSCRIPT":         65,
		"REFERENCED_CONTENT":  66,
		"SPOILER":             67,
		"HTML_ELEMENT":        68,
	}
)

func (x NodeType) Enum() *NodeType {
	p := new(NodeType)
	*p = x
	return p
}

func (x NodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_markdown_service_proto_enumTypes[0].Descriptor()
}

func (NodeType) Type() protoreflect.EnumType {
	return &file_api_v1_markdown_service_proto_enumTypes[0]
}

func (x NodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeType.Descriptor instead.
func (NodeType) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{0}
}

type ListNode_Kind int32

const (
	ListNode_KIND_UNSPECIFIED ListNode_Kind = 0
	ListNode_ORDERED          ListNode_Kind = 1
	ListNode_UNORDERED        ListNode_Kind = 2
	ListNode_DESCRIPTION      ListNode_Kind = 3
)

// Enum value maps for ListNode_Kind.
var (
	ListNode_Kind_name = map[int32]string{
		0: "KIND_UNSPECIFIED",
		1: "ORDERED",
		2: "UNORDERED",
		3: "DESCRIPTION",
	}
	ListNode_Kind_value = map[string]int32{
		"KIND_UNSPECIFIED": 0,
		"ORDERED":          1,
		"UNORDERED":        2,
		"DESCRIPTION":      3,
	}
)

func (x ListNode_Kind) Enum() *ListNode_Kind {
	p := new(ListNode_Kind)
	*p = x
	return p
}

func (x ListNode_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListNode_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_markdown_service_proto_enumTypes[1].Descriptor()
}

func (ListNode_Kind) Type() protoreflect.EnumType {
	return &file_api_v1_markdown_service_proto_enumTypes[1]
}

func (x ListNode_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListNode_Kind.Descriptor instead.
func (ListNode_Kind) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{15, 0}
}

type ParseMarkdownRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The markdown content to parse.
	Markdown      string `protobuf:"bytes,1,opt,name=markdown,proto3" json:"markdown,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseMarkdownRequest) Reset() {
	*x = ParseMarkdownRequest{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseMarkdownRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseMarkdownRequest) ProtoMessage() {}

func (x *ParseMarkdownRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseMarkdownRequest.ProtoReflect.Descriptor instead.
func (*ParseMarkdownRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{0}
}

func (x *ParseMarkdownRequest) GetMarkdown() string {
	if x != nil {
		return x.Markdown
	}
	return ""
}

type ParseMarkdownResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The parsed markdown nodes.
	Nodes         []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseMarkdownResponse) Reset() {
	*x = ParseMarkdownResponse{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseMarkdownResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseMarkdownResponse) ProtoMessage() {}

func (x *ParseMarkdownResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseMarkdownResponse.ProtoReflect.Descriptor instead.
func (*ParseMarkdownResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{1}
}

func (x *ParseMarkdownResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type RestoreMarkdownNodesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The nodes to restore to markdown content.
	Nodes         []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreMarkdownNodesRequest) Reset() {
	*x = RestoreMarkdownNodesRequest{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMarkdownNodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMarkdownNodesRequest) ProtoMessage() {}

func (x *RestoreMarkdownNodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMarkdownNodesRequest.ProtoReflect.Descriptor instead.
func (*RestoreMarkdownNodesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{2}
}

func (x *RestoreMarkdownNodesRequest) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type RestoreMarkdownNodesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The restored markdown content.
	Markdown      string `protobuf:"bytes,1,opt,name=markdown,proto3" json:"markdown,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreMarkdownNodesResponse) Reset() {
	*x = RestoreMarkdownNodesResponse{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMarkdownNodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMarkdownNodesResponse) ProtoMessage() {}

func (x *RestoreMarkdownNodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMarkdownNodesResponse.ProtoReflect.Descriptor instead.
func (*RestoreMarkdownNodesResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{3}
}

func (x *RestoreMarkdownNodesResponse) GetMarkdown() string {
	if x != nil {
		return x.Markdown
	}
	return ""
}

type StringifyMarkdownNodesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The nodes to stringify to plain text.
	Nodes         []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringifyMarkdownNodesRequest) Reset() {
	*x = StringifyMarkdownNodesRequest{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringifyMarkdownNodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringifyMarkdownNodesRequest) ProtoMessage() {}

func (x *StringifyMarkdownNodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringifyMarkdownNodesRequest.ProtoReflect.Descriptor instead.
func (*StringifyMarkdownNodesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{4}
}

func (x *StringifyMarkdownNodesRequest) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type StringifyMarkdownNodesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The plain text content.
	PlainText     string `protobuf:"bytes,1,opt,name=plain_text,json=plainText,proto3" json:"plain_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringifyMarkdownNodesResponse) Reset() {
	*x = StringifyMarkdownNodesResponse{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringifyMarkdownNodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringifyMarkdownNodesResponse) ProtoMessage() {}

func (x *StringifyMarkdownNodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringifyMarkdownNodesResponse.ProtoReflect.Descriptor instead.
func (*StringifyMarkdownNodesResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{5}
}

func (x *StringifyMarkdownNodesResponse) GetPlainText() string {
	if x != nil {
		return x.PlainText
	}
	return ""
}

type GetLinkMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The link URL to get metadata for.
	Link          string `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLinkMetadataRequest) Reset() {
	*x = GetLinkMetadataRequest{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLinkMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkMetadataRequest) ProtoMessage() {}

func (x *GetLinkMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetLinkMetadataRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetLinkMetadataRequest) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

type LinkMetadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The title of the linked page.
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// The description of the linked page.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// The URL of the preview image for the linked page.
	Image         string `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkMetadata) Reset() {
	*x = LinkMetadata{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkMetadata) ProtoMessage() {}

func (x *LinkMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkMetadata.ProtoReflect.Descriptor instead.
func (*LinkMetadata) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{7}
}

func (x *LinkMetadata) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *LinkMetadata) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LinkMetadata) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type Node struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Type  NodeType               `protobuf:"varint,1,opt,name=type,proto3,enum=memos.api.v1.NodeType" json:"type,omitempty"`
	// Types that are valid to be assigned to Node:
	//
	//	*Node_LineBreakNode
	//	*Node_ParagraphNode
	//	*Node_CodeBlockNode
	//	*Node_HeadingNode
	//	*Node_HorizontalRuleNode
	//	*Node_BlockquoteNode
	//	*Node_ListNode
	//	*Node_OrderedListItemNode
	//	*Node_UnorderedListItemNode
	//	*Node_TaskListItemNode
	//	*Node_MathBlockNode
	//	*Node_TableNode
	//	*Node_EmbeddedContentNode
	//	*Node_TextNode
	//	*Node_BoldNode
	//	*Node_ItalicNode
	//	*Node_BoldItalicNode
	//	*Node_CodeNode
	//	*Node_ImageNode
	//	*Node_LinkNode
	//	*Node_AutoLinkNode
	//	*Node_TagNode
	//	*Node_StrikethroughNode
	//	*Node_EscapingCharacterNode
	//	*Node_MathNode
	//	*Node_HighlightNode
	//	*Node_SubscriptNode
	//	*Node_SuperscriptNode
	//	*Node_ReferencedContentNode
	//	*Node_SpoilerNode
	//	*Node_HtmlElementNode
	Node          isNode_Node `protobuf_oneof:"node"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{8}
}

func (x *Node) GetType() NodeType {
	if x != nil {
		return x.Type
	}
	return NodeType_NODE_UNSPECIFIED
}

func (x *Node) GetNode() isNode_Node {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *Node) GetLineBreakNode() *LineBreakNode {
	if x != nil {
		if x, ok := x.Node.(*Node_LineBreakNode); ok {
			return x.LineBreakNode
		}
	}
	return nil
}

func (x *Node) GetParagraphNode() *ParagraphNode {
	if x != nil {
		if x, ok := x.Node.(*Node_ParagraphNode); ok {
			return x.ParagraphNode
		}
	}
	return nil
}

func (x *Node) GetCodeBlockNode() *CodeBlockNode {
	if x != nil {
		if x, ok := x.Node.(*Node_CodeBlockNode); ok {
			return x.CodeBlockNode
		}
	}
	return nil
}

func (x *Node) GetHeadingNode() *HeadingNode {
	if x != nil {
		if x, ok := x.Node.(*Node_HeadingNode); ok {
			return x.HeadingNode
		}
	}
	return nil
}

func (x *Node) GetHorizontalRuleNode() *HorizontalRuleNode {
	if x != nil {
		if x, ok := x.Node.(*Node_HorizontalRuleNode); ok {
			return x.HorizontalRuleNode
		}
	}
	return nil
}

func (x *Node) GetBlockquoteNode() *BlockquoteNode {
	if x != nil {
		if x, ok := x.Node.(*Node_BlockquoteNode); ok {
			return x.BlockquoteNode
		}
	}
	return nil
}

func (x *Node) GetListNode() *ListNode {
	if x != nil {
		if x, ok := x.Node.(*Node_ListNode); ok {
			return x.ListNode
		}
	}
	return nil
}

func (x *Node) GetOrderedListItemNode() *OrderedListItemNode {
	if x != nil {
		if x, ok := x.Node.(*Node_OrderedListItemNode); ok {
			return x.OrderedListItemNode
		}
	}
	return nil
}

func (x *Node) GetUnorderedListItemNode() *UnorderedListItemNode {
	if x != nil {
		if x, ok := x.Node.(*Node_UnorderedListItemNode); ok {
			return x.UnorderedListItemNode
		}
	}
	return nil
}

func (x *Node) GetTaskListItemNode() *TaskListItemNode {
	if x != nil {
		if x, ok := x.Node.(*Node_TaskListItemNode); ok {
			return x.TaskListItemNode
		}
	}
	return nil
}

func (x *Node) GetMathBlockNode() *MathBlockNode {
	if x != nil {
		if x, ok := x.Node.(*Node_MathBlockNode); ok {
			return x.MathBlockNode
		}
	}
	return nil
}

func (x *Node) GetTableNode() *TableNode {
	if x != nil {
		if x, ok := x.Node.(*Node_TableNode); ok {
			return x.TableNode
		}
	}
	return nil
}

func (x *Node) GetEmbeddedContentNode() *EmbeddedContentNode {
	if x != nil {
		if x, ok := x.Node.(*Node_EmbeddedContentNode); ok {
			return x.EmbeddedContentNode
		}
	}
	return nil
}

func (x *Node) GetTextNode() *TextNode {
	if x != nil {
		if x, ok := x.Node.(*Node_TextNode); ok {
			return x.TextNode
		}
	}
	return nil
}

func (x *Node) GetBoldNode() *BoldNode {
	if x != nil {
		if x, ok := x.Node.(*Node_BoldNode); ok {
			return x.BoldNode
		}
	}
	return nil
}

func (x *Node) GetItalicNode() *ItalicNode {
	if x != nil {
		if x, ok := x.Node.(*Node_ItalicNode); ok {
			return x.ItalicNode
		}
	}
	return nil
}

func (x *Node) GetBoldItalicNode() *BoldItalicNode {
	if x != nil {
		if x, ok := x.Node.(*Node_BoldItalicNode); ok {
			return x.BoldItalicNode
		}
	}
	return nil
}

func (x *Node) GetCodeNode() *CodeNode {
	if x != nil {
		if x, ok := x.Node.(*Node_CodeNode); ok {
			return x.CodeNode
		}
	}
	return nil
}

func (x *Node) GetImageNode() *ImageNode {
	if x != nil {
		if x, ok := x.Node.(*Node_ImageNode); ok {
			return x.ImageNode
		}
	}
	return nil
}

func (x *Node) GetLinkNode() *LinkNode {
	if x != nil {
		if x, ok := x.Node.(*Node_LinkNode); ok {
			return x.LinkNode
		}
	}
	return nil
}

func (x *Node) GetAutoLinkNode() *AutoLinkNode {
	if x != nil {
		if x, ok := x.Node.(*Node_AutoLinkNode); ok {
			return x.AutoLinkNode
		}
	}
	return nil
}

func (x *Node) GetTagNode() *TagNode {
	if x != nil {
		if x, ok := x.Node.(*Node_TagNode); ok {
			return x.TagNode
		}
	}
	return nil
}

func (x *Node) GetStrikethroughNode() *StrikethroughNode {
	if x != nil {
		if x, ok := x.Node.(*Node_StrikethroughNode); ok {
			return x.StrikethroughNode
		}
	}
	return nil
}

func (x *Node) GetEscapingCharacterNode() *EscapingCharacterNode {
	if x != nil {
		if x, ok := x.Node.(*Node_EscapingCharacterNode); ok {
			return x.EscapingCharacterNode
		}
	}
	return nil
}

func (x *Node) GetMathNode() *MathNode {
	if x != nil {
		if x, ok := x.Node.(*Node_MathNode); ok {
			return x.MathNode
		}
	}
	return nil
}

func (x *Node) GetHighlightNode() *HighlightNode {
	if x != nil {
		if x, ok := x.Node.(*Node_HighlightNode); ok {
			return x.HighlightNode
		}
	}
	return nil
}

func (x *Node) GetSubscriptNode() *SubscriptNode {
	if x != nil {
		if x, ok := x.Node.(*Node_SubscriptNode); ok {
			return x.SubscriptNode
		}
	}
	return nil
}

func (x *Node) GetSuperscriptNode() *SuperscriptNode {
	if x != nil {
		if x, ok := x.Node.(*Node_SuperscriptNode); ok {
			return x.SuperscriptNode
		}
	}
	return nil
}

func (x *Node) GetReferencedContentNode() *ReferencedContentNode {
	if x != nil {
		if x, ok := x.Node.(*Node_ReferencedContentNode); ok {
			return x.ReferencedContentNode
		}
	}
	return nil
}

func (x *Node) GetSpoilerNode() *SpoilerNode {
	if x != nil {
		if x, ok := x.Node.(*Node_SpoilerNode); ok {
			return x.SpoilerNode
		}
	}
	return nil
}

func (x *Node) GetHtmlElementNode() *HTMLElementNode {
	if x != nil {
		if x, ok := x.Node.(*Node_HtmlElementNode); ok {
			return x.HtmlElementNode
		}
	}
	return nil
}

type isNode_Node interface {
	isNode_Node()
}

type Node_LineBreakNode struct {
	// Block nodes.
	LineBreakNode *LineBreakNode `protobuf:"bytes,11,opt,name=line_break_node,json=lineBreakNode,proto3,oneof"`
}

type Node_ParagraphNode struct {
	ParagraphNode *ParagraphNode `protobuf:"bytes,12,opt,name=paragraph_node,json=paragraphNode,proto3,oneof"`
}

type Node_CodeBlockNode struct {
	CodeBlockNode *CodeBlockNode `protobuf:"bytes,13,opt,name=code_block_node,json=codeBlockNode,proto3,oneof"`
}

type Node_HeadingNode struct {
	HeadingNode *HeadingNode `protobuf:"bytes,14,opt,name=heading_node,json=headingNode,proto3,oneof"`
}

type Node_HorizontalRuleNode struct {
	HorizontalRuleNode *HorizontalRuleNode `protobuf:"bytes,15,opt,name=horizontal_rule_node,json=horizontalRuleNode,proto3,oneof"`
}

type Node_BlockquoteNode struct {
	BlockquoteNode *BlockquoteNode `protobuf:"bytes,16,opt,name=blockquote_node,json=blockquoteNode,proto3,oneof"`
}

type Node_ListNode struct {
	ListNode *ListNode `protobuf:"bytes,17,opt,name=list_node,json=listNode,proto3,oneof"`
}

type Node_OrderedListItemNode struct {
	OrderedListItemNode *OrderedListItemNode `protobuf:"bytes,18,opt,name=ordered_list_item_node,json=orderedListItemNode,proto3,oneof"`
}

type Node_UnorderedListItemNode struct {
	UnorderedListItemNode *UnorderedListItemNode `protobuf:"bytes,19,opt,name=unordered_list_item_node,json=unorderedListItemNode,proto3,oneof"`
}

type Node_TaskListItemNode struct {
	TaskListItemNode *TaskListItemNode `protobuf:"bytes,20,opt,name=task_list_item_node,json=taskListItemNode,proto3,oneof"`
}

type Node_MathBlockNode struct {
	MathBlockNode *MathBlockNode `protobuf:"bytes,21,opt,name=math_block_node,json=mathBlockNode,proto3,oneof"`
}

type Node_TableNode struct {
	TableNode *TableNode `protobuf:"bytes,22,opt,name=table_node,json=tableNode,proto3,oneof"`
}

type Node_EmbeddedContentNode struct {
	EmbeddedContentNode *EmbeddedContentNode `protobuf:"bytes,23,opt,name=embedded_content_node,json=embeddedContentNode,proto3,oneof"`
}

type Node_TextNode struct {
	// Inline nodes.
	TextNode *TextNode `protobuf:"bytes,51,opt,name=text_node,json=textNode,proto3,oneof"`
}

type Node_BoldNode struct {
	BoldNode *BoldNode `protobuf:"bytes,52,opt,name=bold_node,json=boldNode,proto3,oneof"`
}

type Node_ItalicNode struct {
	ItalicNode *ItalicNode `protobuf:"bytes,53,opt,name=italic_node,json=italicNode,proto3,oneof"`
}

type Node_BoldItalicNode struct {
	BoldItalicNode *BoldItalicNode `protobuf:"bytes,54,opt,name=bold_italic_node,json=boldItalicNode,proto3,oneof"`
}

type Node_CodeNode struct {
	CodeNode *CodeNode `protobuf:"bytes,55,opt,name=code_node,json=codeNode,proto3,oneof"`
}

type Node_ImageNode struct {
	ImageNode *ImageNode `protobuf:"bytes,56,opt,name=image_node,json=imageNode,proto3,oneof"`
}

type Node_LinkNode struct {
	LinkNode *LinkNode `protobuf:"bytes,57,opt,name=link_node,json=linkNode,proto3,oneof"`
}

type Node_AutoLinkNode struct {
	AutoLinkNode *AutoLinkNode `protobuf:"bytes,58,opt,name=auto_link_node,json=autoLinkNode,proto3,oneof"`
}

type Node_TagNode struct {
	TagNode *TagNode `protobuf:"bytes,59,opt,name=tag_node,json=tagNode,proto3,oneof"`
}

type Node_StrikethroughNode struct {
	StrikethroughNode *StrikethroughNode `protobuf:"bytes,60,opt,name=strikethrough_node,json=strikethroughNode,proto3,oneof"`
}

type Node_EscapingCharacterNode struct {
	EscapingCharacterNode *EscapingCharacterNode `protobuf:"bytes,61,opt,name=escaping_character_node,json=escapingCharacterNode,proto3,oneof"`
}

type Node_MathNode struct {
	MathNode *MathNode `protobuf:"bytes,62,opt,name=math_node,json=mathNode,proto3,oneof"`
}

type Node_HighlightNode struct {
	HighlightNode *HighlightNode `protobuf:"bytes,63,opt,name=highlight_node,json=highlightNode,proto3,oneof"`
}

type Node_SubscriptNode struct {
	SubscriptNode *SubscriptNode `protobuf:"bytes,64,opt,name=subscript_node,json=subscriptNode,proto3,oneof"`
}

type Node_SuperscriptNode struct {
	SuperscriptNode *SuperscriptNode `protobuf:"bytes,65,opt,name=superscript_node,json=superscriptNode,proto3,oneof"`
}

type Node_ReferencedContentNode struct {
	ReferencedContentNode *ReferencedContentNode `protobuf:"bytes,66,opt,name=referenced_content_node,json=referencedContentNode,proto3,oneof"`
}

type Node_SpoilerNode struct {
	SpoilerNode *SpoilerNode `protobuf:"bytes,67,opt,name=spoiler_node,json=spoilerNode,proto3,oneof"`
}

type Node_HtmlElementNode struct {
	HtmlElementNode *HTMLElementNode `protobuf:"bytes,68,opt,name=html_element_node,json=htmlElementNode,proto3,oneof"`
}

func (*Node_LineBreakNode) isNode_Node() {}

func (*Node_ParagraphNode) isNode_Node() {}

func (*Node_CodeBlockNode) isNode_Node() {}

func (*Node_HeadingNode) isNode_Node() {}

func (*Node_HorizontalRuleNode) isNode_Node() {}

func (*Node_BlockquoteNode) isNode_Node() {}

func (*Node_ListNode) isNode_Node() {}

func (*Node_OrderedListItemNode) isNode_Node() {}

func (*Node_UnorderedListItemNode) isNode_Node() {}

func (*Node_TaskListItemNode) isNode_Node() {}

func (*Node_MathBlockNode) isNode_Node() {}

func (*Node_TableNode) isNode_Node() {}

func (*Node_EmbeddedContentNode) isNode_Node() {}

func (*Node_TextNode) isNode_Node() {}

func (*Node_BoldNode) isNode_Node() {}

func (*Node_ItalicNode) isNode_Node() {}

func (*Node_BoldItalicNode) isNode_Node() {}

func (*Node_CodeNode) isNode_Node() {}

func (*Node_ImageNode) isNode_Node() {}

func (*Node_LinkNode) isNode_Node() {}

func (*Node_AutoLinkNode) isNode_Node() {}

func (*Node_TagNode) isNode_Node() {}

func (*Node_StrikethroughNode) isNode_Node() {}

func (*Node_EscapingCharacterNode) isNode_Node() {}

func (*Node_MathNode) isNode_Node() {}

func (*Node_HighlightNode) isNode_Node() {}

func (*Node_SubscriptNode) isNode_Node() {}

func (*Node_SuperscriptNode) isNode_Node() {}

func (*Node_ReferencedContentNode) isNode_Node() {}

func (*Node_SpoilerNode) isNode_Node() {}

func (*Node_HtmlElementNode) isNode_Node() {}

type LineBreakNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LineBreakNode) Reset() {
	*x = LineBreakNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LineBreakNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineBreakNode) ProtoMessage() {}

func (x *LineBreakNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineBreakNode.ProtoReflect.Descriptor instead.
func (*LineBreakNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{9}
}

type ParagraphNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Children      []*Node                `protobuf:"bytes,1,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParagraphNode) Reset() {
	*x = ParagraphNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParagraphNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParagraphNode) ProtoMessage() {}

func (x *ParagraphNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParagraphNode.ProtoReflect.Descriptor instead.
func (*ParagraphNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{10}
}

func (x *ParagraphNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type CodeBlockNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Language      string                 `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodeBlockNode) Reset() {
	*x = CodeBlockNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeBlockNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeBlockNode) ProtoMessage() {}

func (x *CodeBlockNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeBlockNode.ProtoReflect.Descriptor instead.
func (*CodeBlockNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{11}
}

func (x *CodeBlockNode) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CodeBlockNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type HeadingNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         int32                  `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Children      []*Node                `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeadingNode) Reset() {
	*x = HeadingNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeadingNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadingNode) ProtoMessage() {}

func (x *HeadingNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadingNode.ProtoReflect.Descriptor instead.
func (*HeadingNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{12}
}

func (x *HeadingNode) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *HeadingNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type HorizontalRuleNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HorizontalRuleNode) Reset() {
	*x = HorizontalRuleNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HorizontalRuleNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HorizontalRuleNode) ProtoMessage() {}

func (x *HorizontalRuleNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HorizontalRuleNode.ProtoReflect.Descriptor instead.
func (*HorizontalRuleNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{13}
}

func (x *HorizontalRuleNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

type BlockquoteNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Children      []*Node                `protobuf:"bytes,1,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockquoteNode) Reset() {
	*x = BlockquoteNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockquoteNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockquoteNode) ProtoMessage() {}

func (x *BlockquoteNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockquoteNode.ProtoReflect.Descriptor instead.
func (*BlockquoteNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{14}
}

func (x *BlockquoteNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type ListNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kind          ListNode_Kind          `protobuf:"varint,1,opt,name=kind,proto3,enum=memos.api.v1.ListNode_Kind" json:"kind,omitempty"`
	Indent        int32                  `protobuf:"varint,2,opt,name=indent,proto3" json:"indent,omitempty"`
	Children      []*Node                `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNode) Reset() {
	*x = ListNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNode) ProtoMessage() {}

func (x *ListNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNode.ProtoReflect.Descriptor instead.
func (*ListNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListNode) GetKind() ListNode_Kind {
	if x != nil {
		return x.Kind
	}
	return ListNode_KIND_UNSPECIFIED
}

func (x *ListNode) GetIndent() int32 {
	if x != nil {
		return x.Indent
	}
	return 0
}

func (x *ListNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type OrderedListItemNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Number        string                 `protobuf:"bytes,1,opt,name=number,proto3" json:"number,omitempty"`
	Indent        int32                  `protobuf:"varint,2,opt,name=indent,proto3" json:"indent,omitempty"`
	Children      []*Node                `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderedListItemNode) Reset() {
	*x = OrderedListItemNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderedListItemNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderedListItemNode) ProtoMessage() {}

func (x *OrderedListItemNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderedListItemNode.ProtoReflect.Descriptor instead.
func (*OrderedListItemNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{16}
}

func (x *OrderedListItemNode) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *OrderedListItemNode) GetIndent() int32 {
	if x != nil {
		return x.Indent
	}
	return 0
}

func (x *OrderedListItemNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type UnorderedListItemNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Indent        int32                  `protobuf:"varint,2,opt,name=indent,proto3" json:"indent,omitempty"`
	Children      []*Node                `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnorderedListItemNode) Reset() {
	*x = UnorderedListItemNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnorderedListItemNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnorderedListItemNode) ProtoMessage() {}

func (x *UnorderedListItemNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnorderedListItemNode.ProtoReflect.Descriptor instead.
func (*UnorderedListItemNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{17}
}

func (x *UnorderedListItemNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *UnorderedListItemNode) GetIndent() int32 {
	if x != nil {
		return x.Indent
	}
	return 0
}

func (x *UnorderedListItemNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type TaskListItemNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Indent        int32                  `protobuf:"varint,2,opt,name=indent,proto3" json:"indent,omitempty"`
	Complete      bool                   `protobuf:"varint,3,opt,name=complete,proto3" json:"complete,omitempty"`
	Children      []*Node                `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskListItemNode) Reset() {
	*x = TaskListItemNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskListItemNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListItemNode) ProtoMessage() {}

func (x *TaskListItemNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListItemNode.ProtoReflect.Descriptor instead.
func (*TaskListItemNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{18}
}

func (x *TaskListItemNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TaskListItemNode) GetIndent() int32 {
	if x != nil {
		return x.Indent
	}
	return 0
}

func (x *TaskListItemNode) GetComplete() bool {
	if x != nil {
		return x.Complete
	}
	return false
}

func (x *TaskListItemNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type MathBlockNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MathBlockNode) Reset() {
	*x = MathBlockNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MathBlockNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MathBlockNode) ProtoMessage() {}

func (x *MathBlockNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MathBlockNode.ProtoReflect.Descriptor instead.
func (*MathBlockNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{19}
}

func (x *MathBlockNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type TableNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        []*Node                `protobuf:"bytes,1,rep,name=header,proto3" json:"header,omitempty"`
	Delimiter     []string               `protobuf:"bytes,2,rep,name=delimiter,proto3" json:"delimiter,omitempty"`
	Rows          []*TableNode_Row       `protobuf:"bytes,3,rep,name=rows,proto3" json:"rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableNode) Reset() {
	*x = TableNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableNode) ProtoMessage() {}

func (x *TableNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableNode.ProtoReflect.Descriptor instead.
func (*TableNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{20}
}

func (x *TableNode) GetHeader() []*Node {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *TableNode) GetDelimiter() []string {
	if x != nil {
		return x.Delimiter
	}
	return nil
}

func (x *TableNode) GetRows() []*TableNode_Row {
	if x != nil {
		return x.Rows
	}
	return nil
}

type EmbeddedContentNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the embedded content.
	ResourceName string `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	// Additional parameters for the embedded content.
	Params        string `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmbeddedContentNode) Reset() {
	*x = EmbeddedContentNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmbeddedContentNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmbeddedContentNode) ProtoMessage() {}

func (x *EmbeddedContentNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmbeddedContentNode.ProtoReflect.Descriptor instead.
func (*EmbeddedContentNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{21}
}

func (x *EmbeddedContentNode) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *EmbeddedContentNode) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

type TextNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TextNode) Reset() {
	*x = TextNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TextNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextNode) ProtoMessage() {}

func (x *TextNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextNode.ProtoReflect.Descriptor instead.
func (*TextNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{22}
}

func (x *TextNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type BoldNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Children      []*Node                `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoldNode) Reset() {
	*x = BoldNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoldNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoldNode) ProtoMessage() {}

func (x *BoldNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoldNode.ProtoReflect.Descriptor instead.
func (*BoldNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{23}
}

func (x *BoldNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *BoldNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type ItalicNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Children      []*Node                `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItalicNode) Reset() {
	*x = ItalicNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItalicNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItalicNode) ProtoMessage() {}

func (x *ItalicNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItalicNode.ProtoReflect.Descriptor instead.
func (*ItalicNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{24}
}

func (x *ItalicNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ItalicNode) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

type BoldItalicNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoldItalicNode) Reset() {
	*x = BoldItalicNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoldItalicNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoldItalicNode) ProtoMessage() {}

func (x *BoldItalicNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoldItalicNode.ProtoReflect.Descriptor instead.
func (*BoldItalicNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{25}
}

func (x *BoldItalicNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *BoldItalicNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type CodeNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodeNode) Reset() {
	*x = CodeNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeNode) ProtoMessage() {}

func (x *CodeNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeNode.ProtoReflect.Descriptor instead.
func (*CodeNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{26}
}

func (x *CodeNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ImageNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AltText       string                 `protobuf:"bytes,1,opt,name=alt_text,json=altText,proto3" json:"alt_text,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageNode) Reset() {
	*x = ImageNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageNode) ProtoMessage() {}

func (x *ImageNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageNode.ProtoReflect.Descriptor instead.
func (*ImageNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{27}
}

func (x *ImageNode) GetAltText() string {
	if x != nil {
		return x.AltText
	}
	return ""
}

func (x *ImageNode) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type LinkNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       []*Node                `protobuf:"bytes,1,rep,name=content,proto3" json:"content,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkNode) Reset() {
	*x = LinkNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkNode) ProtoMessage() {}

func (x *LinkNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkNode.ProtoReflect.Descriptor instead.
func (*LinkNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{28}
}

func (x *LinkNode) GetContent() []*Node {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *LinkNode) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type AutoLinkNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	IsRawText     bool                   `protobuf:"varint,2,opt,name=is_raw_text,json=isRawText,proto3" json:"is_raw_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AutoLinkNode) Reset() {
	*x = AutoLinkNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoLinkNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoLinkNode) ProtoMessage() {}

func (x *AutoLinkNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoLinkNode.ProtoReflect.Descriptor instead.
func (*AutoLinkNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{29}
}

func (x *AutoLinkNode) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AutoLinkNode) GetIsRawText() bool {
	if x != nil {
		return x.IsRawText
	}
	return false
}

type TagNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TagNode) Reset() {
	*x = TagNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagNode) ProtoMessage() {}

func (x *TagNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagNode.ProtoReflect.Descriptor instead.
func (*TagNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{30}
}

func (x *TagNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type StrikethroughNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StrikethroughNode) Reset() {
	*x = StrikethroughNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StrikethroughNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrikethroughNode) ProtoMessage() {}

func (x *StrikethroughNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrikethroughNode.ProtoReflect.Descriptor instead.
func (*StrikethroughNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{31}
}

func (x *StrikethroughNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type EscapingCharacterNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EscapingCharacterNode) Reset() {
	*x = EscapingCharacterNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EscapingCharacterNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EscapingCharacterNode) ProtoMessage() {}

func (x *EscapingCharacterNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EscapingCharacterNode.ProtoReflect.Descriptor instead.
func (*EscapingCharacterNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{32}
}

func (x *EscapingCharacterNode) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

type MathNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MathNode) Reset() {
	*x = MathNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MathNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MathNode) ProtoMessage() {}

func (x *MathNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MathNode.ProtoReflect.Descriptor instead.
func (*MathNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{33}
}

func (x *MathNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type HighlightNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightNode) Reset() {
	*x = HighlightNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightNode) ProtoMessage() {}

func (x *HighlightNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightNode.ProtoReflect.Descriptor instead.
func (*HighlightNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{34}
}

func (x *HighlightNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SubscriptNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptNode) Reset() {
	*x = SubscriptNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptNode) ProtoMessage() {}

func (x *SubscriptNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptNode.ProtoReflect.Descriptor instead.
func (*SubscriptNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{35}
}

func (x *SubscriptNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SuperscriptNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuperscriptNode) Reset() {
	*x = SuperscriptNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuperscriptNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuperscriptNode) ProtoMessage() {}

func (x *SuperscriptNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuperscriptNode.ProtoReflect.Descriptor instead.
func (*SuperscriptNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{36}
}

func (x *SuperscriptNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ReferencedContentNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the referenced content.
	ResourceName string `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	// Additional parameters for the referenced content.
	Params        string `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReferencedContentNode) Reset() {
	*x = ReferencedContentNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReferencedContentNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferencedContentNode) ProtoMessage() {}

func (x *ReferencedContentNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferencedContentNode.ProtoReflect.Descriptor instead.
func (*ReferencedContentNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{37}
}

func (x *ReferencedContentNode) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *ReferencedContentNode) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

type SpoilerNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpoilerNode) Reset() {
	*x = SpoilerNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpoilerNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpoilerNode) ProtoMessage() {}

func (x *SpoilerNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpoilerNode.ProtoReflect.Descriptor instead.
func (*SpoilerNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{38}
}

func (x *SpoilerNode) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type HTMLElementNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TagName       string                 `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Attributes    map[string]string      `protobuf:"bytes,2,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HTMLElementNode) Reset() {
	*x = HTMLElementNode{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HTMLElementNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTMLElementNode) ProtoMessage() {}

func (x *HTMLElementNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTMLElementNode.ProtoReflect.Descriptor instead.
func (*HTMLElementNode) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{39}
}

func (x *HTMLElementNode) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *HTMLElementNode) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

type TableNode_Row struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cells         []*Node                `protobuf:"bytes,1,rep,name=cells,proto3" json:"cells,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableNode_Row) Reset() {
	*x = TableNode_Row{}
	mi := &file_api_v1_markdown_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableNode_Row) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableNode_Row) ProtoMessage() {}

func (x *TableNode_Row) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_markdown_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableNode_Row.ProtoReflect.Descriptor instead.
func (*TableNode_Row) Descriptor() ([]byte, []int) {
	return file_api_v1_markdown_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *TableNode_Row) GetCells() []*Node {
	if x != nil {
		return x.Cells
	}
	return nil
}

var File_api_v1_markdown_service_proto protoreflect.FileDescriptor

const file_api_v1_markdown_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/v1/markdown_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\"7\n" +
	"\x14ParseMarkdownRequest\x12\x1f\n" +
	"\bmarkdown\x18\x01 \x01(\tB\x03\xe0A\x02R\bmarkdown\"A\n" +
	"\x15ParseMarkdownResponse\x12(\n" +
	"\x05nodes\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\x05nodes\"L\n" +
	"\x1bRestoreMarkdownNodesRequest\x12-\n" +
	"\x05nodes\x18\x01 \x03(\v2\x12.memos.api.v1.NodeB\x03\xe0A\x02R\x05nodes\":\n" +
	"\x1cRestoreMarkdownNodesResponse\x12\x1a\n" +
	"\bmarkdown\x18\x01 \x01(\tR\bmarkdown\"N\n" +
	"\x1dStringifyMarkdownNodesRequest\x12-\n" +
	"\x05nodes\x18\x01 \x03(\v2\x12.memos.api.v1.NodeB\x03\xe0A\x02R\x05nodes\"?\n" +
	"\x1eStringifyMarkdownNodesResponse\x12\x1d\n" +
	"\n" +
	"plain_text\x18\x01 \x01(\tR\tplainText\"1\n" +
	"\x16GetLinkMetadataRequest\x12\x17\n" +
	"\x04link\x18\x01 \x01(\tB\x03\xe0A\x02R\x04link\"\\\n" +
	"\fLinkMetadata\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x03 \x01(\tR\x05image\"\xca\x11\n" +
	"\x04Node\x12*\n" +
	"\x04type\x18\x01 \x01(\x0e2\x16.memos.api.v1.NodeTypeR\x04type\x12E\n" +
	"\x0fline_break_node\x18\v \x01(\v2\x1b.memos.api.v1.LineBreakNodeH\x00R\rlineBreakNode\x12D\n" +
	"\x0eparagraph_node\x18\f \x01(\v2\x1b.memos.api.v1.ParagraphNodeH\x00R\rparagraphNode\x12E\n" +
	"\x0fcode_block_node\x18\r \x01(\v2\x1b.memos.api.v1.CodeBlockNodeH\x00R\rcodeBlockNode\x12>\n" +
	"\fheading_node\x18\x0e \x01(\v2\x19.memos.api.v1.HeadingNodeH\x00R\vheadingNode\x12T\n" +
	"\x14horizontal_rule_node\x18\x0f \x01(\v2 .memos.api.v1.HorizontalRuleNodeH\x00R\x12horizontalRuleNode\x12G\n" +
	"\x0fblockquote_node\x18\x10 \x01(\v2\x1c.memos.api.v1.BlockquoteNodeH\x00R\x0eblockquoteNode\x125\n" +
	"\tlist_node\x18\x11 \x01(\v2\x16.memos.api.v1.ListNodeH\x00R\blistNode\x12X\n" +
	"\x16ordered_list_item_node\x18\x12 \x01(\v2!.memos.api.v1.OrderedListItemNodeH\x00R\x13orderedListItemNode\x12^\n" +
	"\x18unordered_list_item_node\x18\x13 \x01(\v2#.memos.api.v1.UnorderedListItemNodeH\x00R\x15unorderedListItemNode\x12O\n" +
	"\x13task_list_item_node\x18\x14 \x01(\v2\x1e.memos.api.v1.TaskListItemNodeH\x00R\x10taskListItemNode\x12E\n" +
	"\x0fmath_block_node\x18\x15 \x01(\v2\x1b.memos.api.v1.MathBlockNodeH\x00R\rmathBlockNode\x128\n" +
	"\n" +
	"table_node\x18\x16 \x01(\v2\x17.memos.api.v1.TableNodeH\x00R\ttableNode\x12W\n" +
	"\x15embedded_content_node\x18\x17 \x01(\v2!.memos.api.v1.EmbeddedContentNodeH\x00R\x13embeddedContentNode\x125\n" +
	"\ttext_node\x183 \x01(\v2\x16.memos.api.v1.TextNodeH\x00R\btextNode\x125\n" +
	"\tbold_node\x184 \x01(\v2\x16.memos.api.v1.BoldNodeH\x00R\bboldNode\x12;\n" +
	"\vitalic_node\x185 \x01(\v2\x18.memos.api.v1.ItalicNodeH\x00R\n" +
	"italicNode\x12H\n" +
	"\x10bold_italic_node\x186 \x01(\v2\x1c.memos.api.v1.BoldItalicNodeH\x00R\x0eboldItalicNode\x125\n" +
	"\tcode_node\x187 \x01(\v2\x16.memos.api.v1.CodeNodeH\x00R\bcodeNode\x128\n" +
	"\n" +
	"image_node\x188 \x01(\v2\x17.memos.api.v1.ImageNodeH\x00R\timageNode\x125\n" +
	"\tlink_node\x189 \x01(\v2\x16.memos.api.v1.LinkNodeH\x00R\blinkNode\x12B\n" +
	"\x0eauto_link_node\x18: \x01(\v2\x1a.memos.api.v1.AutoLinkNodeH\x00R\fautoLinkNode\x122\n" +
	"\btag_node\x18; \x01(\v2\x15.memos.api.v1.TagNodeH\x00R\atagNode\x12P\n" +
	"\x12strikethrough_node\x18< \x01(\v2\x1f.memos.api.v1.StrikethroughNodeH\x00R\x11strikethroughNode\x12]\n" +
	"\x17escaping_character_node\x18= \x01(\v2#.memos.api.v1.EscapingCharacterNodeH\x00R\x15escapingCharacterNode\x125\n" +
	"\tmath_node\x18> \x01(\v2\x16.memos.api.v1.MathNodeH\x00R\bmathNode\x12D\n" +
	"\x0ehighlight_node\x18? \x01(\v2\x1b.memos.api.v1.HighlightNodeH\x00R\rhighlightNode\x12D\n" +
	"\x0esubscript_node\x18@ \x01(\v2\x1b.memos.api.v1.SubscriptNodeH\x00R\rsubscriptNode\x12J\n" +
	"\x10superscript_node\x18A \x01(\v2\x1d.memos.api.v1.SuperscriptNodeH\x00R\x0fsuperscriptNode\x12]\n" +
	"\x17referenced_content_node\x18B \x01(\v2#.memos.api.v1.ReferencedContentNodeH\x00R\x15referencedContentNode\x12>\n" +
	"\fspoiler_node\x18C \x01(\v2\x19.memos.api.v1.SpoilerNodeH\x00R\vspoilerNode\x12K\n" +
	"\x11html_element_node\x18D \x01(\v2\x1d.memos.api.v1.HTMLElementNodeH\x00R\x0fhtmlElementNodeB\x06\n" +
	"\x04node\"\x0f\n" +
	"\rLineBreakNode\"?\n" +
	"\rParagraphNode\x12.\n" +
	"\bchildren\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"E\n" +
	"\rCodeBlockNode\x12\x1a\n" +
	"\blanguage\x18\x01 \x01(\tR\blanguage\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"S\n" +
	"\vHeadingNode\x12\x14\n" +
	"\x05level\x18\x01 \x01(\x05R\x05level\x12.\n" +
	"\bchildren\x18\x02 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\",\n" +
	"\x12HorizontalRuleNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\"@\n" +
	"\x0eBlockquoteNode\x12.\n" +
	"\bchildren\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"\xce\x01\n" +
	"\bListNode\x12/\n" +
	"\x04kind\x18\x01 \x01(\x0e2\x1b.memos.api.v1.ListNode.KindR\x04kind\x12\x16\n" +
	"\x06indent\x18\x02 \x01(\x05R\x06indent\x12.\n" +
	"\bchildren\x18\x03 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"I\n" +
	"\x04Kind\x12\x14\n" +
	"\x10KIND_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aORDERED\x10\x01\x12\r\n" +
	"\tUNORDERED\x10\x02\x12\x0f\n" +
	"\vDESCRIPTION\x10\x03\"u\n" +
	"\x13OrderedListItemNode\x12\x16\n" +
	"\x06number\x18\x01 \x01(\tR\x06number\x12\x16\n" +
	"\x06indent\x18\x02 \x01(\x05R\x06indent\x12.\n" +
	"\bchildren\x18\x03 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"w\n" +
	"\x15UnorderedListItemNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12\x16\n" +
	"\x06indent\x18\x02 \x01(\x05R\x06indent\x12.\n" +
	"\bchildren\x18\x03 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"\x8e\x01\n" +
	"\x10TaskListItemNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12\x16\n" +
	"\x06indent\x18\x02 \x01(\x05R\x06indent\x12\x1a\n" +
	"\bcomplete\x18\x03 \x01(\bR\bcomplete\x12.\n" +
	"\bchildren\x18\x04 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\")\n" +
	"\rMathBlockNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"\xb7\x01\n" +
	"\tTableNode\x12*\n" +
	"\x06header\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\x06header\x12\x1c\n" +
	"\tdelimiter\x18\x02 \x03(\tR\tdelimiter\x12/\n" +
	"\x04rows\x18\x03 \x03(\v2\x1b.memos.api.v1.TableNode.RowR\x04rows\x1a/\n" +
	"\x03Row\x12(\n" +
	"\x05cells\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\x05cells\"R\n" +
	"\x13EmbeddedContentNode\x12#\n" +
	"\rresource_name\x18\x01 \x01(\tR\fresourceName\x12\x16\n" +
	"\x06params\x18\x02 \x01(\tR\x06params\"$\n" +
	"\bTextNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"R\n" +
	"\bBoldNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12.\n" +
	"\bchildren\x18\x02 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"T\n" +
	"\n" +
	"ItalicNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12.\n" +
	"\bchildren\x18\x02 \x03(\v2\x12.memos.api.v1.NodeR\bchildren\"B\n" +
	"\x0eBoldItalicNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"$\n" +
	"\bCodeNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"8\n" +
	"\tImageNode\x12\x19\n" +
	"\balt_text\x18\x01 \x01(\tR\aaltText\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"J\n" +
	"\bLinkNode\x12,\n" +
	"\acontent\x18\x01 \x03(\v2\x12.memos.api.v1.NodeR\acontent\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"@\n" +
	"\fAutoLinkNode\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x1e\n" +
	"\vis_raw_text\x18\x02 \x01(\bR\tisRawText\"#\n" +
	"\aTagNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"-\n" +
	"\x11StrikethroughNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"/\n" +
	"\x15EscapingCharacterNode\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\"$\n" +
	"\bMathNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\")\n" +
	"\rHighlightNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\")\n" +
	"\rSubscriptNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"+\n" +
	"\x0fSuperscriptNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"T\n" +
	"\x15ReferencedContentNode\x12#\n" +
	"\rresource_name\x18\x01 \x01(\tR\fresourceName\x12\x16\n" +
	"\x06params\x18\x02 \x01(\tR\x06params\"'\n" +
	"\vSpoilerNode\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"\xba\x01\n" +
	"\x0fHTMLElementNode\x12\x19\n" +
	"\btag_name\x18\x01 \x01(\tR\atagName\x12M\n" +
	"\n" +
	"attributes\x18\x02 \x03(\v2-.memos.api.v1.HTMLElementNode.AttributesEntryR\n" +
	"attributes\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*\x83\x04\n" +
	"\bNodeType\x12\x14\n" +
	"\x10NODE_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"LINE_BREAK\x10\x01\x12\r\n" +
	"\tPARAGRAPH\x10\x02\x12\x0e\n" +
	"\n" +
	"CODE_BLOCK\x10\x03\x12\v\n" +
	"\aHEADING\x10\x04\x12\x13\n" +
	"\x0fHORIZONTAL_RULE\x10\x05\x12\x0e\n" +
	"\n" +
	"BLOCKQUOTE\x10\x06\x12\b\n" +
	"\x04LIST\x10\a\x12\x15\n" +
	"\x11ORDERED_LIST_ITEM\x10\b\x12\x17\n" +
	"\x13UNORDERED_LIST_ITEM\x10\t\x12\x12\n" +
	"\x0eTASK_LIST_ITEM\x10\n" +
	"\x12\x0e\n" +
	"\n" +
	"MATH_BLOCK\x10\v\x12\t\n" +
	"\x05TABLE\x10\f\x12\x14\n" +
	"\x10EMBEDDED_CONTENT\x10\r\x12\b\n" +
	"\x04TEXT\x103\x12\b\n" +
	"\x04BOLD\x104\x12\n" +
	"\n" +
	"\x06ITALIC\x105\x12\x0f\n" +
	"\vBOLD_ITALIC\x106\x12\b\n" +
	"\x04CODE\x107\x12\t\n" +
	"\x05IMAGE\x108\x12\b\n" +
	"\x04LINK\x109\x12\r\n" +
	"\tAUTO_LINK\x10:\x12\a\n" +
	"\x03TAG\x10;\x12\x11\n" +
	"\rSTRIKETHROUGH\x10<\x12\x16\n" +
	"\x12ESCAPING_CHARACTER\x10=\x12\b\n" +
	"\x04MATH\x10>\x12\r\n" +
	"\tHIGHLIGHT\x10?\x12\r\n" +
	"\tSUBSCRIPT\x10@\x12\x0f\n" +
	"\vSUPERSCRIPT\x10A\x12\x16\n" +
	"\x12REFERENCED_CONTENT\x10B\x12\v\n" +
	"\aSPOILER\x10C\x12\x10\n" +
	"\fHTML_ELEMENT\x10D2\xc1\x04\n" +
	"\x0fMarkdownService\x12{\n" +
	"\rParseMarkdown\x12\".memos.api.v1.ParseMarkdownRequest\x1a#.memos.api.v1.ParseMarkdownResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/markdown:parse\x12\x92\x01\n" +
	"\x14RestoreMarkdownNodes\x12).memos.api.v1.RestoreMarkdownNodesRequest\x1a*.memos.api.v1.RestoreMarkdownNodesResponse\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/api/v1/markdown:restore\x12\x9a\x01\n" +
	"\x16StringifyMarkdownNodes\x12+.memos.api.v1.StringifyMarkdownNodesRequest\x1a,.memos.api.v1.StringifyMarkdownNodesResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/api/v1/markdown:stringify\x12\x7f\n" +
	"\x0fGetLinkMetadata\x12$.memos.api.v1.GetLinkMetadataRequest\x1a\x1a.memos.api.v1.LinkMetadata\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v1/markdown/links:getMetadataB\xac\x01\n" +
	"\x10com.memos.api.v1B\x14MarkdownServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_markdown_service_proto_rawDescOnce sync.Once
	file_api_v1_markdown_service_proto_rawDescData []byte
)

func file_api_v1_markdown_service_proto_rawDescGZIP() []byte {
	file_api_v1_markdown_service_proto_rawDescOnce.Do(func() {
		file_api_v1_markdown_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_markdown_service_proto_rawDesc), len(file_api_v1_markdown_service_proto_rawDesc)))
	})
	return file_api_v1_markdown_service_proto_rawDescData
}

var file_api_v1_markdown_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_v1_markdown_service_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_api_v1_markdown_service_proto_goTypes = []any{
	(NodeType)(0),                          // 0: memos.api.v1.NodeType
	(ListNode_Kind)(0),                     // 1: memos.api.v1.ListNode.Kind
	(*ParseMarkdownRequest)(nil),           // 2: memos.api.v1.ParseMarkdownRequest
	(*ParseMarkdownResponse)(nil),          // 3: memos.api.v1.ParseMarkdownResponse
	(*RestoreMarkdownNodesRequest)(nil),    // 4: memos.api.v1.RestoreMarkdownNodesRequest
	(*RestoreMarkdownNodesResponse)(nil),   // 5: memos.api.v1.RestoreMarkdownNodesResponse
	(*StringifyMarkdownNodesRequest)(nil),  // 6: memos.api.v1.StringifyMarkdownNodesRequest
	(*StringifyMarkdownNodesResponse)(nil), // 7: memos.api.v1.StringifyMarkdownNodesResponse
	(*GetLinkMetadataRequest)(nil),         // 8: memos.api.v1.GetLinkMetadataRequest
	(*LinkMetadata)(nil),                   // 9: memos.api.v1.LinkMetadata
	(*Node)(nil),                           // 10: memos.api.v1.Node
	(*LineBreakNode)(nil),                  // 11: memos.api.v1.LineBreakNode
	(*ParagraphNode)(nil),                  // 12: memos.api.v1.ParagraphNode
	(*CodeBlockNode)(nil),                  // 13: memos.api.v1.CodeBlockNode
	(*HeadingNode)(nil),                    // 14: memos.api.v1.HeadingNode
	(*HorizontalRuleNode)(nil),             // 15: memos.api.v1.HorizontalRuleNode
	(*BlockquoteNode)(nil),                 // 16: memos.api.v1.BlockquoteNode
	(*ListNode)(nil),                       // 17: memos.api.v1.ListNode
	(*OrderedListItemNode)(nil),            // 18: memos.api.v1.OrderedListItemNode
	(*UnorderedListItemNode)(nil),          // 19: memos.api.v1.UnorderedListItemNode
	(*TaskListItemNode)(nil),               // 20: memos.api.v1.TaskListItemNode
	(*MathBlockNode)(nil),                  // 21: memos.api.v1.MathBlockNode
	(*TableNode)(nil),                      // 22: memos.api.v1.TableNode
	(*EmbeddedContentNode)(nil),            // 23: memos.api.v1.EmbeddedContentNode
	(*TextNode)(nil),                       // 24: memos.api.v1.TextNode
	(*BoldNode)(nil),                       // 25: memos.api.v1.BoldNode
	(*ItalicNode)(nil),                     // 26: memos.api.v1.ItalicNode
	(*BoldItalicNode)(nil),                 // 27: memos.api.v1.BoldItalicNode
	(*CodeNode)(nil),                       // 28: memos.api.v1.CodeNode
	(*ImageNode)(nil),                      // 29: memos.api.v1.ImageNode
	(*LinkNode)(nil),                       // 30: memos.api.v1.LinkNode
	(*AutoLinkNode)(nil),                   // 31: memos.api.v1.AutoLinkNode
	(*TagNode)(nil),                        // 32: memos.api.v1.TagNode
	(*StrikethroughNode)(nil),              // 33: memos.api.v1.StrikethroughNode
	(*EscapingCharacterNode)(nil),          // 34: memos.api.v1.EscapingCharacterNode
	(*MathNode)(nil),                       // 35: memos.api.v1.MathNode
	(*HighlightNode)(nil),                  // 36: memos.api.v1.HighlightNode
	(*SubscriptNode)(nil),                  // 37: memos.api.v1.SubscriptNode
	(*SuperscriptNode)(nil),                // 38: memos.api.v1.SuperscriptNode
	(*ReferencedContentNode)(nil),          // 39: memos.api.v1.ReferencedContentNode
	(*SpoilerNode)(nil),                    // 40: memos.api.v1.SpoilerNode
	(*HTMLElementNode)(nil),                // 41: memos.api.v1.HTMLElementNode
	(*TableNode_Row)(nil),                  // 42: memos.api.v1.TableNode.Row
	nil,                                    // 43: memos.api.v1.HTMLElementNode.AttributesEntry
}
var file_api_v1_markdown_service_proto_depIdxs = []int32{
	10, // 0: memos.api.v1.ParseMarkdownResponse.nodes:type_name -> memos.api.v1.Node
	10, // 1: memos.api.v1.RestoreMarkdownNodesRequest.nodes:type_name -> memos.api.v1.Node
	10, // 2: memos.api.v1.StringifyMarkdownNodesRequest.nodes:type_name -> memos.api.v1.Node
	0,  // 3: memos.api.v1.Node.type:type_name -> memos.api.v1.NodeType
	11, // 4: memos.api.v1.Node.line_break_node:type_name -> memos.api.v1.LineBreakNode
	12, // 5: memos.api.v1.Node.paragraph_node:type_name -> memos.api.v1.ParagraphNode
	13, // 6: memos.api.v1.Node.code_block_node:type_name -> memos.api.v1.CodeBlockNode
	14, // 7: memos.api.v1.Node.heading_node:type_name -> memos.api.v1.HeadingNode
	15, // 8: memos.api.v1.Node.horizontal_rule_node:type_name -> memos.api.v1.HorizontalRuleNode
	16, // 9: memos.api.v1.Node.blockquote_node:type_name -> memos.api.v1.BlockquoteNode
	17, // 10: memos.api.v1.Node.list_node:type_name -> memos.api.v1.ListNode
	18, // 11: memos.api.v1.Node.ordered_list_item_node:type_name -> memos.api.v1.OrderedListItemNode
	19, // 12: memos.api.v1.Node.unordered_list_item_node:type_name -> memos.api.v1.UnorderedListItemNode
	20, // 13: memos.api.v1.Node.task_list_item_node:type_name -> memos.api.v1.TaskListItemNode
	21, // 14: memos.api.v1.Node.math_block_node:type_name -> memos.api.v1.MathBlockNode
	22, // 15: memos.api.v1.Node.table_node:type_name -> memos.api.v1.TableNode
	23, // 16: memos.api.v1.Node.embedded_content_node:type_name -> memos.api.v1.EmbeddedContentNode
	24, // 17: memos.api.v1.Node.text_node:type_name -> memos.api.v1.TextNode
	25, // 18: memos.api.v1.Node.bold_node:type_name -> memos.api.v1.BoldNode
	26, // 19: memos.api.v1.Node.italic_node:type_name -> memos.api.v1.ItalicNode
	27, // 20: memos.api.v1.Node.bold_italic_node:type_name -> memos.api.v1.BoldItalicNode
	28, // 21: memos.api.v1.Node.code_node:type_name -> memos.api.v1.CodeNode
	29, // 22: memos.api.v1.Node.image_node:type_name -> memos.api.v1.ImageNode
	30, // 23: memos.api.v1.Node.link_node:type_name -> memos.api.v1.LinkNode
	31, // 24: memos.api.v1.Node.auto_link_node:type_name -> memos.api.v1.AutoLinkNode
	32, // 25: memos.api.v1.Node.tag_node:type_name -> memos.api.v1.TagNode
	33, // 26: memos.api.v1.Node.strikethrough_node:type_name -> memos.api.v1.StrikethroughNode
	34, // 27: memos.api.v1.Node.escaping_character_node:type_name -> memos.api.v1.EscapingCharacterNode
	35, // 28: memos.api.v1.Node.math_node:type_name -> memos.api.v1.MathNode
	36, // 29: memos.api.v1.Node.highlight_node:type_name -> memos.api.v1.HighlightNode
	37, // 30: memos.api.v1.Node.subscript_node:type_name -> memos.api.v1.SubscriptNode
	38, // 31: memos.api.v1.Node.superscript_node:type_name -> memos.api.v1.SuperscriptNode
	39, // 32: memos.api.v1.Node.referenced_content_node:type_name -> memos.api.v1.ReferencedContentNode
	40, // 33: memos.api.v1.Node.spoiler_node:type_name -> memos.api.v1.SpoilerNode
	41, // 34: memos.api.v1.Node.html_element_node:type_name -> memos.api.v1.HTMLElementNode
	10, // 35: memos.api.v1.ParagraphNode.children:type_name -> memos.api.v1.Node
	10, // 36: memos.api.v1.HeadingNode.children:type_name -> memos.api.v1.Node
	10, // 37: memos.api.v1.BlockquoteNode.children:type_name -> memos.api.v1.Node
	1,  // 38: memos.api.v1.ListNode.kind:type_name -> memos.api.v1.ListNode.Kind
	10, // 39: memos.api.v1.ListNode.children:type_name -> memos.api.v1.Node
	10, // 40: memos.api.v1.OrderedListItemNode.children:type_name -> memos.api.v1.Node
	10, // 41: memos.api.v1.UnorderedListItemNode.children:type_name -> memos.api.v1.Node
	10, // 42: memos.api.v1.TaskListItemNode.children:type_name -> memos.api.v1.Node
	10, // 43: memos.api.v1.TableNode.header:type_name -> memos.api.v1.Node
	42, // 44: memos.api.v1.TableNode.rows:type_name -> memos.api.v1.TableNode.Row
	10, // 45: memos.api.v1.BoldNode.children:type_name -> memos.api.v1.Node
	10, // 46: memos.api.v1.ItalicNode.children:type_name -> memos.api.v1.Node
	10, // 47: memos.api.v1.LinkNode.content:type_name -> memos.api.v1.Node
	43, // 48: memos.api.v1.HTMLElementNode.attributes:type_name -> memos.api.v1.HTMLElementNode.AttributesEntry
	10, // 49: memos.api.v1.TableNode.Row.cells:type_name -> memos.api.v1.Node
	2,  // 50: memos.api.v1.MarkdownService.ParseMarkdown:input_type -> memos.api.v1.ParseMarkdownRequest
	4,  // 51: memos.api.v1.MarkdownService.RestoreMarkdownNodes:input_type -> memos.api.v1.RestoreMarkdownNodesRequest
	6,  // 52: memos.api.v1.MarkdownService.StringifyMarkdownNodes:input_type -> memos.api.v1.StringifyMarkdownNodesRequest
	8,  // 53: memos.api.v1.MarkdownService.GetLinkMetadata:input_type -> memos.api.v1.GetLinkMetadataRequest
	3,  // 54: memos.api.v1.MarkdownService.ParseMarkdown:output_type -> memos.api.v1.ParseMarkdownResponse
	5,  // 55: memos.api.v1.MarkdownService.RestoreMarkdownNodes:output_type -> memos.api.v1.RestoreMarkdownNodesResponse
	7,  // 56: memos.api.v1.MarkdownService.StringifyMarkdownNodes:output_type -> memos.api.v1.StringifyMarkdownNodesResponse
	9,  // 57: memos.api.v1.MarkdownService.GetLinkMetadata:output_type -> memos.api.v1.LinkMetadata
	54, // [54:58] is the sub-list for method output_type
	50, // [50:54] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_api_v1_markdown_service_proto_init() }
func file_api_v1_markdown_service_proto_init() {
	if File_api_v1_markdown_service_proto != nil {
		return
	}
	file_api_v1_markdown_service_proto_msgTypes[8].OneofWrappers = []any{
		(*Node_LineBreakNode)(nil),
		(*Node_ParagraphNode)(nil),
		(*Node_CodeBlockNode)(nil),
		(*Node_HeadingNode)(nil),
		(*Node_HorizontalRuleNode)(nil),
		(*Node_BlockquoteNode)(nil),
		(*Node_ListNode)(nil),
		(*Node_OrderedListItemNode)(nil),
		(*Node_UnorderedListItemNode)(nil),
		(*Node_TaskListItemNode)(nil),
		(*Node_MathBlockNode)(nil),
		(*Node_TableNode)(nil),
		(*Node_EmbeddedContentNode)(nil),
		(*Node_TextNode)(nil),
		(*Node_BoldNode)(nil),
		(*Node_ItalicNode)(nil),
		(*Node_BoldItalicNode)(nil),
		(*Node_CodeNode)(nil),
		(*Node_ImageNode)(nil),
		(*Node_LinkNode)(nil),
		(*Node_AutoLinkNode)(nil),
		(*Node_TagNode)(nil),
		(*Node_StrikethroughNode)(nil),
		(*Node_EscapingCharacterNode)(nil),
		(*Node_MathNode)(nil),
		(*Node_HighlightNode)(nil),
		(*Node_SubscriptNode)(nil),
		(*Node_SuperscriptNode)(nil),
		(*Node_ReferencedContentNode)(nil),
		(*Node_SpoilerNode)(nil),
		(*Node_HtmlElementNode)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_markdown_service_proto_rawDesc), len(file_api_v1_markdown_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_markdown_service_proto_goTypes,
		DependencyIndexes: file_api_v1_markdown_service_proto_depIdxs,
		EnumInfos:         file_api_v1_markdown_service_proto_enumTypes,
		MessageInfos:      file_api_v1_markdown_service_proto_msgTypes,
	}.Build()
	File_api_v1_markdown_service_proto = out.File
	file_api_v1_markdown_service_proto_goTypes = nil
	file_api_v1_markdown_service_proto_depIdxs = nil
}
