{"about": {"blogs": "博客", "description": "Memos 是一个隐私优先、轻量级的笔记解决方案，可以轻松捕捉和分享你的想法。", "documents": "文档", "github-repository": "GitHub 仓库", "official-website": "官网"}, "auth": {"create-your-account": "创建您的账户", "host-tip": "您正在注册为站点管理员。", "new-password": "新密码", "repeat-new-password": "重复新密码", "sign-in-tip": "已有账户？", "sign-up-tip": "还没有账户？"}, "common": {"about": "关于", "add": "增加", "admin": "管理", "archive": "归档", "archived": "已归档", "attachments": "附件", "avatar": "头像", "basic": "基础", "beta": "测试", "cancel": "取消", "change": "修改", "clear": "清除", "close": "关闭", "collapse": "收起", "confirm": "确定", "create": "创建", "created-at": "创建时间", "database": "数据库", "day": "天", "days": "天", "delete": "删除", "description": "说明", "edit": "编辑", "email": "邮箱", "expand": "展开", "explore": "发现", "file": "文件", "filter": "过滤器", "home": "主页", "image": "图片", "inbox": "通知", "input": "输入", "language": "语言", "last-updated-at": "最后更新时间", "layout": "布局", "learn-more": "了解更多", "link": "链接", "mark": "引用", "memo": "备忘录", "memos": "备忘录", "name": "名称", "new": "新建", "nickname": "昵称", "null": "空", "or": "或者", "password": "密码", "pin": "置顶", "pinned": "已置顶", "preview": "预览", "profile": "个人资料", "properties": "属性", "referenced-by": "被引用", "referencing": "引用", "relations": "关系图", "remember-me": "保持登录", "rename": "重命名", "reset": "重置", "resources": "资源库", "restore": "恢复", "role": "身份", "save": "保存", "search": "搜索", "select": "选择", "settings": "设置", "share": "分享", "shortcut-filter": "捷径过滤器", "shortcuts": "捷径", "sign-in": "登录", "sign-in-with": "使用 {{provider}} 登录", "sign-out": "退出登录", "sign-up": "注册", "statistics": "统计", "tags": "标签", "title": "标题", "tree-mode": "树模式", "type": "类型", "unpin": "取消置顶", "update": "更新", "upload": "上传", "user": "用户", "username": "用户名", "version": "版本", "visibility": "可见性", "yourself": "您自己"}, "days": {"fri": "五", "mon": "一", "sat": "六", "sun": "日", "thu": "四", "tue": "二", "wed": "三"}, "editor": {"add-your-comment-here": "请输入您的评论...", "any-thoughts": "此刻的想法...", "save": "保存", "no-changes-detected": "未检测到更改"}, "filters": {"has-code": "有代码", "has-link": "有链接", "has-task-list": "有待办"}, "inbox": {"memo-comment": "{{user}} 评论了您的“{{memo}}”。", "version-update": "新版本 {{version}} 现已推出！"}, "markdown": {"checkbox": "复选框", "code-block": "代码块", "content-syntax": "内容语法"}, "memo": {"archived-at": "归档于", "click-to-hide-nsfw-content": "点击隐藏 NSFW 内容", "click-to-show-nsfw-content": "点击显示 NSFW 内容", "code": "代码", "comment": {"self": "评论", "write-a-comment": "写评论"}, "copy-link": "复制链接", "count-memos-in-date": "{{date}} 有 {{count}} 条备忘录", "delete-confirm": "您确定要删除此条备忘录吗？（此操作不可逆）", "direction": "排序方式", "direction-asc": "正序", "direction-desc": "倒序", "display-time": "展示时间", "filters": "过滤器", "links": "链接", "load-more": "加载更多", "no-archived-memos": "没有已归档备忘录。", "no-memos": "无备忘录", "order-by": "排序", "remove-completed-task-list-items": "移除已办", "remove-completed-task-list-items-confirm": "您确定要移除所有完成的待办吗？（此操作不可逆）", "search-placeholder": "搜索备忘录", "show-less": "显示较少", "show-more": "查看更多", "to-do": "待办", "view-detail": "查看详情", "visibility": {"disabled": "已禁用公开备忘录", "private": "私有", "protected": "工作区", "public": "公开"}, "list": "列表模式", "masonry": "瀑布流模式"}, "message": {"archived-successfully": "归档成功", "change-memo-created-time": "更改备忘录创建时间", "copied": "已复制", "deleted-successfully": "成功删除！", "fill-all": "请填写所有栏目。", "maximum-upload-size-is": "允许的最大上传大小为 {{size}} MiB", "memo-not-found": "找不到备忘录", "new-password-not-match": "新密码不一致。", "no-data": "未找到任何数据。", "password-changed": "密码已修改", "password-not-match": "密码不一致。", "remove-completed-task-list-items-successfully": "移除成功！", "restored-successfully": "恢复成功", "succeed-copy-link": "复制链接到剪贴板成功。", "update-succeed": "更新成功", "user-not-found": "未找到该用户"}, "reference": {"add-references": "添加引用", "embedded-usage": "作为嵌入内容使用", "no-memos-found": "没有发现备忘录", "search-placeholder": "搜索内容"}, "resource": {"clear": "清除", "copy-link": "复制链接", "create-dialog": {"external-link": {"file-name": "文件名", "file-name-placeholder": "文件名", "link": "链接", "link-placeholder": "https://the.link.to/your/resource", "option": "外部链接", "type": "类型", "type-placeholder": "文件类型"}, "local-file": {"choose": "选择文件…", "option": "本地文件"}, "title": "创建资源", "upload-method": "上传方式"}, "delete-resource": "删除资源", "delete-selected-resources": "删除选中资源", "fetching-data": "正在获取数据…", "file-drag-drop-prompt": "将您的文件拖放到此处以上传文件", "linked-amount": "链接的备忘录数量", "no-files-selected": "没有文件被选中", "no-resources": "没有资源。", "no-unused-resources": "没有可删除的资源", "reset-link": "重置链接", "reset-link-prompt": "您确定要重置链接吗？这将导致当前使用的链接失效。（此操作不可逆）", "reset-resource-link": "重置资源链接", "unused-resources": "未使用到的资源"}, "router": {"back-to-top": "回到顶部", "go-to-home": "回到首页"}, "setting": {"access-token-section": {"create-dialog": {"create-access-token": "创建令牌", "created-at": "创建时间", "description": "描述", "duration-1m": "1 个月", "duration-8h": "8 小时", "duration-never": "从不", "expiration": "过期时间", "expires-at": "过期时间", "some-description": "请输入描述..."}, "description": "该账号下全部的访问令牌", "title": "访问令牌"}, "user-sessions-section": {"title": "用户会话", "description": "当前账号下全部的会话，你可以撤销除当前会话以外的所有会话。", "device": "设备", "last-active": "最后活跃时间", "expires": "过期时间", "current": "当前设备", "never": "从未", "session-revocation": "您确定要撤销会话 {{sessionId}} 吗？删除后您将需要在该设备上再次登录。", "session-revoked": "会话已成功撤销", "revoke-session": "撤销会话"}, "account-section": {"change-password": "修改密码", "email-note": "可选", "export-memos": "导出备忘录", "nickname-note": "显示在横幅中", "openapi-reset": "重置 OpenAPI 密钥（Key）", "openapi-sample-post": "您好 #memos 来自 {{url}}", "openapi-title": "OpenAPI", "reset-api": "重置 API", "title": "账号信息", "update-information": "更新个人信息", "username-note": "用于登录"}, "appearance-option": {"dark": "深色", "light": "浅色", "system": "跟随系统"}, "member": "成员", "member-list": "成员列表", "member-section": {"admin": "管理员", "archive-member": "归档成员", "archive-warning": "您确定要归档 {{username}} 吗？", "create-a-member": "创建成员", "delete-member": "删除成员", "delete-warning": "您确定要删除 {{username}} 吗？（此操作不可逆）", "user": "普通用户"}, "memo-related": "备忘录", "memo-related-settings": {"content-lenght-limit": "内容长度限制（字节）", "enable-blur-nsfw-content": "启用 NSFW 内容模糊处理（在下方添加 NSFW 标签）", "enable-link-preview": "启用链接预览", "enable-memo-comments": "启用备忘录评论", "enable-memo-location": "启用备忘录定位", "reactions": "表态", "title": "备忘录相关设置"}, "my-account": "我的账号", "preference": "偏好设置", "preference-section": {"default-memo-sort-option": "备忘录显示时间", "default-memo-visibility": "默认备忘录可见性", "theme": "主题"}, "sso": "单点登录", "sso-section": {"authorization-endpoint": "授权端点（Authorization Endpoint）", "client-id": "客户端ID（Client ID）", "client-secret": "客户端密钥（Client Secret）", "confirm-delete": "您确定要删除“{{name}}”单点登录配置吗？（此操作不可逆）", "create-sso": "创建单点登录", "custom": "自定义", "delete-sso": "确认删除", "disabled-password-login-warning": "密码登录已被禁用，删除身份提供程序时要格外小心", "display-name": "显示名称", "identifier": "标识符（Identifier）", "identifier-filter": "标识符过滤器（Identifier Filter）", "no-sso-found": "没有 SSO 配置", "redirect-url": "重定向链接", "scopes": "范围", "sso-created": "单点登录 {{name}} 已创建", "sso-list": "单点登录列表", "sso-updated": "单点登录 {{name}} 已更新", "template": "模板", "token-endpoint": "令牌端点（Token Endpoint）", "update-sso": "更新单点登录", "user-endpoint": "用户端点（User Endpoint）"}, "storage": "存储", "storage-section": {"accesskey": "访问密钥（Access key）", "accesskey-placeholder": "Access key / Access ID", "bucket": "储存桶（Bucket）", "bucket-placeholder": "储存桶名", "create-a-service": "新建服务", "create-storage": "创建存储", "current-storage": "当前对象存储", "delete-storage": "删除存储", "endpoint": "端点（Endpoint）", "filepath-template": "文件路径模板", "local-storage-path": "本地存储路径", "path": "存储路径", "path-description": "您可以使用本地存储中的相同动态变量，例如 {filename}", "path-placeholder": "自定义路径", "presign-placeholder": "预签名链接（可选）", "region": "地区", "region-placeholder": "区域名称", "s3-compatible-url": "S3 兼容链接", "secretkey": "Secret key", "secretkey-placeholder": "Secret key / Access Key", "storage-services": "存储服务列表", "type-database": "数据库", "type-local": "本地文件系统", "update-a-service": "更新服务", "update-local-path": "更新本地存储路径", "update-local-path-description": "本地存储路径是数据库文件的相对路径", "update-storage": "更新存储", "url-prefix": "链接前缀", "url-prefix-placeholder": "自定义链接前缀，可选", "url-suffix": "链接后缀", "url-suffix-placeholder": "自定义链接后缀，可选", "warning-text": "您确定要删除存储服务“{{name}}”吗？（此操作不可逆）"}, "system": "系统", "system-section": {"additional-script": "自定义脚本", "additional-script-placeholder": "自定义 JavaScript 代码", "additional-style": "自定义样式", "additional-style-placeholder": "自定义 CSS 代码", "allow-user-signup": "允许用户注册", "customize-server": {"appearance": "服务器外观", "description": "描述", "icon-url": "图标链接", "locale": "服务器语言环境", "title": "自定义服务器"}, "disable-markdown-shortcuts-in-editor": "禁用编辑器中的 Markdown 快捷键", "disable-password-login": "禁用密码登录", "disable-password-login-final-warning": "如果您知道自己在做什么，请输入 \"CONFIRM\"。", "disable-password-login-warning": "所有用户将无法使用密码登录。如果配置的身份提供程序失效，不在数据库中恢复此设置将无法登录。删除身份提供程序时也要格外小心", "disable-public-memos": "禁用公开备忘录", "display-with-updated-time": "根据最后修改时间顺序显示", "enable-auto-compact": "启用自动超长折叠显示", "enable-double-click-to-edit": "启用双击编辑", "enable-password-login": "启用密码登录", "enable-password-login-warning": "启用所有用户的密码登录。如果希望用户同时使用单点登录和密码登录，请开启密码登录", "max-upload-size": "最大上传大小 (MiB)", "max-upload-size-hint": "建议值为 32 MiB。", "removed-completed-task-list-items": "启用移除已办", "server-name": "服务器名称", "title": "一般设置"}, "webhook-section": {"create-dialog": {"an-easy-to-remember-name": "请输入一个容易记住的标题", "create-webhook": "创建 Webhook", "edit-webhook": "编辑 webhook", "payload-url": "请输入有效的 URL", "title": "标题"}, "no-webhooks-found": "没有 webhooks。"}, "workspace-section": {"disallow-change-nickname": "禁止修改用户昵称", "disallow-change-username": "禁止修改用户名", "disallow-password-auth": "禁用密码登录", "disallow-user-registration": "禁用用户注册", "monday": "周一", "saturday": "周六", "sunday": "周日", "week-start-day": "周开始日"}}, "tag": {"all-tags": "全部标签", "create-tag": "创建标签", "create-tags-guide": "您可以通过输入 “#标签” 创建标签。", "delete-confirm": "您确定要删除此标签吗?所有相关的备忘录将会被归档。", "delete-tag": "删除标签", "new-name": "新名称", "no-tag-found": "没找到此标签", "old-name": "旧名称", "rename-error-empty": "新名称不能为空或包含空格", "rename-error-repeat": "新名称不能与旧名称相同", "rename-success": "重命名成功", "rename-tag": "重命名", "rename-tip": "您的所有带有此标签的备忘录将被更新。"}}