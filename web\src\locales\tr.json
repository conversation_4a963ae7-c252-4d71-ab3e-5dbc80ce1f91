{"about": {"blogs": "Bloglar", "description": "<PERSON><PERSON><PERSON><PERSON>, ha<PERSON><PERSON> bir not alma servisi. <PERSON><PERSON> d<PERSON>ncelerinizi kolayca yakalayın ve paylaşın.", "documents": "<PERSON><PERSON><PERSON>", "github-repository": "GitHub Deposu", "official-website": "<PERSON><PERSON><PERSON>i"}, "auth": {"create-your-account": "Hesabınızı oluşturun", "host-tip": "Site Yöneticisi olarak kayıt oluyorsunuz.", "new-password": "<PERSON><PERSON>", "repeat-new-password": "<PERSON><PERSON> tekrar girin", "sign-in-tip": "Zaten hesabınız var mı?", "sign-up-tip": "<PERSON><PERSON><PERSON><PERSON> hesa<PERSON>ınız yok mu?"}, "common": {"about": "Hakkında", "add": "<PERSON><PERSON>", "admin": "Yönetici", "archive": "Arşiv", "archived": "Arşivlendi", "avatar": "<PERSON><PERSON>", "basic": "Temel", "beta": "Beta", "cancel": "İptal", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "collapse": "Dar<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "create": "Oluştur", "database": "Veritabanı", "day": "<PERSON><PERSON><PERSON>", "days": "<PERSON><PERSON><PERSON>", "delete": "Sil", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-posta", "expand": "Genişlet", "explore": "Keşfet", "file": "<PERSON><PERSON><PERSON>", "filter": "Filtrele", "home": "<PERSON>", "image": "G<PERSON><PERSON><PERSON>", "in": "İçinde", "inbox": "<PERSON><PERSON><PERSON>", "language": "Dil", "learn-more": "<PERSON><PERSON> fazla bilgi", "link": "Bağlantı", "mark": "İşaretle", "memo": "Not", "memos": "Notlar", "name": "İsim", "new": "<PERSON><PERSON>", "nickname": "Takma Ad", "null": "Boş", "or": "veya", "password": "Şifre", "pin": "<PERSON><PERSON><PERSON>", "pinned": "<PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "remember-me": "<PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON>ı<PERSON>", "reset": "Sıfırla", "resources": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON>", "role": "Rol", "save": "<PERSON><PERSON>", "search": "Ara", "select": "Seç", "settings": "<PERSON><PERSON><PERSON>", "share": "Paylaş", "shortcuts": "K<PERSON><PERSON>ollar", "sign-in": "<PERSON><PERSON><PERSON> yap", "sign-in-with": "{{provider}} ile giri<PERSON> yap", "sign-out": "Çıkış yap", "sign-up": "<PERSON><PERSON><PERSON> ol", "statistics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "title": "Başlık", "type": "<PERSON><PERSON><PERSON>", "unpin": "Sabitlemeyi kaldır", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "username": "Kullanıcı adı", "version": "S<PERSON>r<PERSON><PERSON>", "visibility": "Görünürlük", "yourself": "<PERSON><PERSON><PERSON>"}, "days": {"fri": "Cum", "mon": "Pzt", "sat": "Cmt", "sun": "Paz", "thu": "Per", "tue": "Sal", "wed": "Çar"}, "editor": {"add-your-comment-here": "<PERSON><PERSON><PERSON><PERSON><PERSON> buraya e<PERSON>...", "any-thoughts": "Düşünceleriniz...", "save": "<PERSON><PERSON>"}, "inbox": {"memo-comment": "{{user}} {{memo}} notunuza yorum yaptı.", "version-update": "<PERSON><PERSON> s<PERSON>üm {{version}} kullanıma hazır!"}, "markdown": {"checkbox": "<PERSON><PERSON> kut<PERSON>u", "code-block": "<PERSON><PERSON>", "content-syntax": "İçerik sözdizimi"}, "memo": {"archived-at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>hi", "code": "Kod", "comment": {"self": "<PERSON><PERSON><PERSON>", "write-a-comment": "<PERSON><PERSON> yaz"}, "copy-link": "Bağlantıyı Kopyala", "count-memos-in-date": "{{date}} tari<PERSON>de {{count}} {{memos}}", "delete-confirm": "Bu notu silmek istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ", "direction": "<PERSON><PERSON><PERSON>", "direction-asc": "<PERSON><PERSON>", "direction-desc": "<PERSON><PERSON><PERSON>", "display-time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filters": "<PERSON><PERSON><PERSON><PERSON>", "links": "Bağlantılar", "load-more": "<PERSON><PERSON> faz<PERSON>", "no-archived-memos": "A<PERSON>ş<PERSON><PERSON>miş not yok.", "no-memos": "Not yok.", "order-by": "Sıralama Ölçütü", "remove-completed-task-list-items": "Tamamlananları kaldır", "remove-completed-task-list-items-confirm": "Tamamlanan tüm görevleri kaldırmak istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ", "search-placeholder": "Notlarda ara", "show-less": "<PERSON><PERSON> a<PERSON> g<PERSON>", "show-more": "<PERSON><PERSON> faz<PERSON>", "to-do": "Yapılacaklar", "view-detail": "Detayları Görüntüle", "visibility": {"disabled": "Herkese açık notlar devre dışı", "private": "<PERSON><PERSON>", "protected": "Çalışma Alanı", "public": "Herkese Açık"}}, "message": {"archived-successfully": "Başarıyla arşivlendi", "change-memo-created-time": "Not oluşturma zamanını değiştir", "copied": "Kopyalandı", "deleted-successfully": "Başar<PERSON><PERSON>", "description-is-required": "Açıklama gerekli", "failed-to-embed-memo": "Not gömme işlemi başarısız", "fill-all": "Lütfen tüm alanları doldurun.", "fill-all-required-fields": "Lütfen tüm gerekli alanları doldurun", "maximum-upload-size-is": "<PERSON><PERSON><PERSON><PERSON> yükleme boyutu {{size}} MiB", "memo-not-found": "Not bulunamadı.", "new-password-not-match": "<PERSON><PERSON> eşleşmiyor.", "no-data": "<PERSON><PERSON> bulunamadı.", "password-changed": "<PERSON><PERSON><PERSON>", "password-not-match": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor.", "remove-completed-task-list-items-successfully": "Kaldırma işlemi başarılı", "restored-successfully": "Başarıyla geri <PERSON>", "succeed-copy-link": "Bağlantı başarıyla kopyalandı.", "update-succeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarılı", "user-not-found": "Kullanıcı bulunamadı"}, "reference": {"add-references": "<PERSON><PERSON><PERSON> ekle", "embedded-usage": "Gömülü İçerik Olarak Kullan", "no-memos-found": "Not bulunamadı", "search-placeholder": "İçerik ara"}, "resource": {"clear": "<PERSON><PERSON><PERSON>", "copy-link": "Bağlantıyı Kopyala", "create-dialog": {"external-link": {"file-name": "<PERSON><PERSON><PERSON> adı", "file-name-placeholder": "<PERSON><PERSON><PERSON> adı", "link": "Bağlantı", "link-placeholder": "https://kaynaginizin.baglantisi/dosyaniza", "option": "<PERSON><PERSON> bağlantı", "type": "<PERSON><PERSON><PERSON>", "type-placeholder": "<PERSON><PERSON><PERSON>"}, "local-file": {"choose": "<PERSON>ir dosya seç<PERSON>...", "option": "<PERSON><PERSON>"}, "title": "Kaynak Oluştur", "upload-method": "<PERSON><PERSON><PERSON><PERSON>"}, "delete-resource": "Kaynağı Sil", "delete-selected-resources": "Seçili Kaynakları Sil", "fetching-data": "<PERSON><PERSON><PERSON> alınıyor…", "file-drag-drop-prompt": "Dosya yüklemek için dosyanızı buraya sürükleyip bırakın", "linked-amount": "Bağlantı sayısı", "no-files-selected": "<PERSON><PERSON><PERSON>", "no-resources": "Kaynak yok.", "no-unused-resources": "Kullanılmayan kaynak yok", "reset-link": "Bağlantıyı Sıfırla", "reset-link-prompt": "Bağlantıyı sıfırlamak istediğinizden emin misiniz? Bu işlem mevcut tüm bağlantı kullanımlarını bozacaktır. BU İŞLEM GERİ ALINAMAZ", "reset-resource-link": "Kaynak Bağlantısını Sıfırla"}, "router": {"back-to-top": "Başa <PERSON>", "go-to-home": "<PERSON>"}, "setting": {"access-token-section": {"access-token-copied-to-clipboard": "E<PERSON>şim tokeni panoya kopyalandı", "access-token-deletion": "{{accessToken}} er<PERSON><PERSON><PERSON> tokenini silmek istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ.", "create-dialog": {"create-access-token": "<PERSON><PERSON><PERSON><PERSON>luş<PERSON>", "created-at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "duration-1m": "1 Ay", "duration-8h": "8 Saat", "duration-never": "<PERSON><PERSON>", "expiration": "<PERSON>", "expires-at": "<PERSON><PERSON>", "some-description": "Bir açıklama..."}, "description": "Hesabınız için tüm erişim <PERSON> listesi.", "title": "<PERSON><PERSON><PERSON><PERSON>", "token": "Token"}, "account-section": {"change-password": "<PERSON><PERSON><PERSON>", "email-note": "İsteğe bağlı", "export-memos": "Notları Dışa Aktar", "nickname-note": "Banner'da görünt<PERSON>lenir", "openapi-reset": "OpenAPI Anahtarını Sıfırla", "openapi-sample-post": "Merhaba #memos, {{url}} adresinden", "openapi-title": "OpenAPI", "reset-api": "API'yi <PERSON>", "title": "<PERSON><PERSON><PERSON>", "update-information": "Bilgileri Güncelle", "username-note": "<PERSON><PERSON><PERSON>ak iç<PERSON> k<PERSON>"}, "appearance-option": {"dark": "Her zaman koyu", "light": "Her zaman açık", "system": "<PERSON><PERSON><PERSON> et"}, "member": "Üye", "member-list": "<PERSON><PERSON> list<PERSON>", "member-section": {"archive-member": "Üyeyi arşivle", "archive-warning": "{{username}} kullanıcısını arşivlemek istediğinizden emin misiniz?", "create-a-member": "Üye oluştur", "delete-member": "Üyeyi Sil", "delete-warning": "{{username}} kullanıcısını silmek istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ"}, "memo-related": "Not", "memo-related-settings": {"content-lenght-limit": "İçerik uzunluğu sınırı (Bayt)", "enable-link-preview": "Bağlantı önizlemeyi etkinleştir", "enable-memo-comments": "Not yorumlarını etkinleştir", "enable-memo-location": "Not konumunu et<PERSON>r", "reactions": "<PERSON><PERSON><PERSON><PERSON>", "title": "Not ile ilgili a<PERSON>lar"}, "my-account": "He<PERSON>b<PERSON>m", "preference": "<PERSON><PERSON><PERSON><PERSON>", "preference-section": {"default-memo-sort-option": "Not g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zamanı", "default-memo-visibility": "Varsayılan not görünürlüğü", "theme": "<PERSON><PERSON>"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Yetkilendirme uç noktası", "client-id": "İstemci Kimliği", "client-secret": "İstemci Gizli Anahtarı", "confirm-delete": "\"{{name}}\" SSO yapılandırmasını silmek istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ", "create-sso": "SSO Oluştur", "custom": "<PERSON><PERSON>", "delete-sso": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>la", "disabled-password-login-warning": "Şifre ile giriş devre dışı bırakıldı, kimlik sağlayıcıları kaldırırken çok dikkatli olun", "display-name": "Görünen Ad", "identifier": "Tanımlayıcı", "identifier-filter": "Tanımlayıcı Filtresi", "no-sso-found": "SSO bulunamadı.", "redirect-url": "Yönlendirme URL'si", "scopes": "<PERSON><PERSON><PERSON><PERSON>", "single-sign-on": "Kimlik Doğrulama için Tek Oturum Açma (SSO) Yapılandırması", "sso-created": "SSO {{name}} oluşturuldu", "sso-list": "SSO Listesi", "sso-updated": "SSO {{name}} g<PERSON><PERSON><PERSON><PERSON>", "template": "Şablon", "token-endpoint": "Token uç noktası", "update-sso": "SS<PERSON><PERSON><PERSON><PERSON>", "user-endpoint": "Kullanıcı uç noktası"}, "storage": "<PERSON><PERSON><PERSON>", "storage-section": {"accesskey": "<PERSON><PERSON><PERSON><PERSON>", "accesskey-placeholder": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "bucket": "Bucket", "bucket-placeholder": "Bucket adı", "create-a-service": "<PERSON><PERSON>", "create-storage": "<PERSON><PERSON><PERSON>", "current-storage": "Mevcut nesne depolama", "delete-storage": "Depolamayı Sil", "endpoint": "Uç nokta", "local-storage-path": "<PERSON><PERSON> de<PERSON> yolu", "path": "<PERSON><PERSON><PERSON>", "path-description": "<PERSON><PERSON> de<PERSON> gibi {filename} gibi aynı dinamik değişkenleri kullanabilirsiniz", "path-placeholder": "özel/yol", "presign-placeholder": "Ön imzalı URL, isteğe bağlı", "region": "<PERSON><PERSON><PERSON>", "region-placeholder": "<PERSON><PERSON><PERSON> adı", "s3-compatible-url": "S3 Uyumlu URL", "secretkey": "<PERSON><PERSON><PERSON>", "secretkey-placeholder": "<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "storage-services": "<PERSON><PERSON><PERSON> ser<PERSON>i", "type-database": "Veritabanı", "type-local": "<PERSON><PERSON>", "update-a-service": "Ser<PERSON><PERSON> g<PERSON>", "update-local-path": "<PERSON><PERSON>", "update-local-path-description": "<PERSON><PERSON> yolu, veritabanı dosyanıza göre göreceli bir yoldur", "update-storage": "Depolamayı Güncelle", "url-prefix": "URL öneki", "url-prefix-placeholder": "Özel URL öneki, isteğe bağlı", "url-suffix": "URL soneki", "url-suffix-placeholder": "Özel URL soneki, iste<PERSON><PERSON> bağlı", "warning-text": "\"{{name}}\" depolama servisini silmek istediğinizden emin misiniz? BU İŞLEM GERİ ALINAMAZ"}, "system": "Sistem", "system-section": {"additional-script": "Ek script", "additional-script-placeholder": "Ek JavaScript kodu", "additional-style": "<PERSON><PERSON> stil", "additional-style-placeholder": "Ek CSS kodu", "allow-user-signup": "Kullanıcı kaydına izin ver", "customize-server": {"appearance": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "icon-url": "İkon URL'si", "locale": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "disable-markdown-shortcuts-in-editor": "Editörde Markdown kısayollarını devre dışı bırak", "disable-password-login": "Şifre ile girişi devre dışı bırak", "disable-password-login-final-warning": "Ne yaptığınızı biliyorsanız lütfen \"CONFIRM\" yazın.", "disable-password-login-warning": "<PERSON><PERSON>, tüm kullanıcılar için şifre ile girişi devre dışı bırakacaktır. Yapılandırılmış kimlik sağlayıcılarınız başarısız olursa, veritabanında bu ayarı geri almadan giriş yapmak mümkün olmayacaktır. Ayrıca bir kimlik sağlayıcıyı kaldırırken çok dikkatli olmanız gerekecektir", "disable-public-memos": "Herkese açık notları devre dışı bırak", "display-with-updated-time": "Güncellenme z<PERSON>ı<PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable-auto-compact": "Otomatik sıkıştırmayı etkinleştir", "enable-double-click-to-edit": "Düzenlemek için çift tıklamayı etkinleştir", "enable-password-login": "<PERSON><PERSON><PERSON> ile giri<PERSON>", "enable-password-login-warning": "<PERSON><PERSON>, tüm kullanıcılar için şifre ile girişi etkinleştirecektir. Yalnızca kullanıcıların hem SSO hem de şifre kullanarak giriş yapmasını istiyorsanız devam edin", "max-upload-size": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON> (MiB)", "max-upload-size-hint": "Önerilen <PERSON> 32 MiB'dir.", "removed-completed-task-list-items": "Tamamlanan görev listesi öğelerinin kaldırılmasını etkinleştir", "server-name": "<PERSON><PERSON><PERSON>"}, "version": "S<PERSON>r<PERSON><PERSON>", "webhook-section": {"create-dialog": {"an-easy-to-remember-name": "Hatırlaması kolay bir isim", "create-webhook": "Webhook oluştur", "edit-webhook": "Webhook düzenle", "payload-url": "Payload URL'si", "title": "Başlık", "url-example-post-receive": "https://example.com/postreceive"}, "no-webhooks-found": "Webhook bulunamadı.", "title": "Webhook'lar", "url": "URL"}, "workspace-section": {"disallow-change-nickname": "Takma ad değiştirmeyi yasakla", "disallow-change-username": "Kullanıcı adı değiştirmeyi yasakla", "disallow-password-auth": "Şifre ile kimlik doğrulamayı yasakla", "disallow-user-registration": "Kullanıcı kaydını yasakla", "monday": "<PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON><PERSON><PERSON>", "sunday": "Pazar", "week-start-day": "Haftanın ba<PERSON><PERSON><PERSON>ç günü"}}, "tag": {"all-tags": "<PERSON><PERSON><PERSON>", "create-tag": "Etiket Oluştur", "create-tags-guide": "`#etiket` yazarak etiket oluşturabilirsiniz.", "delete-confirm": "Bu etiketi silmek istediğinizden emin misiniz? İlgili tüm notlar arşivlenecektir.", "delete-tag": "Etiketi Sil", "no-tag-found": "Etiket bulunamadı"}}