// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/v1/shortcut_service.proto

package apiv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ShortcutService_ListShortcuts_FullMethodName  = "/memos.api.v1.ShortcutService/ListShortcuts"
	ShortcutService_GetShortcut_FullMethodName    = "/memos.api.v1.ShortcutService/GetShortcut"
	ShortcutService_CreateShortcut_FullMethodName = "/memos.api.v1.ShortcutService/CreateShortcut"
	ShortcutService_UpdateShortcut_FullMethodName = "/memos.api.v1.ShortcutService/UpdateShortcut"
	ShortcutService_DeleteShortcut_FullMethodName = "/memos.api.v1.ShortcutService/DeleteShortcut"
)

// ShortcutServiceClient is the client API for ShortcutService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ShortcutServiceClient interface {
	// ListShortcuts returns a list of shortcuts for a user.
	ListShortcuts(ctx context.Context, in *ListShortcutsRequest, opts ...grpc.CallOption) (*ListShortcutsResponse, error)
	// GetShortcut gets a shortcut by name.
	GetShortcut(ctx context.Context, in *GetShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error)
	// CreateShortcut creates a new shortcut for a user.
	CreateShortcut(ctx context.Context, in *CreateShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error)
	// UpdateShortcut updates a shortcut for a user.
	UpdateShortcut(ctx context.Context, in *UpdateShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error)
	// DeleteShortcut deletes a shortcut for a user.
	DeleteShortcut(ctx context.Context, in *DeleteShortcutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type shortcutServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewShortcutServiceClient(cc grpc.ClientConnInterface) ShortcutServiceClient {
	return &shortcutServiceClient{cc}
}

func (c *shortcutServiceClient) ListShortcuts(ctx context.Context, in *ListShortcutsRequest, opts ...grpc.CallOption) (*ListShortcutsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListShortcutsResponse)
	err := c.cc.Invoke(ctx, ShortcutService_ListShortcuts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shortcutServiceClient) GetShortcut(ctx context.Context, in *GetShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Shortcut)
	err := c.cc.Invoke(ctx, ShortcutService_GetShortcut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shortcutServiceClient) CreateShortcut(ctx context.Context, in *CreateShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Shortcut)
	err := c.cc.Invoke(ctx, ShortcutService_CreateShortcut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shortcutServiceClient) UpdateShortcut(ctx context.Context, in *UpdateShortcutRequest, opts ...grpc.CallOption) (*Shortcut, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Shortcut)
	err := c.cc.Invoke(ctx, ShortcutService_UpdateShortcut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shortcutServiceClient) DeleteShortcut(ctx context.Context, in *DeleteShortcutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ShortcutService_DeleteShortcut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShortcutServiceServer is the server API for ShortcutService service.
// All implementations must embed UnimplementedShortcutServiceServer
// for forward compatibility.
type ShortcutServiceServer interface {
	// ListShortcuts returns a list of shortcuts for a user.
	ListShortcuts(context.Context, *ListShortcutsRequest) (*ListShortcutsResponse, error)
	// GetShortcut gets a shortcut by name.
	GetShortcut(context.Context, *GetShortcutRequest) (*Shortcut, error)
	// CreateShortcut creates a new shortcut for a user.
	CreateShortcut(context.Context, *CreateShortcutRequest) (*Shortcut, error)
	// UpdateShortcut updates a shortcut for a user.
	UpdateShortcut(context.Context, *UpdateShortcutRequest) (*Shortcut, error)
	// DeleteShortcut deletes a shortcut for a user.
	DeleteShortcut(context.Context, *DeleteShortcutRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedShortcutServiceServer()
}

// UnimplementedShortcutServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShortcutServiceServer struct{}

func (UnimplementedShortcutServiceServer) ListShortcuts(context.Context, *ListShortcutsRequest) (*ListShortcutsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListShortcuts not implemented")
}
func (UnimplementedShortcutServiceServer) GetShortcut(context.Context, *GetShortcutRequest) (*Shortcut, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShortcut not implemented")
}
func (UnimplementedShortcutServiceServer) CreateShortcut(context.Context, *CreateShortcutRequest) (*Shortcut, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateShortcut not implemented")
}
func (UnimplementedShortcutServiceServer) UpdateShortcut(context.Context, *UpdateShortcutRequest) (*Shortcut, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateShortcut not implemented")
}
func (UnimplementedShortcutServiceServer) DeleteShortcut(context.Context, *DeleteShortcutRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteShortcut not implemented")
}
func (UnimplementedShortcutServiceServer) mustEmbedUnimplementedShortcutServiceServer() {}
func (UnimplementedShortcutServiceServer) testEmbeddedByValue()                         {}

// UnsafeShortcutServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShortcutServiceServer will
// result in compilation errors.
type UnsafeShortcutServiceServer interface {
	mustEmbedUnimplementedShortcutServiceServer()
}

func RegisterShortcutServiceServer(s grpc.ServiceRegistrar, srv ShortcutServiceServer) {
	// If the following call pancis, it indicates UnimplementedShortcutServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ShortcutService_ServiceDesc, srv)
}

func _ShortcutService_ListShortcuts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListShortcutsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShortcutServiceServer).ListShortcuts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShortcutService_ListShortcuts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShortcutServiceServer).ListShortcuts(ctx, req.(*ListShortcutsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShortcutService_GetShortcut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShortcutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShortcutServiceServer).GetShortcut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShortcutService_GetShortcut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShortcutServiceServer).GetShortcut(ctx, req.(*GetShortcutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShortcutService_CreateShortcut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateShortcutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShortcutServiceServer).CreateShortcut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShortcutService_CreateShortcut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShortcutServiceServer).CreateShortcut(ctx, req.(*CreateShortcutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShortcutService_UpdateShortcut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateShortcutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShortcutServiceServer).UpdateShortcut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShortcutService_UpdateShortcut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShortcutServiceServer).UpdateShortcut(ctx, req.(*UpdateShortcutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShortcutService_DeleteShortcut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteShortcutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShortcutServiceServer).DeleteShortcut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShortcutService_DeleteShortcut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShortcutServiceServer).DeleteShortcut(ctx, req.(*DeleteShortcutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShortcutService_ServiceDesc is the grpc.ServiceDesc for ShortcutService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShortcutService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "memos.api.v1.ShortcutService",
	HandlerType: (*ShortcutServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListShortcuts",
			Handler:    _ShortcutService_ListShortcuts_Handler,
		},
		{
			MethodName: "GetShortcut",
			Handler:    _ShortcutService_GetShortcut_Handler,
		},
		{
			MethodName: "CreateShortcut",
			Handler:    _ShortcutService_CreateShortcut_Handler,
		},
		{
			MethodName: "UpdateShortcut",
			Handler:    _ShortcutService_UpdateShortcut_Handler,
		},
		{
			MethodName: "DeleteShortcut",
			Handler:    _ShortcutService_DeleteShortcut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/v1/shortcut_service.proto",
}
