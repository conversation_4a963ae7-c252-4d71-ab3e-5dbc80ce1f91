// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/idp.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IdentityProvider_Type int32

const (
	IdentityProvider_TYPE_UNSPECIFIED IdentityProvider_Type = 0
	IdentityProvider_OAUTH2           IdentityProvider_Type = 1
)

// Enum value maps for IdentityProvider_Type.
var (
	IdentityProvider_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "OAUTH2",
	}
	IdentityProvider_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"OAUTH2":           1,
	}
)

func (x IdentityProvider_Type) Enum() *IdentityProvider_Type {
	p := new(IdentityProvider_Type)
	*p = x
	return p
}

func (x IdentityProvider_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityProvider_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_store_idp_proto_enumTypes[0].Descriptor()
}

func (IdentityProvider_Type) Type() protoreflect.EnumType {
	return &file_store_idp_proto_enumTypes[0]
}

func (x IdentityProvider_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityProvider_Type.Descriptor instead.
func (IdentityProvider_Type) EnumDescriptor() ([]byte, []int) {
	return file_store_idp_proto_rawDescGZIP(), []int{0, 0}
}

type IdentityProvider struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	Id               int32                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type             IdentityProvider_Type   `protobuf:"varint,3,opt,name=type,proto3,enum=memos.store.IdentityProvider_Type" json:"type,omitempty"`
	IdentifierFilter string                  `protobuf:"bytes,4,opt,name=identifier_filter,json=identifierFilter,proto3" json:"identifier_filter,omitempty"`
	Config           *IdentityProviderConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IdentityProvider) Reset() {
	*x = IdentityProvider{}
	mi := &file_store_idp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IdentityProvider) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityProvider) ProtoMessage() {}

func (x *IdentityProvider) ProtoReflect() protoreflect.Message {
	mi := &file_store_idp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityProvider.ProtoReflect.Descriptor instead.
func (*IdentityProvider) Descriptor() ([]byte, []int) {
	return file_store_idp_proto_rawDescGZIP(), []int{0}
}

func (x *IdentityProvider) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *IdentityProvider) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdentityProvider) GetType() IdentityProvider_Type {
	if x != nil {
		return x.Type
	}
	return IdentityProvider_TYPE_UNSPECIFIED
}

func (x *IdentityProvider) GetIdentifierFilter() string {
	if x != nil {
		return x.IdentifierFilter
	}
	return ""
}

func (x *IdentityProvider) GetConfig() *IdentityProviderConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type IdentityProviderConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Config:
	//
	//	*IdentityProviderConfig_Oauth2Config
	Config        isIdentityProviderConfig_Config `protobuf_oneof:"config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IdentityProviderConfig) Reset() {
	*x = IdentityProviderConfig{}
	mi := &file_store_idp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IdentityProviderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityProviderConfig) ProtoMessage() {}

func (x *IdentityProviderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_store_idp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityProviderConfig.ProtoReflect.Descriptor instead.
func (*IdentityProviderConfig) Descriptor() ([]byte, []int) {
	return file_store_idp_proto_rawDescGZIP(), []int{1}
}

func (x *IdentityProviderConfig) GetConfig() isIdentityProviderConfig_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *IdentityProviderConfig) GetOauth2Config() *OAuth2Config {
	if x != nil {
		if x, ok := x.Config.(*IdentityProviderConfig_Oauth2Config); ok {
			return x.Oauth2Config
		}
	}
	return nil
}

type isIdentityProviderConfig_Config interface {
	isIdentityProviderConfig_Config()
}

type IdentityProviderConfig_Oauth2Config struct {
	Oauth2Config *OAuth2Config `protobuf:"bytes,1,opt,name=oauth2_config,json=oauth2Config,proto3,oneof"`
}

func (*IdentityProviderConfig_Oauth2Config) isIdentityProviderConfig_Config() {}

type FieldMapping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Identifier    string                 `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	DisplayName   string                 `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,4,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldMapping) Reset() {
	*x = FieldMapping{}
	mi := &file_store_idp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldMapping) ProtoMessage() {}

func (x *FieldMapping) ProtoReflect() protoreflect.Message {
	mi := &file_store_idp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldMapping.ProtoReflect.Descriptor instead.
func (*FieldMapping) Descriptor() ([]byte, []int) {
	return file_store_idp_proto_rawDescGZIP(), []int{2}
}

func (x *FieldMapping) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *FieldMapping) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *FieldMapping) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *FieldMapping) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type OAuth2Config struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      string                 `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	AuthUrl       string                 `protobuf:"bytes,3,opt,name=auth_url,json=authUrl,proto3" json:"auth_url,omitempty"`
	TokenUrl      string                 `protobuf:"bytes,4,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	UserInfoUrl   string                 `protobuf:"bytes,5,opt,name=user_info_url,json=userInfoUrl,proto3" json:"user_info_url,omitempty"`
	Scopes        []string               `protobuf:"bytes,6,rep,name=scopes,proto3" json:"scopes,omitempty"`
	FieldMapping  *FieldMapping          `protobuf:"bytes,7,opt,name=field_mapping,json=fieldMapping,proto3" json:"field_mapping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OAuth2Config) Reset() {
	*x = OAuth2Config{}
	mi := &file_store_idp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OAuth2Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuth2Config) ProtoMessage() {}

func (x *OAuth2Config) ProtoReflect() protoreflect.Message {
	mi := &file_store_idp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuth2Config.ProtoReflect.Descriptor instead.
func (*OAuth2Config) Descriptor() ([]byte, []int) {
	return file_store_idp_proto_rawDescGZIP(), []int{3}
}

func (x *OAuth2Config) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OAuth2Config) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *OAuth2Config) GetAuthUrl() string {
	if x != nil {
		return x.AuthUrl
	}
	return ""
}

func (x *OAuth2Config) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *OAuth2Config) GetUserInfoUrl() string {
	if x != nil {
		return x.UserInfoUrl
	}
	return ""
}

func (x *OAuth2Config) GetScopes() []string {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *OAuth2Config) GetFieldMapping() *FieldMapping {
	if x != nil {
		return x.FieldMapping
	}
	return nil
}

var File_store_idp_proto protoreflect.FileDescriptor

const file_store_idp_proto_rawDesc = "" +
	"\n" +
	"\x0fstore/idp.proto\x12\vmemos.store\"\x82\x02\n" +
	"\x10IdentityProvider\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x126\n" +
	"\x04type\x18\x03 \x01(\x0e2\".memos.store.IdentityProvider.TypeR\x04type\x12+\n" +
	"\x11identifier_filter\x18\x04 \x01(\tR\x10identifierFilter\x12;\n" +
	"\x06config\x18\x05 \x01(\v2#.memos.store.IdentityProviderConfigR\x06config\"(\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06OAUTH2\x10\x01\"d\n" +
	"\x16IdentityProviderConfig\x12@\n" +
	"\roauth2_config\x18\x01 \x01(\v2\x19.memos.store.OAuth2ConfigH\x00R\foauth2ConfigB\b\n" +
	"\x06config\"\x86\x01\n" +
	"\fFieldMapping\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12!\n" +
	"\fdisplay_name\x18\x02 \x01(\tR\vdisplayName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x04 \x01(\tR\tavatarUrl\"\x84\x02\n" +
	"\fOAuth2Config\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x02 \x01(\tR\fclientSecret\x12\x19\n" +
	"\bauth_url\x18\x03 \x01(\tR\aauthUrl\x12\x1b\n" +
	"\ttoken_url\x18\x04 \x01(\tR\btokenUrl\x12\"\n" +
	"\ruser_info_url\x18\x05 \x01(\tR\vuserInfoUrl\x12\x16\n" +
	"\x06scopes\x18\x06 \x03(\tR\x06scopes\x12>\n" +
	"\rfield_mapping\x18\a \x01(\v2\x19.memos.store.FieldMappingR\ffieldMappingB\x93\x01\n" +
	"\x0fcom.memos.storeB\bIdpProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_idp_proto_rawDescOnce sync.Once
	file_store_idp_proto_rawDescData []byte
)

func file_store_idp_proto_rawDescGZIP() []byte {
	file_store_idp_proto_rawDescOnce.Do(func() {
		file_store_idp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_idp_proto_rawDesc), len(file_store_idp_proto_rawDesc)))
	})
	return file_store_idp_proto_rawDescData
}

var file_store_idp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_store_idp_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_store_idp_proto_goTypes = []any{
	(IdentityProvider_Type)(0),     // 0: memos.store.IdentityProvider.Type
	(*IdentityProvider)(nil),       // 1: memos.store.IdentityProvider
	(*IdentityProviderConfig)(nil), // 2: memos.store.IdentityProviderConfig
	(*FieldMapping)(nil),           // 3: memos.store.FieldMapping
	(*OAuth2Config)(nil),           // 4: memos.store.OAuth2Config
}
var file_store_idp_proto_depIdxs = []int32{
	0, // 0: memos.store.IdentityProvider.type:type_name -> memos.store.IdentityProvider.Type
	2, // 1: memos.store.IdentityProvider.config:type_name -> memos.store.IdentityProviderConfig
	4, // 2: memos.store.IdentityProviderConfig.oauth2_config:type_name -> memos.store.OAuth2Config
	3, // 3: memos.store.OAuth2Config.field_mapping:type_name -> memos.store.FieldMapping
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_store_idp_proto_init() }
func file_store_idp_proto_init() {
	if File_store_idp_proto != nil {
		return
	}
	file_store_idp_proto_msgTypes[1].OneofWrappers = []any{
		(*IdentityProviderConfig_Oauth2Config)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_idp_proto_rawDesc), len(file_store_idp_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_idp_proto_goTypes,
		DependencyIndexes: file_store_idp_proto_depIdxs,
		EnumInfos:         file_store_idp_proto_enumTypes,
		MessageInfos:      file_store_idp_proto_msgTypes,
	}.Build()
	File_store_idp_proto = out.File
	file_store_idp_proto_goTypes = nil
	file_store_idp_proto_depIdxs = nil
}
