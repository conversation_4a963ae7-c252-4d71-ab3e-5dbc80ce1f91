// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/activity_service.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "../../google/protobuf/timestamp";

export const protobufPackage = "memos.api.v1";

export interface Activity {
  /**
   * The name of the activity.
   * Format: activities/{id}
   */
  name: string;
  /**
   * The name of the creator.
   * Format: users/{user}
   */
  creator: string;
  /** The type of the activity. */
  type: Activity_Type;
  /** The level of the activity. */
  level: Activity_Level;
  /** The create time of the activity. */
  createTime?:
    | Date
    | undefined;
  /** The payload of the activity. */
  payload?: ActivityPayload | undefined;
}

/** Activity types. */
export enum Activity_Type {
  /** TYPE_UNSPECIFIED - Unspecified type. */
  TYPE_UNSPECIFIED = "TYPE_UNSPECIFIED",
  /** MEMO_COMMENT - Memo comment activity. */
  MEMO_COMMENT = "MEMO_COMMENT",
  /** VERSION_UPDATE - Version update activity. */
  VERSION_UPDATE = "VERSION_UPDATE",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function activity_TypeFromJSON(object: any): Activity_Type {
  switch (object) {
    case 0:
    case "TYPE_UNSPECIFIED":
      return Activity_Type.TYPE_UNSPECIFIED;
    case 1:
    case "MEMO_COMMENT":
      return Activity_Type.MEMO_COMMENT;
    case 2:
    case "VERSION_UPDATE":
      return Activity_Type.VERSION_UPDATE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Activity_Type.UNRECOGNIZED;
  }
}

export function activity_TypeToNumber(object: Activity_Type): number {
  switch (object) {
    case Activity_Type.TYPE_UNSPECIFIED:
      return 0;
    case Activity_Type.MEMO_COMMENT:
      return 1;
    case Activity_Type.VERSION_UPDATE:
      return 2;
    case Activity_Type.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Activity levels. */
export enum Activity_Level {
  /** LEVEL_UNSPECIFIED - Unspecified level. */
  LEVEL_UNSPECIFIED = "LEVEL_UNSPECIFIED",
  /** INFO - Info level. */
  INFO = "INFO",
  /** WARN - Warn level. */
  WARN = "WARN",
  /** ERROR - Error level. */
  ERROR = "ERROR",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function activity_LevelFromJSON(object: any): Activity_Level {
  switch (object) {
    case 0:
    case "LEVEL_UNSPECIFIED":
      return Activity_Level.LEVEL_UNSPECIFIED;
    case 1:
    case "INFO":
      return Activity_Level.INFO;
    case 2:
    case "WARN":
      return Activity_Level.WARN;
    case 3:
    case "ERROR":
      return Activity_Level.ERROR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Activity_Level.UNRECOGNIZED;
  }
}

export function activity_LevelToNumber(object: Activity_Level): number {
  switch (object) {
    case Activity_Level.LEVEL_UNSPECIFIED:
      return 0;
    case Activity_Level.INFO:
      return 1;
    case Activity_Level.WARN:
      return 2;
    case Activity_Level.ERROR:
      return 3;
    case Activity_Level.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface ActivityPayload {
  /** Memo comment activity payload. */
  memoComment?: ActivityMemoCommentPayload | undefined;
}

/** ActivityMemoCommentPayload represents the payload of a memo comment activity. */
export interface ActivityMemoCommentPayload {
  /**
   * The memo name of comment.
   * Format: memos/{memo}
   */
  memo: string;
  /**
   * The name of related memo.
   * Format: memos/{memo}
   */
  relatedMemo: string;
}

export interface ListActivitiesRequest {
  /**
   * The maximum number of activities to return.
   * The service may return fewer than this value.
   * If unspecified, at most 100 activities will be returned.
   * The maximum value is 1000; values above 1000 will be coerced to 1000.
   */
  pageSize: number;
  /**
   * A page token, received from a previous `ListActivities` call.
   * Provide this to retrieve the subsequent page.
   */
  pageToken: string;
}

export interface ListActivitiesResponse {
  /** The activities. */
  activities: Activity[];
  /**
   * A token to retrieve the next page of results.
   * Pass this value in the page_token field in the subsequent call to `ListActivities`
   * method to retrieve the next page of results.
   */
  nextPageToken: string;
}

export interface GetActivityRequest {
  /**
   * The name of the activity.
   * Format: activities/{id}, id is the system generated auto-incremented id.
   */
  name: string;
}

function createBaseActivity(): Activity {
  return {
    name: "",
    creator: "",
    type: Activity_Type.TYPE_UNSPECIFIED,
    level: Activity_Level.LEVEL_UNSPECIFIED,
    createTime: undefined,
    payload: undefined,
  };
}

export const Activity: MessageFns<Activity> = {
  encode(message: Activity, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.creator !== "") {
      writer.uint32(18).string(message.creator);
    }
    if (message.type !== Activity_Type.TYPE_UNSPECIFIED) {
      writer.uint32(24).int32(activity_TypeToNumber(message.type));
    }
    if (message.level !== Activity_Level.LEVEL_UNSPECIFIED) {
      writer.uint32(32).int32(activity_LevelToNumber(message.level));
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(42).fork()).join();
    }
    if (message.payload !== undefined) {
      ActivityPayload.encode(message.payload, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Activity {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = activity_TypeFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.level = activity_LevelFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.payload = ActivityPayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Activity>): Activity {
    return Activity.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Activity>): Activity {
    const message = createBaseActivity();
    message.name = object.name ?? "";
    message.creator = object.creator ?? "";
    message.type = object.type ?? Activity_Type.TYPE_UNSPECIFIED;
    message.level = object.level ?? Activity_Level.LEVEL_UNSPECIFIED;
    message.createTime = object.createTime ?? undefined;
    message.payload = (object.payload !== undefined && object.payload !== null)
      ? ActivityPayload.fromPartial(object.payload)
      : undefined;
    return message;
  },
};

function createBaseActivityPayload(): ActivityPayload {
  return { memoComment: undefined };
}

export const ActivityPayload: MessageFns<ActivityPayload> = {
  encode(message: ActivityPayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memoComment !== undefined) {
      ActivityMemoCommentPayload.encode(message.memoComment, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityPayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memoComment = ActivityMemoCommentPayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ActivityPayload>): ActivityPayload {
    return ActivityPayload.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ActivityPayload>): ActivityPayload {
    const message = createBaseActivityPayload();
    message.memoComment = (object.memoComment !== undefined && object.memoComment !== null)
      ? ActivityMemoCommentPayload.fromPartial(object.memoComment)
      : undefined;
    return message;
  },
};

function createBaseActivityMemoCommentPayload(): ActivityMemoCommentPayload {
  return { memo: "", relatedMemo: "" };
}

export const ActivityMemoCommentPayload: MessageFns<ActivityMemoCommentPayload> = {
  encode(message: ActivityMemoCommentPayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== "") {
      writer.uint32(10).string(message.memo);
    }
    if (message.relatedMemo !== "") {
      writer.uint32(18).string(message.relatedMemo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityMemoCommentPayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityMemoCommentPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relatedMemo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ActivityMemoCommentPayload>): ActivityMemoCommentPayload {
    return ActivityMemoCommentPayload.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ActivityMemoCommentPayload>): ActivityMemoCommentPayload {
    const message = createBaseActivityMemoCommentPayload();
    message.memo = object.memo ?? "";
    message.relatedMemo = object.relatedMemo ?? "";
    return message;
  },
};

function createBaseListActivitiesRequest(): ListActivitiesRequest {
  return { pageSize: 0, pageToken: "" };
}

export const ListActivitiesRequest: MessageFns<ListActivitiesRequest> = {
  encode(message: ListActivitiesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(18).string(message.pageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListActivitiesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListActivitiesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListActivitiesRequest>): ListActivitiesRequest {
    return ListActivitiesRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListActivitiesRequest>): ListActivitiesRequest {
    const message = createBaseListActivitiesRequest();
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    return message;
  },
};

function createBaseListActivitiesResponse(): ListActivitiesResponse {
  return { activities: [], nextPageToken: "" };
}

export const ListActivitiesResponse: MessageFns<ListActivitiesResponse> = {
  encode(message: ListActivitiesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.activities) {
      Activity.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListActivitiesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListActivitiesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.activities.push(Activity.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListActivitiesResponse>): ListActivitiesResponse {
    return ListActivitiesResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListActivitiesResponse>): ListActivitiesResponse {
    const message = createBaseListActivitiesResponse();
    message.activities = object.activities?.map((e) => Activity.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    return message;
  },
};

function createBaseGetActivityRequest(): GetActivityRequest {
  return { name: "" };
}

export const GetActivityRequest: MessageFns<GetActivityRequest> = {
  encode(message: GetActivityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetActivityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetActivityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetActivityRequest>): GetActivityRequest {
    return GetActivityRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetActivityRequest>): GetActivityRequest {
    const message = createBaseGetActivityRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type ActivityServiceDefinition = typeof ActivityServiceDefinition;
export const ActivityServiceDefinition = {
  name: "ActivityService",
  fullName: "memos.api.v1.ActivityService",
  methods: {
    /** ListActivities returns a list of activities. */
    listActivities: {
      name: "ListActivities",
      requestType: ListActivitiesRequest,
      requestStream: false,
      responseType: ListActivitiesResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              20,
              18,
              18,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              99,
              116,
              105,
              118,
              105,
              116,
              105,
              101,
              115,
            ]),
          ],
        },
      },
    },
    /** GetActivity returns the activity with the given id. */
    getActivity: {
      name: "GetActivity",
      requestType: GetActivityRequest,
      requestStream: false,
      responseType: Activity,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              97,
              99,
              116,
              105,
              118,
              105,
              116,
              105,
              101,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
