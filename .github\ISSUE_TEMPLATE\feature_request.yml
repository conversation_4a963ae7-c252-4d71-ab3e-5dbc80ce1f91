name: Feature Request
description: If you have a suggestion for a new feature
labels: [enhancement]
body:
  - type: markdown
    attributes:
      value: |
        Before submitting a feature request, please check if the issue is already present in the issues. If it is, please add a reaction to the issue. If it isn't, please fill out the form below.
  - type: textarea
    attributes:
      label: Describe the solution you'd like
      description: |
        A clear and concise description of what you want to happen.
      placeholder: |
        It would be great if [...]
    validations:
      required: true
  - type: dropdown
    attributes:
      label: Type of feature
      description: What type of feature is this?
      options:
        - User Interface (UI)
        - User Experience (UX)
        - API
        - Documentation
        - Integrations
        - Other
      default: 0
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional context
      description: |
        What are you trying to do? Why is this important to you?
