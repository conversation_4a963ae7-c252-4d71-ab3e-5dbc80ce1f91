// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/activity_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Activity types.
type Activity_Type int32

const (
	// Unspecified type.
	Activity_TYPE_UNSPECIFIED Activity_Type = 0
	// Memo comment activity.
	Activity_MEMO_COMMENT Activity_Type = 1
	// Version update activity.
	Activity_VERSION_UPDATE Activity_Type = 2
)

// Enum value maps for Activity_Type.
var (
	Activity_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MEMO_COMMENT",
		2: "VERSION_UPDATE",
	}
	Activity_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MEMO_COMMENT":     1,
		"VERSION_UPDATE":   2,
	}
)

func (x Activity_Type) Enum() *Activity_Type {
	p := new(Activity_Type)
	*p = x
	return p
}

func (x Activity_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Activity_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_activity_service_proto_enumTypes[0].Descriptor()
}

func (Activity_Type) Type() protoreflect.EnumType {
	return &file_api_v1_activity_service_proto_enumTypes[0]
}

func (x Activity_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Activity_Type.Descriptor instead.
func (Activity_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{0, 0}
}

// Activity levels.
type Activity_Level int32

const (
	// Unspecified level.
	Activity_LEVEL_UNSPECIFIED Activity_Level = 0
	// Info level.
	Activity_INFO Activity_Level = 1
	// Warn level.
	Activity_WARN Activity_Level = 2
	// Error level.
	Activity_ERROR Activity_Level = 3
)

// Enum value maps for Activity_Level.
var (
	Activity_Level_name = map[int32]string{
		0: "LEVEL_UNSPECIFIED",
		1: "INFO",
		2: "WARN",
		3: "ERROR",
	}
	Activity_Level_value = map[string]int32{
		"LEVEL_UNSPECIFIED": 0,
		"INFO":              1,
		"WARN":              2,
		"ERROR":             3,
	}
)

func (x Activity_Level) Enum() *Activity_Level {
	p := new(Activity_Level)
	*p = x
	return p
}

func (x Activity_Level) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Activity_Level) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_activity_service_proto_enumTypes[1].Descriptor()
}

func (Activity_Level) Type() protoreflect.EnumType {
	return &file_api_v1_activity_service_proto_enumTypes[1]
}

func (x Activity_Level) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Activity_Level.Descriptor instead.
func (Activity_Level) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{0, 1}
}

type Activity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the activity.
	// Format: activities/{id}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The name of the creator.
	// Format: users/{user}
	Creator string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	// The type of the activity.
	Type Activity_Type `protobuf:"varint,3,opt,name=type,proto3,enum=memos.api.v1.Activity_Type" json:"type,omitempty"`
	// The level of the activity.
	Level Activity_Level `protobuf:"varint,4,opt,name=level,proto3,enum=memos.api.v1.Activity_Level" json:"level,omitempty"`
	// The create time of the activity.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The payload of the activity.
	Payload       *ActivityPayload `protobuf:"bytes,6,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Activity) Reset() {
	*x = Activity{}
	mi := &file_api_v1_activity_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Activity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity) ProtoMessage() {}

func (x *Activity) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity.ProtoReflect.Descriptor instead.
func (*Activity) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{0}
}

func (x *Activity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Activity) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Activity) GetType() Activity_Type {
	if x != nil {
		return x.Type
	}
	return Activity_TYPE_UNSPECIFIED
}

func (x *Activity) GetLevel() Activity_Level {
	if x != nil {
		return x.Level
	}
	return Activity_LEVEL_UNSPECIFIED
}

func (x *Activity) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Activity) GetPayload() *ActivityPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type ActivityPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*ActivityPayload_MemoComment
	Payload       isActivityPayload_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityPayload) Reset() {
	*x = ActivityPayload{}
	mi := &file_api_v1_activity_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityPayload) ProtoMessage() {}

func (x *ActivityPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityPayload.ProtoReflect.Descriptor instead.
func (*ActivityPayload) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{1}
}

func (x *ActivityPayload) GetPayload() isActivityPayload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ActivityPayload) GetMemoComment() *ActivityMemoCommentPayload {
	if x != nil {
		if x, ok := x.Payload.(*ActivityPayload_MemoComment); ok {
			return x.MemoComment
		}
	}
	return nil
}

type isActivityPayload_Payload interface {
	isActivityPayload_Payload()
}

type ActivityPayload_MemoComment struct {
	// Memo comment activity payload.
	MemoComment *ActivityMemoCommentPayload `protobuf:"bytes,1,opt,name=memo_comment,json=memoComment,proto3,oneof"`
}

func (*ActivityPayload_MemoComment) isActivityPayload_Payload() {}

// ActivityMemoCommentPayload represents the payload of a memo comment activity.
type ActivityMemoCommentPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The memo name of comment.
	// Format: memos/{memo}
	Memo string `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
	// The name of related memo.
	// Format: memos/{memo}
	RelatedMemo   string `protobuf:"bytes,2,opt,name=related_memo,json=relatedMemo,proto3" json:"related_memo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityMemoCommentPayload) Reset() {
	*x = ActivityMemoCommentPayload{}
	mi := &file_api_v1_activity_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityMemoCommentPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityMemoCommentPayload) ProtoMessage() {}

func (x *ActivityMemoCommentPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityMemoCommentPayload.ProtoReflect.Descriptor instead.
func (*ActivityMemoCommentPayload) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{2}
}

func (x *ActivityMemoCommentPayload) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *ActivityMemoCommentPayload) GetRelatedMemo() string {
	if x != nil {
		return x.RelatedMemo
	}
	return ""
}

type ListActivitiesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The maximum number of activities to return.
	// The service may return fewer than this value.
	// If unspecified, at most 100 activities will be returned.
	// The maximum value is 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A page token, received from a previous `ListActivities` call.
	// Provide this to retrieve the subsequent page.
	PageToken     string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActivitiesRequest) Reset() {
	*x = ListActivitiesRequest{}
	mi := &file_api_v1_activity_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActivitiesRequest) ProtoMessage() {}

func (x *ListActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActivitiesRequest.ProtoReflect.Descriptor instead.
func (*ListActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListActivitiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListActivitiesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListActivitiesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The activities.
	Activities []*Activity `protobuf:"bytes,1,rep,name=activities,proto3" json:"activities,omitempty"`
	// A token to retrieve the next page of results.
	// Pass this value in the page_token field in the subsequent call to `ListActivities`
	// method to retrieve the next page of results.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActivitiesResponse) Reset() {
	*x = ListActivitiesResponse{}
	mi := &file_api_v1_activity_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActivitiesResponse) ProtoMessage() {}

func (x *ListActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActivitiesResponse.ProtoReflect.Descriptor instead.
func (*ListActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListActivitiesResponse) GetActivities() []*Activity {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *ListActivitiesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type GetActivityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the activity.
	// Format: activities/{id}, id is the system generated auto-incremented id.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActivityRequest) Reset() {
	*x = GetActivityRequest{}
	mi := &file_api_v1_activity_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityRequest) ProtoMessage() {}

func (x *GetActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityRequest.ProtoReflect.Descriptor instead.
func (*GetActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetActivityRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_activity_service_proto protoreflect.FileDescriptor

const file_api_v1_activity_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/v1/activity_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x86\x04\n" +
	"\bActivity\x12\x1a\n" +
	"\x04name\x18\x01 \x01(\tB\x06\xe0A\x03\xe0A\bR\x04name\x12\x1d\n" +
	"\acreator\x18\x02 \x01(\tB\x03\xe0A\x03R\acreator\x124\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1b.memos.api.v1.Activity.TypeB\x03\xe0A\x03R\x04type\x127\n" +
	"\x05level\x18\x04 \x01(\x0e2\x1c.memos.api.v1.Activity.LevelB\x03\xe0A\x03R\x05level\x12@\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12<\n" +
	"\apayload\x18\x06 \x01(\v2\x1d.memos.api.v1.ActivityPayloadB\x03\xe0A\x03R\apayload\"B\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fMEMO_COMMENT\x10\x01\x12\x12\n" +
	"\x0eVERSION_UPDATE\x10\x02\"=\n" +
	"\x05Level\x12\x15\n" +
	"\x11LEVEL_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04INFO\x10\x01\x12\b\n" +
	"\x04WARN\x10\x02\x12\t\n" +
	"\x05ERROR\x10\x03:M\xeaAJ\n" +
	"\x15memos.api.v1/Activity\x12\x15activities/{activity}\x1a\x04name*\n" +
	"activities2\bactivity\"k\n" +
	"\x0fActivityPayload\x12M\n" +
	"\fmemo_comment\x18\x01 \x01(\v2(.memos.api.v1.ActivityMemoCommentPayloadH\x00R\vmemoCommentB\t\n" +
	"\apayload\"S\n" +
	"\x1aActivityMemoCommentPayload\x12\x12\n" +
	"\x04memo\x18\x01 \x01(\tR\x04memo\x12!\n" +
	"\frelated_memo\x18\x02 \x01(\tR\vrelatedMemo\"S\n" +
	"\x15ListActivitiesRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tR\tpageToken\"x\n" +
	"\x16ListActivitiesResponse\x126\n" +
	"\n" +
	"activities\x18\x01 \x03(\v2\x16.memos.api.v1.ActivityR\n" +
	"activities\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"G\n" +
	"\x12GetActivityRequest\x121\n" +
	"\x04name\x18\x01 \x01(\tB\x1d\xe0A\x02\xfaA\x17\n" +
	"\x15memos.api.v1/ActivityR\x04name2\xff\x01\n" +
	"\x0fActivityService\x12w\n" +
	"\x0eListActivities\x12#.memos.api.v1.ListActivitiesRequest\x1a$.memos.api.v1.ListActivitiesResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/api/v1/activities\x12s\n" +
	"\vGetActivity\x12 .memos.api.v1.GetActivityRequest\x1a\x16.memos.api.v1.Activity\"*\xdaA\x04name\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v1/{name=activities/*}B\xac\x01\n" +
	"\x10com.memos.api.v1B\x14ActivityServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_activity_service_proto_rawDescOnce sync.Once
	file_api_v1_activity_service_proto_rawDescData []byte
)

func file_api_v1_activity_service_proto_rawDescGZIP() []byte {
	file_api_v1_activity_service_proto_rawDescOnce.Do(func() {
		file_api_v1_activity_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_activity_service_proto_rawDesc), len(file_api_v1_activity_service_proto_rawDesc)))
	})
	return file_api_v1_activity_service_proto_rawDescData
}

var file_api_v1_activity_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_v1_activity_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_v1_activity_service_proto_goTypes = []any{
	(Activity_Type)(0),                 // 0: memos.api.v1.Activity.Type
	(Activity_Level)(0),                // 1: memos.api.v1.Activity.Level
	(*Activity)(nil),                   // 2: memos.api.v1.Activity
	(*ActivityPayload)(nil),            // 3: memos.api.v1.ActivityPayload
	(*ActivityMemoCommentPayload)(nil), // 4: memos.api.v1.ActivityMemoCommentPayload
	(*ListActivitiesRequest)(nil),      // 5: memos.api.v1.ListActivitiesRequest
	(*ListActivitiesResponse)(nil),     // 6: memos.api.v1.ListActivitiesResponse
	(*GetActivityRequest)(nil),         // 7: memos.api.v1.GetActivityRequest
	(*timestamppb.Timestamp)(nil),      // 8: google.protobuf.Timestamp
}
var file_api_v1_activity_service_proto_depIdxs = []int32{
	0, // 0: memos.api.v1.Activity.type:type_name -> memos.api.v1.Activity.Type
	1, // 1: memos.api.v1.Activity.level:type_name -> memos.api.v1.Activity.Level
	8, // 2: memos.api.v1.Activity.create_time:type_name -> google.protobuf.Timestamp
	3, // 3: memos.api.v1.Activity.payload:type_name -> memos.api.v1.ActivityPayload
	4, // 4: memos.api.v1.ActivityPayload.memo_comment:type_name -> memos.api.v1.ActivityMemoCommentPayload
	2, // 5: memos.api.v1.ListActivitiesResponse.activities:type_name -> memos.api.v1.Activity
	5, // 6: memos.api.v1.ActivityService.ListActivities:input_type -> memos.api.v1.ListActivitiesRequest
	7, // 7: memos.api.v1.ActivityService.GetActivity:input_type -> memos.api.v1.GetActivityRequest
	6, // 8: memos.api.v1.ActivityService.ListActivities:output_type -> memos.api.v1.ListActivitiesResponse
	2, // 9: memos.api.v1.ActivityService.GetActivity:output_type -> memos.api.v1.Activity
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_v1_activity_service_proto_init() }
func file_api_v1_activity_service_proto_init() {
	if File_api_v1_activity_service_proto != nil {
		return
	}
	file_api_v1_activity_service_proto_msgTypes[1].OneofWrappers = []any{
		(*ActivityPayload_MemoComment)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_activity_service_proto_rawDesc), len(file_api_v1_activity_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_activity_service_proto_goTypes,
		DependencyIndexes: file_api_v1_activity_service_proto_depIdxs,
		EnumInfos:         file_api_v1_activity_service_proto_enumTypes,
		MessageInfos:      file_api_v1_activity_service_proto_msgTypes,
	}.Build()
	File_api_v1_activity_service_proto = out.File
	file_api_v1_activity_service_proto_goTypes = nil
	file_api_v1_activity_service_proto_depIdxs = nil
}
