// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/shortcut_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";

export const protobufPackage = "memos.api.v1";

export interface Shortcut {
  /**
   * The resource name of the shortcut.
   * Format: users/{user}/shortcuts/{shortcut}
   */
  name: string;
  /** The title of the shortcut. */
  title: string;
  /** The filter expression for the shortcut. */
  filter: string;
}

export interface ListShortcutsRequest {
  /**
   * Required. The parent resource where shortcuts are listed.
   * Format: users/{user}
   */
  parent: string;
}

export interface ListShortcutsResponse {
  /** The list of shortcuts. */
  shortcuts: Shortcut[];
}

export interface GetShortcutRequest {
  /**
   * Required. The resource name of the shortcut to retrieve.
   * Format: users/{user}/shortcuts/{shortcut}
   */
  name: string;
}

export interface CreateShortcutRequest {
  /**
   * Required. The parent resource where this shortcut will be created.
   * Format: users/{user}
   */
  parent: string;
  /** Required. The shortcut to create. */
  shortcut?:
    | Shortcut
    | undefined;
  /** Optional. If set, validate the request, but do not actually create the shortcut. */
  validateOnly: boolean;
}

export interface UpdateShortcutRequest {
  /** Required. The shortcut resource which replaces the resource on the server. */
  shortcut?:
    | Shortcut
    | undefined;
  /** Optional. The list of fields to update. */
  updateMask?: string[] | undefined;
}

export interface DeleteShortcutRequest {
  /**
   * Required. The resource name of the shortcut to delete.
   * Format: users/{user}/shortcuts/{shortcut}
   */
  name: string;
}

function createBaseShortcut(): Shortcut {
  return { name: "", title: "", filter: "" };
}

export const Shortcut: MessageFns<Shortcut> = {
  encode(message: Shortcut, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.filter !== "") {
      writer.uint32(26).string(message.filter);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Shortcut {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseShortcut();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Shortcut>): Shortcut {
    return Shortcut.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Shortcut>): Shortcut {
    const message = createBaseShortcut();
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.filter = object.filter ?? "";
    return message;
  },
};

function createBaseListShortcutsRequest(): ListShortcutsRequest {
  return { parent: "" };
}

export const ListShortcutsRequest: MessageFns<ListShortcutsRequest> = {
  encode(message: ListShortcutsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListShortcutsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListShortcutsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListShortcutsRequest>): ListShortcutsRequest {
    return ListShortcutsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListShortcutsRequest>): ListShortcutsRequest {
    const message = createBaseListShortcutsRequest();
    message.parent = object.parent ?? "";
    return message;
  },
};

function createBaseListShortcutsResponse(): ListShortcutsResponse {
  return { shortcuts: [] };
}

export const ListShortcutsResponse: MessageFns<ListShortcutsResponse> = {
  encode(message: ListShortcutsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.shortcuts) {
      Shortcut.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListShortcutsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListShortcutsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.shortcuts.push(Shortcut.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListShortcutsResponse>): ListShortcutsResponse {
    return ListShortcutsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListShortcutsResponse>): ListShortcutsResponse {
    const message = createBaseListShortcutsResponse();
    message.shortcuts = object.shortcuts?.map((e) => Shortcut.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetShortcutRequest(): GetShortcutRequest {
  return { name: "" };
}

export const GetShortcutRequest: MessageFns<GetShortcutRequest> = {
  encode(message: GetShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetShortcutRequest>): GetShortcutRequest {
    return GetShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetShortcutRequest>): GetShortcutRequest {
    const message = createBaseGetShortcutRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseCreateShortcutRequest(): CreateShortcutRequest {
  return { parent: "", shortcut: undefined, validateOnly: false };
}

export const CreateShortcutRequest: MessageFns<CreateShortcutRequest> = {
  encode(message: CreateShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.shortcut !== undefined) {
      Shortcut.encode(message.shortcut, writer.uint32(18).fork()).join();
    }
    if (message.validateOnly !== false) {
      writer.uint32(24).bool(message.validateOnly);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortcut = Shortcut.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.validateOnly = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateShortcutRequest>): CreateShortcutRequest {
    return CreateShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateShortcutRequest>): CreateShortcutRequest {
    const message = createBaseCreateShortcutRequest();
    message.parent = object.parent ?? "";
    message.shortcut = (object.shortcut !== undefined && object.shortcut !== null)
      ? Shortcut.fromPartial(object.shortcut)
      : undefined;
    message.validateOnly = object.validateOnly ?? false;
    return message;
  },
};

function createBaseUpdateShortcutRequest(): UpdateShortcutRequest {
  return { shortcut: undefined, updateMask: undefined };
}

export const UpdateShortcutRequest: MessageFns<UpdateShortcutRequest> = {
  encode(message: UpdateShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.shortcut !== undefined) {
      Shortcut.encode(message.shortcut, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.shortcut = Shortcut.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateShortcutRequest>): UpdateShortcutRequest {
    return UpdateShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateShortcutRequest>): UpdateShortcutRequest {
    const message = createBaseUpdateShortcutRequest();
    message.shortcut = (object.shortcut !== undefined && object.shortcut !== null)
      ? Shortcut.fromPartial(object.shortcut)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteShortcutRequest(): DeleteShortcutRequest {
  return { name: "" };
}

export const DeleteShortcutRequest: MessageFns<DeleteShortcutRequest> = {
  encode(message: DeleteShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteShortcutRequest>): DeleteShortcutRequest {
    return DeleteShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteShortcutRequest>): DeleteShortcutRequest {
    const message = createBaseDeleteShortcutRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type ShortcutServiceDefinition = typeof ShortcutServiceDefinition;
export const ShortcutServiceDefinition = {
  name: "ShortcutService",
  fullName: "memos.api.v1.ShortcutService",
  methods: {
    /** ListShortcuts returns a list of shortcuts for a user. */
    listShortcuts: {
      name: "ListShortcuts",
      requestType: ListShortcutsRequest,
      requestStream: false,
      responseType: ListShortcutsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([6, 112, 97, 114, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetShortcut gets a shortcut by name. */
    getShortcut: {
      name: "GetShortcut",
      requestType: GetShortcutRequest,
      requestStream: false,
      responseType: Shortcut,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** CreateShortcut creates a new shortcut for a user. */
    createShortcut: {
      name: "CreateShortcut",
      requestType: CreateShortcutRequest,
      requestStream: false,
      responseType: Shortcut,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([15, 112, 97, 114, 101, 110, 116, 44, 115, 104, 111, 114, 116, 99, 117, 116])],
          578365826: [
            new Uint8Array([
              46,
              58,
              8,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              34,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** UpdateShortcut updates a shortcut for a user. */
    updateShortcut: {
      name: "UpdateShortcut",
      requestType: UpdateShortcutRequest,
      requestStream: false,
      responseType: Shortcut,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              20,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              55,
              58,
              8,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              50,
              43,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              46,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteShortcut deletes a shortcut for a user. */
    deleteShortcut: {
      name: "DeleteShortcut",
      requestType: DeleteShortcutRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              36,
              42,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
