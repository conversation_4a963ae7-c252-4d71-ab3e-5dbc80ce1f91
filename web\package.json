{"name": "memos", "private": true, "scripts": {"dev": "vite", "build": "vite build", "release": "vite build --mode release --outDir=../server/router/frontend/dist --emptyOutDir", "lint": "tsc --noEmit --skipLibCheck && eslint --ext .js,.ts,.tsx, src"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@github/relative-time-element": "^4.4.8", "@matejmazur/react-katex": "^3.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "fuse.js": "^7.1.0", "highlight.js": "^11.11.1", "i18next": "^25.3.0", "katex": "^0.16.22", "leaflet": "^1.9.4", "lodash-es": "^4.17.21", "lucide-react": "^0.486.0", "mermaid": "^11.8.0", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-force-graph-2d": "^1.28.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-leaflet": "^4.2.1", "react-router-dom": "^7.6.3", "react-simple-pull-to-refresh": "^1.3.3", "react-use": "^17.6.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "textarea-caret": "^3.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@bufbuild/protobuf": "^2.6.0", "@eslint/js": "^9.30.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/d3": "^7.4.3", "@types/katex": "^0.16.7", "@types/leaflet": "^1.9.19", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.10", "@types/qs": "^6.14.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/textarea-caret": "^3.0.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "code-inspector-plugin": "^0.18.3", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "long": "^5.3.2", "nice-grpc-web": "^3.3.7", "prettier": "^3.6.2", "terser": "^5.43.1", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.1"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}