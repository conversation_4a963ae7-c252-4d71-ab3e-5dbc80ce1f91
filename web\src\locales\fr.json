{"about": {"blogs": "Blogs", "description": "Un service de prise de notes léger et respectueux de la vie privée. Capturez et partagez facilement vos meilleures idées.", "documents": "Documents", "github-repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "official-website": "Site web officiel"}, "auth": {"create-your-account": "<PERSON><PERSON><PERSON> votre compte", "host-tip": "Vous vous inscrivez en tant qu'hébergeur du site.", "new-password": "Nouveau mot de passe", "repeat-new-password": "<PERSON><PERSON><PERSON><PERSON><PERSON> le nouveau mot de passe", "sign-in-tip": "Vous avez déjà un compte ?", "sign-up-tip": "Vous n'avez pas encore de compte ?"}, "common": {"about": "À propos", "add": "Ajouter", "admin": "Admin", "archive": "Archiver", "archived": "Archivés", "avatar": "Avatar", "basic": "Base", "beta": "Beta", "cancel": "Annuler", "change": "Changer", "clear": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "create": "<PERSON><PERSON><PERSON>", "created-at": "<PERSON><PERSON><PERSON>", "database": "Base de données", "day": "Jour", "days": "Jours", "delete": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "edit": "Modifier", "email": "Email", "expand": "<PERSON><PERSON><PERSON>", "explore": "Explorer", "file": "<PERSON><PERSON><PERSON>", "filter": "Filtre", "home": "Accueil", "image": "Image", "in": "En", "inbox": "Notifications", "input": "Input", "language": "<PERSON><PERSON>", "last-updated-at": "<PERSON><PERSON><PERSON> mise à jour le", "layout": "Layout", "learn-more": "En savoir plus", "link": "<PERSON><PERSON>", "mark": "<PERSON><PERSON>", "memo": "Memo", "memos": "Memos", "name": "Nom", "new": "Nouveau", "nickname": "Surnom", "null": "<PERSON><PERSON><PERSON>", "or": "ou", "password": "Mot de passe", "pin": "<PERSON><PERSON><PERSON>", "pinned": "<PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "properties": "Propriétés", "referenced-by": "Référencé par", "referencing": "Référence", "relations": "Relations", "remember-me": "Se souvenir de moi", "rename": "<PERSON>mmer", "reset": "Réinitialiser", "resources": "Ressources", "restore": "<PERSON><PERSON><PERSON>", "role": "R<PERSON><PERSON>", "save": "Enregistrer", "search": "Recherche", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Paramètres", "share": "Partager", "shortcut-filter": "Filtre de racco<PERSON>ci", "shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "sign-in": "Se connecter", "sign-in-with": "S'identifier avec {{provider}}", "sign-out": "Se déconnecter", "sign-up": "S'inscrire", "statistics": "Statistiques", "tags": "Étiquettes", "title": "Titre", "tree-mode": "Mode d'arborescence", "type": "Type", "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update": "Mise à jour", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": "Utilisa<PERSON>ur", "username": "Nom d'utilisateur", "version": "Version", "visibility": "Visibilité", "yourself": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "days": {"fri": "Ve", "mon": "<PERSON>", "sat": "Sa", "sun": "Di", "thu": "Je", "tue": "Ma", "wed": "Me"}, "editor": {"add-your-comment-here": "Ajoutez votre commentaire ici …", "any-thoughts": "Une idée …", "save": "Enregistrer"}, "filters": {"has-code": "possède un code", "has-link": "possède un lien", "has-task-list": "possède une liste de tâches"}, "inbox": {"memo-comment": "{{user}} a fait un commentaire sur votre {{memo}}.", "version-update": "Une nouvelle version {{version}} est disponible dès maintenant !"}, "markdown": {"checkbox": "Case à cocher", "code-block": "Bloc de code", "content-syntax": "Syntaxe du contenu"}, "memo": {"archived-at": "<PERSON><PERSON><PERSON>", "click-to-hide-nsfw-content": "Cliquer pour masquer le contenu NSFW", "click-to-show-nsfw-content": "Cliquer pour afficher le contenu NSFW", "code": "Code", "comment": {"self": "Commentaires", "write-a-comment": "Écrire un commentaire"}, "copy-link": "Copier le lien", "count-memos-in-date": "{{count}} {{memos}} le {{date}}", "delete-confirm": "Êtes-vous sûr de vouloir supprimer ce memos ? CETTE ACTION EST IRRÉVERSIBLE", "direction": "Ordre", "direction-asc": "Ascendant", "direction-desc": "Descendant", "display-time": "Date d'Affichage", "filters": "Filtres", "links": "<PERSON><PERSON>", "load-more": "Charger plus", "no-archived-memos": "Pas de memos archivés.", "no-memos": "Pas de memos.", "order-by": "Ordonner par", "remove-completed-task-list-items": "Suppression terminée", "remove-completed-task-list-items-confirm": "Êtes-vous sûr de vouloir supprimer toutes les tâches terminées ? CETTE ACTION EST IRRÉVERSIBLE", "search-placeholder": "Recherche de memos", "show-less": "Affiche<PERSON> moins", "show-more": "Afficher plus", "to-do": "À faire", "view-detail": "Voir le détail", "visibility": {"disabled": "Les memos publics sont désactivés", "private": "Priv<PERSON>", "protected": "Accès aux membres", "public": "Public"}, "list": "Liste", "masonry": "Mosaïque"}, "message": {"archived-successfully": "Archivé a<PERSON> succès", "change-memo-created-time": "Modifier l'heure de création du memo", "copied": "<PERSON><PERSON><PERSON>", "deleted-successfully": "Supprimé avec succès", "description-is-required": "La description est requise", "failed-to-embed-memo": "Échec de l'intégration du memo.", "fill-all": "<PERSON><PERSON><PERSON>z remplir tous les champs.", "fill-all-required-fields": "<PERSON><PERSON><PERSON> de remplir tous les champs requis", "maximum-upload-size-is": "La taille maximale autorisée pour le téléversement est {{size}} MiB", "memo-not-found": "Memo introuvable.", "new-password-not-match": "Les nouveaux mots de passe ne correspondent pas.", "no-data": "Aucune donnée n'a été trouvée.", "password-changed": "Mot de passe modifié", "password-not-match": "Les mots de passe ne correspondent pas.", "remove-completed-task-list-items-successfully": "Supprimé avec succès !", "restored-successfully": "Restauré avec succès", "succeed-copy-link": "Lien copié dans le presse-papier avec succès.", "update-succeed": "Mise à jour effectuée", "user-not-found": "Utilisateur introuvable"}, "reference": {"add-references": "Ajouter des références", "embedded-usage": "Utiliser comme contenu intégré", "no-memos-found": "Aucun memos trouvé", "search-placeholder": "Rechercher du contenu"}, "resource": {"clear": "<PERSON><PERSON><PERSON>", "copy-link": "Copier le lien", "create-dialog": {"external-link": {"file-name": "Nom du fichier", "file-name-placeholder": "Nom du fichier", "link": "<PERSON><PERSON>", "link-placeholder": "https://le.lien.vers/votre/ressource", "option": "Lien externe", "type": "Type", "type-placeholder": "Type de fichier"}, "local-file": {"choose": "<PERSON>sir un fichier …", "option": "Fichier local"}, "title": "<PERSON><PERSON><PERSON> une ressource", "upload-method": "Méthode de téléversement"}, "delete-resource": "Supprimer la ressource", "delete-selected-resources": "Supprimer les ressources sélectionnées", "fetching-data": "Récupération des données …", "file-drag-drop-prompt": "Glissez-<PERSON><PERSON><PERSON>z votre fichier ici pour le téléverser", "linked-amount": "Quantité liée", "no-files-selected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "no-resources": "Aucune ressource.", "no-unused-resources": "Aucune ressource inutilisée", "reset-link": "Réinitialiser le lien", "reset-link-prompt": "Êtes-vous sûr de réinitialiser le lien ? Cette action interrompra toutes les utilisations actuelles du lien. CETTE ACTION EST IRRÉVERSIBLE", "reset-resource-link": "Réinitialiser le lien de ressource", "unused-resources": "Ressources inutilisées"}, "router": {"back-to-top": "<PERSON><PERSON> de page", "go-to-home": "Aller à l'Accueil"}, "setting": {"access-token-section": {"access-token-copied-to-clipboard": "Jeton d'accès copié dans le presse-papier", "access-token-deletion": "Êtes-vous sûr de vouloir supprimer le jeton d'accès {{accessToken}}? CETTE ACTION EST IRRÉVERSIBLE.", "create-dialog": {"create-access-token": "<PERSON><PERSON>er un jeton d'accès", "created-at": "<PERSON><PERSON><PERSON>", "description": "Description", "duration-1m": "1 Mois", "duration-8h": "8 Heures", "duration-never": "<PERSON><PERSON>", "expiration": "Expiration", "expires-at": "Expire le", "some-description": "Une description…"}, "description": "Une liste de tous les jetons d'accès pour votre compte.", "title": "Jetons d'accès", "token": "<PERSON><PERSON>"}, "account-section": {"change-password": "Modifier le mot de passe", "email-note": "Optionnel", "export-memos": "Exporter memos", "nickname-note": "Affiché dans le profil", "openapi-reset": "Réinitialiser la clé OpenAPI", "openapi-sample-post": "Bonjour #memos de {{url}}", "openapi-title": "OpenAPI", "reset-api": "Réinitialiser API", "title": "Informations sur le compte", "update-information": "Mettre à jour les informations", "username-note": "Utilisé pour s'identifier"}, "appearance-option": {"dark": "Toujours sombre", "light": "<PERSON>u<PERSON><PERSON> clair", "system": "Identique au système"}, "member": "Membre", "member-list": "Liste des membres", "member-section": {"admin": "Admin", "archive-member": "Membre archivé", "archive-warning": "Êtes-vous sûr d'archiver {{username}} ?", "create-a-member": "<PERSON><PERSON><PERSON> un membre", "delete-member": "Supprimer un membre", "delete-warning": "Êtes-vous sûr de vouloir supprimer {{username}} ? CETTE ACTION EST IRRÉVERSIBLE", "user": "Utilisa<PERSON>ur"}, "memo-related": "Memo", "memo-related-settings": {"content-lenght-limit": "Taille maximum de contenu (Octet)", "enable-blur-nsfw-content": "Activer le floutage du contenu sensible (NSFW)", "enable-link-preview": "Activer la prévisualisation de liens", "enable-memo-comments": "Activer les commentaires des memos", "enable-memo-location": "Activer la géolocalisation des memos", "reactions": "Réactions", "title": "Paramètres liés à Memo"}, "my-account": "Mon compte", "preference": "Préférences", "preference-section": {"default-memo-sort-option": "Temps d'affichage des memos", "default-memo-visibility": "Visibilité par défaut des memos", "theme": "Thème"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Autorisation endpoint", "client-id": "Client ID", "client-secret": "Client secret", "confirm-delete": "Êtes-vous sûr de supprimer la configuration SSO \"{{name}}\" ? CETTE ACTION EST IRRÉVERSIBLE", "create-sso": "<PERSON><PERSON><PERSON> un SSO", "custom": "<PERSON><PERSON><PERSON><PERSON>", "delete-sso": "Confirmer la <PERSON>", "disabled-password-login-warning": "La connexion par mot de passe est désactivée, soyez très prudent lorsque vous supprimez des fournisseurs d'identité", "display-name": "Nom affiché", "identifier": "Identifiant", "identifier-filter": "Filtre d'identifiant", "no-sso-found": "Pas de SSO trouvé.", "redirect-url": "URL de redirection", "scopes": "<PERSON><PERSON><PERSON>", "single-sign-on": "Configuration du Single Sign-On (SSO) pour l'Authentification", "sso-created": "SSO {{name}} c<PERSON><PERSON>", "sso-list": "Liste SSO", "sso-updated": "SSO {{name}} mis à jour", "template": "<PERSON><PERSON><PERSON><PERSON>", "token-endpoint": "Jeton endpoint", "update-sso": "Mise à jour du SSO", "user-endpoint": "Utilisateur endpoint"}, "storage": "Stockage", "storage-section": {"accesskey": "Clé d'accès", "accesskey-placeholder": "Clé d'accès / ID d'accès", "bucket": "Bucket", "bucket-placeholder": "Nom du Bucket", "create-a-service": "Créer un service", "create-storage": "C<PERSON>er un stockage", "current-storage": "Stockage actuel d'objets", "delete-storage": "Effacer le stockage", "endpoint": "Endpoint", "filepath-template": "<PERSON>em<PERSON> du modèle", "local-storage-path": "Chemin de stockage Local", "path": "Chemin de stockage", "path-description": "Vous pouvez utiliser les mêmes variables dynamiques du stockage local, comme {filename}", "path-placeholder": "personnalisé/chemin", "presign-placeholder": "URL pré-signé, optionnel", "region": "Région", "region-placeholder": "Nom de la région", "s3-compatible-url": "URL Compatible S3", "secretkey": "<PERSON>lé secrète", "secretkey-placeholder": "Clé secrète / Clé d'accès", "storage-services": "Liste des services de stockage", "type-database": "Base de données", "type-local": "Système de fichier Local", "update-a-service": "Mettre à jour un service", "update-local-path": "Mise à jour du chemin de stockage Local", "update-local-path-description": "Le chemin d'accès au stockage local est un chemin d'accès relatif au fichier de la base de données", "update-storage": "Mise à jour du stockage", "url-prefix": "Préfixe de l'URL", "url-prefix-placeholder": "Préfixe URL personnalisé, optionnel", "url-suffix": "Suffixe URL", "url-suffix-placeholder": "Suffixe URL personnalisé, optionnel", "warning-text": "Êtes-vous sûr de vouloir supprimer le service de stockage \"{{name}}\" ? CETTE ACTION EST IRRÉVERSIBLE"}, "system": "Système", "system-section": {"additional-script": "Scripts supplémentaires", "additional-script-placeholder": "Code JavaScript supplémentaire", "additional-style": "Styles supplémentaires", "additional-style-placeholder": "Code CSS supplémentaire", "allow-user-signup": "Autoriser l'inscription des utilisateurs", "customize-server": {"appearance": "Apparence du Serveur", "description": "Description", "icon-url": "URL de l'icône", "locale": "<PERSON><PERSON> du <PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "disable-markdown-shortcuts-in-editor": "Désactiver les raccourcis Markdown dans l'éditeur", "disable-password-login": "Désactiver la connexion par mot de passe", "disable-password-login-final-warning": "Veuillez taper \"CONFIRM\" si vous savez ce que vous faites.", "disable-password-login-warning": "Cela d<PERSON>ra la connexion par mot de passe pour tous les utilisateurs. Il n'est pas possible de se connecter sans revenir sur ce paramètre dans la base de données si les fournisseurs d'identité configurés échouent. Vous devrez également être très prudent lorsque vous supprimez un fournisseur d'identité", "disable-public-memos": "Désactiver les memos publics", "display-with-updated-time": "Mettre à jour l'heure des memos lorsqu'ils sont modifiés", "enable-auto-compact": "Activer l'auto compact", "enable-double-click-to-edit": "Activer le double-clic pour modifier", "enable-password-login": "Activer la connexion par mot de passe", "enable-password-login-warning": "Ceci activera la connexion par mot de passe pour tous les utilisateurs. Ne continuez que si vous souhaitez que les utilisateurs puissent se connecter à la fois par SSO et par mot de passe", "max-upload-size": "Taille maximale du téléversement (MiB)", "max-upload-size-hint": "La valeur recommandée est 32 MiB.", "removed-completed-task-list-items": "Activer la suppression terminée", "server-name": "Nom du serveur", "title": "General"}, "version": "Version", "webhook-section": {"create-dialog": {"an-easy-to-remember-name": "Un nom facile à retenir", "create-webhook": "<PERSON><PERSON><PERSON> un webhook", "edit-webhook": "É<PERSON><PERSON> le webhook", "payload-url": "URL de payload", "title": "Titre", "url-example-post-receive": "https://exemple.com/postreceive"}, "no-webhooks-found": "Aucun webhook trouvé.", "title": "Webhooks", "url": "URL"}, "workspace-section": {"disallow-change-nickname": "Interdire le changement de surnom", "disallow-change-username": "Interdire le changement de nom d'utilisateur", "disallow-password-auth": "Interdire l'authentification par mot de passe", "disallow-user-registration": "Interdire l'inscription de nouveaux utilisateurs", "monday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "week-start-day": "Premier j<PERSON> de la semaine"}}, "tag": {"all-tags": "Toutes les Étiquettes", "create-tag": "<PERSON><PERSON>er une Étiquette", "create-tags-guide": "Vous pouvez ajouter des étiquettes en écrivant `#etiquette`.", "delete-confirm": "Voulez-vous vraiment supprimer cette étiquette? Tous les memos liés seront archivés.", "delete-tag": "Supprimer l'étiquette", "new-name": "Nouveau nom", "no-tag-found": "<PERSON><PERSON>ne éti<PERSON>te trouvée", "old-name": "Ancien nom", "rename-error-empty": "Le nom de l'étiquette ne peut pas être vide ou contenir des espaces", "rename-error-repeat": "Le nouveau nom ne peut pas être identique à l'ancien", "rename-success": "Étiquette renommée avec succès", "rename-tag": "Renommer l'étiquette", "rename-tip": "Tous vos memos avec cette étiquette seront mis à jour."}}