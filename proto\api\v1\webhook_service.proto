syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "gen/api/v1";

service WebhookService {
  // ListWebhooks returns a list of webhooks for a user.
  rpc ListWebhooks(ListWebhooksRequest) returns (ListWebhooksResponse) {
    option (google.api.http) = {get: "/api/v1/{parent=users/*}/webhooks"};
    option (google.api.method_signature) = "parent";
  }

  // GetWebhook gets a webhook by name.
  rpc GetWebhook(GetWebhookRequest) returns (Webhook) {
    option (google.api.http) = {get: "/api/v1/{name=users/*/webhooks/*}"};
    option (google.api.method_signature) = "name";
  }

  // CreateWebhook creates a new webhook for a user.
  rpc CreateWebhook(CreateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      post: "/api/v1/{parent=users/*}/webhooks"
      body: "webhook"
    };
    option (google.api.method_signature) = "parent,webhook";
  }

  // UpdateWebhook updates a webhook for a user.
  rpc UpdateWebhook(UpdateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      patch: "/api/v1/{webhook.name=users/*/webhooks/*}"
      body: "webhook"
    };
    option (google.api.method_signature) = "webhook,update_mask";
  }

  // DeleteWebhook deletes a webhook for a user.
  rpc DeleteWebhook(DeleteWebhookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/api/v1/{name=users/*/webhooks/*}"};
    option (google.api.method_signature) = "name";
  }
}

message Webhook {
  option (google.api.resource) = {
    type: "memos.api.v1/Webhook"
    pattern: "users/{user}/webhooks/{webhook}"
    singular: "webhook"
    plural: "webhooks"
  };

  // The resource name of the webhook.
  // Format: users/{user}/webhooks/{webhook}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The display name of the webhook.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The target URL for the webhook.
  string url = 3 [(google.api.field_behavior) = REQUIRED];
}

message ListWebhooksRequest {
  // Required. The parent resource where webhooks are listed.
  // Format: users/{user}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "memos.api.v1/Webhook"}
  ];
}

message ListWebhooksResponse {
  // The list of webhooks.
  repeated Webhook webhooks = 1;
}

message GetWebhookRequest {
  // Required. The resource name of the webhook to retrieve.
  // Format: users/{user}/webhooks/{webhook}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/Webhook"}
  ];
}

message CreateWebhookRequest {
  // Required. The parent resource where this webhook will be created.
  // Format: users/{user}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "memos.api.v1/Webhook"}
  ];

  // Required. The webhook to create.
  Webhook webhook = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request, but do not actually create the webhook.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

message UpdateWebhookRequest {
  // Required. The webhook resource which replaces the resource on the server.
  Webhook webhook = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = OPTIONAL];
}

message DeleteWebhookRequest {
  // Required. The resource name of the webhook to delete.
  // Format: users/{user}/webhooks/{webhook}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/Webhook"}
  ];
}
