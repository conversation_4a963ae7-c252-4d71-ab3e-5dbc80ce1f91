// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/workspace_setting.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkspaceSettingKey int32

const (
	WorkspaceSettingKey_WORKSPACE_SETTING_KEY_UNSPECIFIED WorkspaceSettingKey = 0
	// BASIC is the key for basic settings.
	WorkspaceSettingKey_BASIC WorkspaceSettingKey = 1
	// GENERAL is the key for general settings.
	WorkspaceSettingKey_GENERAL WorkspaceSettingKey = 2
	// STORAGE is the key for storage settings.
	WorkspaceSettingKey_STORAGE WorkspaceSettingKey = 3
	// MEMO_RELATED is the key for memo related settings.
	WorkspaceSettingKey_MEMO_RELATED WorkspaceSettingKey = 4
)

// Enum value maps for WorkspaceSettingKey.
var (
	WorkspaceSettingKey_name = map[int32]string{
		0: "WORKSPACE_SETTING_KEY_UNSPECIFIED",
		1: "BASIC",
		2: "GENERAL",
		3: "STORAGE",
		4: "MEMO_RELATED",
	}
	WorkspaceSettingKey_value = map[string]int32{
		"WORKSPACE_SETTING_KEY_UNSPECIFIED": 0,
		"BASIC":                             1,
		"GENERAL":                           2,
		"STORAGE":                           3,
		"MEMO_RELATED":                      4,
	}
)

func (x WorkspaceSettingKey) Enum() *WorkspaceSettingKey {
	p := new(WorkspaceSettingKey)
	*p = x
	return p
}

func (x WorkspaceSettingKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkspaceSettingKey) Descriptor() protoreflect.EnumDescriptor {
	return file_store_workspace_setting_proto_enumTypes[0].Descriptor()
}

func (WorkspaceSettingKey) Type() protoreflect.EnumType {
	return &file_store_workspace_setting_proto_enumTypes[0]
}

func (x WorkspaceSettingKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkspaceSettingKey.Descriptor instead.
func (WorkspaceSettingKey) EnumDescriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{0}
}

type WorkspaceStorageSetting_StorageType int32

const (
	WorkspaceStorageSetting_STORAGE_TYPE_UNSPECIFIED WorkspaceStorageSetting_StorageType = 0
	// STORAGE_TYPE_DATABASE is the database storage type.
	WorkspaceStorageSetting_DATABASE WorkspaceStorageSetting_StorageType = 1
	// STORAGE_TYPE_LOCAL is the local storage type.
	WorkspaceStorageSetting_LOCAL WorkspaceStorageSetting_StorageType = 2
	// STORAGE_TYPE_S3 is the S3 storage type.
	WorkspaceStorageSetting_S3 WorkspaceStorageSetting_StorageType = 3
)

// Enum value maps for WorkspaceStorageSetting_StorageType.
var (
	WorkspaceStorageSetting_StorageType_name = map[int32]string{
		0: "STORAGE_TYPE_UNSPECIFIED",
		1: "DATABASE",
		2: "LOCAL",
		3: "S3",
	}
	WorkspaceStorageSetting_StorageType_value = map[string]int32{
		"STORAGE_TYPE_UNSPECIFIED": 0,
		"DATABASE":                 1,
		"LOCAL":                    2,
		"S3":                       3,
	}
)

func (x WorkspaceStorageSetting_StorageType) Enum() *WorkspaceStorageSetting_StorageType {
	p := new(WorkspaceStorageSetting_StorageType)
	*p = x
	return p
}

func (x WorkspaceStorageSetting_StorageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkspaceStorageSetting_StorageType) Descriptor() protoreflect.EnumDescriptor {
	return file_store_workspace_setting_proto_enumTypes[1].Descriptor()
}

func (WorkspaceStorageSetting_StorageType) Type() protoreflect.EnumType {
	return &file_store_workspace_setting_proto_enumTypes[1]
}

func (x WorkspaceStorageSetting_StorageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkspaceStorageSetting_StorageType.Descriptor instead.
func (WorkspaceStorageSetting_StorageType) EnumDescriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{4, 0}
}

type WorkspaceSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Key   WorkspaceSettingKey    `protobuf:"varint,1,opt,name=key,proto3,enum=memos.store.WorkspaceSettingKey" json:"key,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*WorkspaceSetting_BasicSetting
	//	*WorkspaceSetting_GeneralSetting
	//	*WorkspaceSetting_StorageSetting
	//	*WorkspaceSetting_MemoRelatedSetting
	Value         isWorkspaceSetting_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceSetting) Reset() {
	*x = WorkspaceSetting{}
	mi := &file_store_workspace_setting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceSetting) ProtoMessage() {}

func (x *WorkspaceSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceSetting.ProtoReflect.Descriptor instead.
func (*WorkspaceSetting) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{0}
}

func (x *WorkspaceSetting) GetKey() WorkspaceSettingKey {
	if x != nil {
		return x.Key
	}
	return WorkspaceSettingKey_WORKSPACE_SETTING_KEY_UNSPECIFIED
}

func (x *WorkspaceSetting) GetValue() isWorkspaceSetting_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *WorkspaceSetting) GetBasicSetting() *WorkspaceBasicSetting {
	if x != nil {
		if x, ok := x.Value.(*WorkspaceSetting_BasicSetting); ok {
			return x.BasicSetting
		}
	}
	return nil
}

func (x *WorkspaceSetting) GetGeneralSetting() *WorkspaceGeneralSetting {
	if x != nil {
		if x, ok := x.Value.(*WorkspaceSetting_GeneralSetting); ok {
			return x.GeneralSetting
		}
	}
	return nil
}

func (x *WorkspaceSetting) GetStorageSetting() *WorkspaceStorageSetting {
	if x != nil {
		if x, ok := x.Value.(*WorkspaceSetting_StorageSetting); ok {
			return x.StorageSetting
		}
	}
	return nil
}

func (x *WorkspaceSetting) GetMemoRelatedSetting() *WorkspaceMemoRelatedSetting {
	if x != nil {
		if x, ok := x.Value.(*WorkspaceSetting_MemoRelatedSetting); ok {
			return x.MemoRelatedSetting
		}
	}
	return nil
}

type isWorkspaceSetting_Value interface {
	isWorkspaceSetting_Value()
}

type WorkspaceSetting_BasicSetting struct {
	BasicSetting *WorkspaceBasicSetting `protobuf:"bytes,2,opt,name=basic_setting,json=basicSetting,proto3,oneof"`
}

type WorkspaceSetting_GeneralSetting struct {
	GeneralSetting *WorkspaceGeneralSetting `protobuf:"bytes,3,opt,name=general_setting,json=generalSetting,proto3,oneof"`
}

type WorkspaceSetting_StorageSetting struct {
	StorageSetting *WorkspaceStorageSetting `protobuf:"bytes,4,opt,name=storage_setting,json=storageSetting,proto3,oneof"`
}

type WorkspaceSetting_MemoRelatedSetting struct {
	MemoRelatedSetting *WorkspaceMemoRelatedSetting `protobuf:"bytes,5,opt,name=memo_related_setting,json=memoRelatedSetting,proto3,oneof"`
}

func (*WorkspaceSetting_BasicSetting) isWorkspaceSetting_Value() {}

func (*WorkspaceSetting_GeneralSetting) isWorkspaceSetting_Value() {}

func (*WorkspaceSetting_StorageSetting) isWorkspaceSetting_Value() {}

func (*WorkspaceSetting_MemoRelatedSetting) isWorkspaceSetting_Value() {}

type WorkspaceBasicSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The secret key for workspace. Mainly used for session management.
	SecretKey string `protobuf:"bytes,1,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	// The current schema version of database.
	SchemaVersion string `protobuf:"bytes,2,opt,name=schema_version,json=schemaVersion,proto3" json:"schema_version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceBasicSetting) Reset() {
	*x = WorkspaceBasicSetting{}
	mi := &file_store_workspace_setting_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceBasicSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceBasicSetting) ProtoMessage() {}

func (x *WorkspaceBasicSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceBasicSetting.ProtoReflect.Descriptor instead.
func (*WorkspaceBasicSetting) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{1}
}

func (x *WorkspaceBasicSetting) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *WorkspaceBasicSetting) GetSchemaVersion() string {
	if x != nil {
		return x.SchemaVersion
	}
	return ""
}

type WorkspaceGeneralSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// disallow_user_registration disallows user registration.
	DisallowUserRegistration bool `protobuf:"varint,1,opt,name=disallow_user_registration,json=disallowUserRegistration,proto3" json:"disallow_user_registration,omitempty"`
	// disallow_password_auth disallows password authentication.
	DisallowPasswordAuth bool `protobuf:"varint,2,opt,name=disallow_password_auth,json=disallowPasswordAuth,proto3" json:"disallow_password_auth,omitempty"`
	// additional_script is the additional script.
	AdditionalScript string `protobuf:"bytes,3,opt,name=additional_script,json=additionalScript,proto3" json:"additional_script,omitempty"`
	// additional_style is the additional style.
	AdditionalStyle string `protobuf:"bytes,4,opt,name=additional_style,json=additionalStyle,proto3" json:"additional_style,omitempty"`
	// custom_profile is the custom profile.
	CustomProfile *WorkspaceCustomProfile `protobuf:"bytes,5,opt,name=custom_profile,json=customProfile,proto3" json:"custom_profile,omitempty"`
	// week_start_day_offset is the week start day offset from Sunday.
	// 0: Sunday, 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday
	// Default is Sunday.
	WeekStartDayOffset int32 `protobuf:"varint,6,opt,name=week_start_day_offset,json=weekStartDayOffset,proto3" json:"week_start_day_offset,omitempty"`
	// disallow_change_username disallows changing username.
	DisallowChangeUsername bool `protobuf:"varint,7,opt,name=disallow_change_username,json=disallowChangeUsername,proto3" json:"disallow_change_username,omitempty"`
	// disallow_change_nickname disallows changing nickname.
	DisallowChangeNickname bool `protobuf:"varint,8,opt,name=disallow_change_nickname,json=disallowChangeNickname,proto3" json:"disallow_change_nickname,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *WorkspaceGeneralSetting) Reset() {
	*x = WorkspaceGeneralSetting{}
	mi := &file_store_workspace_setting_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceGeneralSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceGeneralSetting) ProtoMessage() {}

func (x *WorkspaceGeneralSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceGeneralSetting.ProtoReflect.Descriptor instead.
func (*WorkspaceGeneralSetting) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{2}
}

func (x *WorkspaceGeneralSetting) GetDisallowUserRegistration() bool {
	if x != nil {
		return x.DisallowUserRegistration
	}
	return false
}

func (x *WorkspaceGeneralSetting) GetDisallowPasswordAuth() bool {
	if x != nil {
		return x.DisallowPasswordAuth
	}
	return false
}

func (x *WorkspaceGeneralSetting) GetAdditionalScript() string {
	if x != nil {
		return x.AdditionalScript
	}
	return ""
}

func (x *WorkspaceGeneralSetting) GetAdditionalStyle() string {
	if x != nil {
		return x.AdditionalStyle
	}
	return ""
}

func (x *WorkspaceGeneralSetting) GetCustomProfile() *WorkspaceCustomProfile {
	if x != nil {
		return x.CustomProfile
	}
	return nil
}

func (x *WorkspaceGeneralSetting) GetWeekStartDayOffset() int32 {
	if x != nil {
		return x.WeekStartDayOffset
	}
	return 0
}

func (x *WorkspaceGeneralSetting) GetDisallowChangeUsername() bool {
	if x != nil {
		return x.DisallowChangeUsername
	}
	return false
}

func (x *WorkspaceGeneralSetting) GetDisallowChangeNickname() bool {
	if x != nil {
		return x.DisallowChangeNickname
	}
	return false
}

type WorkspaceCustomProfile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	LogoUrl       string                 `protobuf:"bytes,3,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Locale        string                 `protobuf:"bytes,4,opt,name=locale,proto3" json:"locale,omitempty"`
	Appearance    string                 `protobuf:"bytes,5,opt,name=appearance,proto3" json:"appearance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceCustomProfile) Reset() {
	*x = WorkspaceCustomProfile{}
	mi := &file_store_workspace_setting_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceCustomProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceCustomProfile) ProtoMessage() {}

func (x *WorkspaceCustomProfile) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceCustomProfile.ProtoReflect.Descriptor instead.
func (*WorkspaceCustomProfile) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{3}
}

func (x *WorkspaceCustomProfile) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WorkspaceCustomProfile) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspaceCustomProfile) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *WorkspaceCustomProfile) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *WorkspaceCustomProfile) GetAppearance() string {
	if x != nil {
		return x.Appearance
	}
	return ""
}

type WorkspaceStorageSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// storage_type is the storage type.
	StorageType WorkspaceStorageSetting_StorageType `protobuf:"varint,1,opt,name=storage_type,json=storageType,proto3,enum=memos.store.WorkspaceStorageSetting_StorageType" json:"storage_type,omitempty"`
	// The template of file path.
	// e.g. assets/{timestamp}_{filename}
	FilepathTemplate string `protobuf:"bytes,2,opt,name=filepath_template,json=filepathTemplate,proto3" json:"filepath_template,omitempty"`
	// The max upload size in megabytes.
	UploadSizeLimitMb int64 `protobuf:"varint,3,opt,name=upload_size_limit_mb,json=uploadSizeLimitMb,proto3" json:"upload_size_limit_mb,omitempty"`
	// The S3 config.
	S3Config      *StorageS3Config `protobuf:"bytes,4,opt,name=s3_config,json=s3Config,proto3" json:"s3_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceStorageSetting) Reset() {
	*x = WorkspaceStorageSetting{}
	mi := &file_store_workspace_setting_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceStorageSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceStorageSetting) ProtoMessage() {}

func (x *WorkspaceStorageSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceStorageSetting.ProtoReflect.Descriptor instead.
func (*WorkspaceStorageSetting) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{4}
}

func (x *WorkspaceStorageSetting) GetStorageType() WorkspaceStorageSetting_StorageType {
	if x != nil {
		return x.StorageType
	}
	return WorkspaceStorageSetting_STORAGE_TYPE_UNSPECIFIED
}

func (x *WorkspaceStorageSetting) GetFilepathTemplate() string {
	if x != nil {
		return x.FilepathTemplate
	}
	return ""
}

func (x *WorkspaceStorageSetting) GetUploadSizeLimitMb() int64 {
	if x != nil {
		return x.UploadSizeLimitMb
	}
	return 0
}

func (x *WorkspaceStorageSetting) GetS3Config() *StorageS3Config {
	if x != nil {
		return x.S3Config
	}
	return nil
}

// Reference: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-go/
type StorageS3Config struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AccessKeyId     string                 `protobuf:"bytes,1,opt,name=access_key_id,json=accessKeyId,proto3" json:"access_key_id,omitempty"`
	AccessKeySecret string                 `protobuf:"bytes,2,opt,name=access_key_secret,json=accessKeySecret,proto3" json:"access_key_secret,omitempty"`
	Endpoint        string                 `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Region          string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Bucket          string                 `protobuf:"bytes,5,opt,name=bucket,proto3" json:"bucket,omitempty"`
	UsePathStyle    bool                   `protobuf:"varint,6,opt,name=use_path_style,json=usePathStyle,proto3" json:"use_path_style,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StorageS3Config) Reset() {
	*x = StorageS3Config{}
	mi := &file_store_workspace_setting_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageS3Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageS3Config) ProtoMessage() {}

func (x *StorageS3Config) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageS3Config.ProtoReflect.Descriptor instead.
func (*StorageS3Config) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{5}
}

func (x *StorageS3Config) GetAccessKeyId() string {
	if x != nil {
		return x.AccessKeyId
	}
	return ""
}

func (x *StorageS3Config) GetAccessKeySecret() string {
	if x != nil {
		return x.AccessKeySecret
	}
	return ""
}

func (x *StorageS3Config) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *StorageS3Config) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *StorageS3Config) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *StorageS3Config) GetUsePathStyle() bool {
	if x != nil {
		return x.UsePathStyle
	}
	return false
}

type WorkspaceMemoRelatedSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// disallow_public_visibility disallows set memo as public visibility.
	DisallowPublicVisibility bool `protobuf:"varint,1,opt,name=disallow_public_visibility,json=disallowPublicVisibility,proto3" json:"disallow_public_visibility,omitempty"`
	// display_with_update_time orders and displays memo with update time.
	DisplayWithUpdateTime bool `protobuf:"varint,2,opt,name=display_with_update_time,json=displayWithUpdateTime,proto3" json:"display_with_update_time,omitempty"`
	// content_length_limit is the limit of content length. Unit is byte.
	ContentLengthLimit int32 `protobuf:"varint,3,opt,name=content_length_limit,json=contentLengthLimit,proto3" json:"content_length_limit,omitempty"`
	// enable_double_click_edit enables editing on double click.
	EnableDoubleClickEdit bool `protobuf:"varint,4,opt,name=enable_double_click_edit,json=enableDoubleClickEdit,proto3" json:"enable_double_click_edit,omitempty"`
	// enable_link_preview enables links preview.
	EnableLinkPreview bool `protobuf:"varint,5,opt,name=enable_link_preview,json=enableLinkPreview,proto3" json:"enable_link_preview,omitempty"`
	// enable_comment enables comment.
	EnableComment bool `protobuf:"varint,6,opt,name=enable_comment,json=enableComment,proto3" json:"enable_comment,omitempty"`
	// reactions is the list of reactions.
	Reactions []string `protobuf:"bytes,7,rep,name=reactions,proto3" json:"reactions,omitempty"`
	// disable markdown shortcuts
	DisableMarkdownShortcuts bool `protobuf:"varint,8,opt,name=disable_markdown_shortcuts,json=disableMarkdownShortcuts,proto3" json:"disable_markdown_shortcuts,omitempty"`
	// enable_blur_nsfw_content enables blurring of content marked as not safe for work (NSFW).
	EnableBlurNsfwContent bool `protobuf:"varint,9,opt,name=enable_blur_nsfw_content,json=enableBlurNsfwContent,proto3" json:"enable_blur_nsfw_content,omitempty"`
	// nsfw_tags is the list of tags that mark content as NSFW for blurring.
	NsfwTags      []string `protobuf:"bytes,10,rep,name=nsfw_tags,json=nsfwTags,proto3" json:"nsfw_tags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceMemoRelatedSetting) Reset() {
	*x = WorkspaceMemoRelatedSetting{}
	mi := &file_store_workspace_setting_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceMemoRelatedSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceMemoRelatedSetting) ProtoMessage() {}

func (x *WorkspaceMemoRelatedSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_workspace_setting_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceMemoRelatedSetting.ProtoReflect.Descriptor instead.
func (*WorkspaceMemoRelatedSetting) Descriptor() ([]byte, []int) {
	return file_store_workspace_setting_proto_rawDescGZIP(), []int{6}
}

func (x *WorkspaceMemoRelatedSetting) GetDisallowPublicVisibility() bool {
	if x != nil {
		return x.DisallowPublicVisibility
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetDisplayWithUpdateTime() bool {
	if x != nil {
		return x.DisplayWithUpdateTime
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetContentLengthLimit() int32 {
	if x != nil {
		return x.ContentLengthLimit
	}
	return 0
}

func (x *WorkspaceMemoRelatedSetting) GetEnableDoubleClickEdit() bool {
	if x != nil {
		return x.EnableDoubleClickEdit
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetEnableLinkPreview() bool {
	if x != nil {
		return x.EnableLinkPreview
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetEnableComment() bool {
	if x != nil {
		return x.EnableComment
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetReactions() []string {
	if x != nil {
		return x.Reactions
	}
	return nil
}

func (x *WorkspaceMemoRelatedSetting) GetDisableMarkdownShortcuts() bool {
	if x != nil {
		return x.DisableMarkdownShortcuts
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetEnableBlurNsfwContent() bool {
	if x != nil {
		return x.EnableBlurNsfwContent
	}
	return false
}

func (x *WorkspaceMemoRelatedSetting) GetNsfwTags() []string {
	if x != nil {
		return x.NsfwTags
	}
	return nil
}

var File_store_workspace_setting_proto protoreflect.FileDescriptor

const file_store_workspace_setting_proto_rawDesc = "" +
	"\n" +
	"\x1dstore/workspace_setting.proto\x12\vmemos.store\"\x9a\x03\n" +
	"\x10WorkspaceSetting\x122\n" +
	"\x03key\x18\x01 \x01(\x0e2 .memos.store.WorkspaceSettingKeyR\x03key\x12I\n" +
	"\rbasic_setting\x18\x02 \x01(\v2\".memos.store.WorkspaceBasicSettingH\x00R\fbasicSetting\x12O\n" +
	"\x0fgeneral_setting\x18\x03 \x01(\v2$.memos.store.WorkspaceGeneralSettingH\x00R\x0egeneralSetting\x12O\n" +
	"\x0fstorage_setting\x18\x04 \x01(\v2$.memos.store.WorkspaceStorageSettingH\x00R\x0estorageSetting\x12\\\n" +
	"\x14memo_related_setting\x18\x05 \x01(\v2(.memos.store.WorkspaceMemoRelatedSettingH\x00R\x12memoRelatedSettingB\a\n" +
	"\x05value\"]\n" +
	"\x15WorkspaceBasicSetting\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x01 \x01(\tR\tsecretKey\x12%\n" +
	"\x0eschema_version\x18\x02 \x01(\tR\rschemaVersion\"\xd8\x03\n" +
	"\x17WorkspaceGeneralSetting\x12<\n" +
	"\x1adisallow_user_registration\x18\x01 \x01(\bR\x18disallowUserRegistration\x124\n" +
	"\x16disallow_password_auth\x18\x02 \x01(\bR\x14disallowPasswordAuth\x12+\n" +
	"\x11additional_script\x18\x03 \x01(\tR\x10additionalScript\x12)\n" +
	"\x10additional_style\x18\x04 \x01(\tR\x0fadditionalStyle\x12J\n" +
	"\x0ecustom_profile\x18\x05 \x01(\v2#.memos.store.WorkspaceCustomProfileR\rcustomProfile\x121\n" +
	"\x15week_start_day_offset\x18\x06 \x01(\x05R\x12weekStartDayOffset\x128\n" +
	"\x18disallow_change_username\x18\a \x01(\bR\x16disallowChangeUsername\x128\n" +
	"\x18disallow_change_nickname\x18\b \x01(\bR\x16disallowChangeNickname\"\xa3\x01\n" +
	"\x16WorkspaceCustomProfile\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x19\n" +
	"\blogo_url\x18\x03 \x01(\tR\alogoUrl\x12\x16\n" +
	"\x06locale\x18\x04 \x01(\tR\x06locale\x12\x1e\n" +
	"\n" +
	"appearance\x18\x05 \x01(\tR\n" +
	"appearance\"\xd5\x02\n" +
	"\x17WorkspaceStorageSetting\x12S\n" +
	"\fstorage_type\x18\x01 \x01(\x0e20.memos.store.WorkspaceStorageSetting.StorageTypeR\vstorageType\x12+\n" +
	"\x11filepath_template\x18\x02 \x01(\tR\x10filepathTemplate\x12/\n" +
	"\x14upload_size_limit_mb\x18\x03 \x01(\x03R\x11uploadSizeLimitMb\x129\n" +
	"\ts3_config\x18\x04 \x01(\v2\x1c.memos.store.StorageS3ConfigR\bs3Config\"L\n" +
	"\vStorageType\x12\x1c\n" +
	"\x18STORAGE_TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bDATABASE\x10\x01\x12\t\n" +
	"\x05LOCAL\x10\x02\x12\x06\n" +
	"\x02S3\x10\x03\"\xd3\x01\n" +
	"\x0fStorageS3Config\x12\"\n" +
	"\raccess_key_id\x18\x01 \x01(\tR\vaccessKeyId\x12*\n" +
	"\x11access_key_secret\x18\x02 \x01(\tR\x0faccessKeySecret\x12\x1a\n" +
	"\bendpoint\x18\x03 \x01(\tR\bendpoint\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\x12\x16\n" +
	"\x06bucket\x18\x05 \x01(\tR\x06bucket\x12$\n" +
	"\x0euse_path_style\x18\x06 \x01(\bR\fusePathStyle\"\x88\x04\n" +
	"\x1bWorkspaceMemoRelatedSetting\x12<\n" +
	"\x1adisallow_public_visibility\x18\x01 \x01(\bR\x18disallowPublicVisibility\x127\n" +
	"\x18display_with_update_time\x18\x02 \x01(\bR\x15displayWithUpdateTime\x120\n" +
	"\x14content_length_limit\x18\x03 \x01(\x05R\x12contentLengthLimit\x127\n" +
	"\x18enable_double_click_edit\x18\x04 \x01(\bR\x15enableDoubleClickEdit\x12.\n" +
	"\x13enable_link_preview\x18\x05 \x01(\bR\x11enableLinkPreview\x12%\n" +
	"\x0eenable_comment\x18\x06 \x01(\bR\renableComment\x12\x1c\n" +
	"\treactions\x18\a \x03(\tR\treactions\x12<\n" +
	"\x1adisable_markdown_shortcuts\x18\b \x01(\bR\x18disableMarkdownShortcuts\x127\n" +
	"\x18enable_blur_nsfw_content\x18\t \x01(\bR\x15enableBlurNsfwContent\x12\x1b\n" +
	"\tnsfw_tags\x18\n" +
	" \x03(\tR\bnsfwTags*s\n" +
	"\x13WorkspaceSettingKey\x12%\n" +
	"!WORKSPACE_SETTING_KEY_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05BASIC\x10\x01\x12\v\n" +
	"\aGENERAL\x10\x02\x12\v\n" +
	"\aSTORAGE\x10\x03\x12\x10\n" +
	"\fMEMO_RELATED\x10\x04B\xa0\x01\n" +
	"\x0fcom.memos.storeB\x15WorkspaceSettingProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_workspace_setting_proto_rawDescOnce sync.Once
	file_store_workspace_setting_proto_rawDescData []byte
)

func file_store_workspace_setting_proto_rawDescGZIP() []byte {
	file_store_workspace_setting_proto_rawDescOnce.Do(func() {
		file_store_workspace_setting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_workspace_setting_proto_rawDesc), len(file_store_workspace_setting_proto_rawDesc)))
	})
	return file_store_workspace_setting_proto_rawDescData
}

var file_store_workspace_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_store_workspace_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_store_workspace_setting_proto_goTypes = []any{
	(WorkspaceSettingKey)(0),                 // 0: memos.store.WorkspaceSettingKey
	(WorkspaceStorageSetting_StorageType)(0), // 1: memos.store.WorkspaceStorageSetting.StorageType
	(*WorkspaceSetting)(nil),                 // 2: memos.store.WorkspaceSetting
	(*WorkspaceBasicSetting)(nil),            // 3: memos.store.WorkspaceBasicSetting
	(*WorkspaceGeneralSetting)(nil),          // 4: memos.store.WorkspaceGeneralSetting
	(*WorkspaceCustomProfile)(nil),           // 5: memos.store.WorkspaceCustomProfile
	(*WorkspaceStorageSetting)(nil),          // 6: memos.store.WorkspaceStorageSetting
	(*StorageS3Config)(nil),                  // 7: memos.store.StorageS3Config
	(*WorkspaceMemoRelatedSetting)(nil),      // 8: memos.store.WorkspaceMemoRelatedSetting
}
var file_store_workspace_setting_proto_depIdxs = []int32{
	0, // 0: memos.store.WorkspaceSetting.key:type_name -> memos.store.WorkspaceSettingKey
	3, // 1: memos.store.WorkspaceSetting.basic_setting:type_name -> memos.store.WorkspaceBasicSetting
	4, // 2: memos.store.WorkspaceSetting.general_setting:type_name -> memos.store.WorkspaceGeneralSetting
	6, // 3: memos.store.WorkspaceSetting.storage_setting:type_name -> memos.store.WorkspaceStorageSetting
	8, // 4: memos.store.WorkspaceSetting.memo_related_setting:type_name -> memos.store.WorkspaceMemoRelatedSetting
	5, // 5: memos.store.WorkspaceGeneralSetting.custom_profile:type_name -> memos.store.WorkspaceCustomProfile
	1, // 6: memos.store.WorkspaceStorageSetting.storage_type:type_name -> memos.store.WorkspaceStorageSetting.StorageType
	7, // 7: memos.store.WorkspaceStorageSetting.s3_config:type_name -> memos.store.StorageS3Config
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_store_workspace_setting_proto_init() }
func file_store_workspace_setting_proto_init() {
	if File_store_workspace_setting_proto != nil {
		return
	}
	file_store_workspace_setting_proto_msgTypes[0].OneofWrappers = []any{
		(*WorkspaceSetting_BasicSetting)(nil),
		(*WorkspaceSetting_GeneralSetting)(nil),
		(*WorkspaceSetting_StorageSetting)(nil),
		(*WorkspaceSetting_MemoRelatedSetting)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_workspace_setting_proto_rawDesc), len(file_store_workspace_setting_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_workspace_setting_proto_goTypes,
		DependencyIndexes: file_store_workspace_setting_proto_depIdxs,
		EnumInfos:         file_store_workspace_setting_proto_enumTypes,
		MessageInfos:      file_store_workspace_setting_proto_msgTypes,
	}.Build()
	File_store_workspace_setting_proto = out.File
	file_store_workspace_setting_proto_goTypes = nil
	file_store_workspace_setting_proto_depIdxs = nil
}
