syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gen/api/v1";

service InboxService {
  // ListInboxes lists inboxes for a user.
  rpc ListInboxes(ListInboxesRequest) returns (ListInboxesResponse) {
    option (google.api.http) = {get: "/api/v1/{parent=users/*}/inboxes"};
    option (google.api.method_signature) = "parent";
  }
  // UpdateInbox updates an inbox.
  rpc UpdateInbox(UpdateInboxRequest) returns (Inbox) {
    option (google.api.http) = {
      patch: "/api/v1/{inbox.name=inboxes/*}"
      body: "inbox"
    };
    option (google.api.method_signature) = "inbox,update_mask";
  }
  // DeleteInbox deletes an inbox.
  rpc DeleteInbox(DeleteInboxRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/api/v1/{name=inboxes/*}"};
    option (google.api.method_signature) = "name";
  }
}

message Inbox {
  option (google.api.resource) = {
    type: "memos.api.v1/Inbox"
    pattern: "inboxes/{inbox}"
    name_field: "name"
    singular: "inbox"
    plural: "inboxes"
  };

  // The resource name of the inbox.
  // Format: inboxes/{inbox}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The sender of the inbox notification.
  // Format: users/{user}
  string sender = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The receiver of the inbox notification.
  // Format: users/{user}
  string receiver = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the inbox notification.
  Status status = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The creation timestamp.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The type of the inbox notification.
  Type type = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The activity ID associated with this inbox notification.
  optional int32 activity_id = 7 [(google.api.field_behavior) = OPTIONAL];

  // Status enumeration for inbox notifications.
  enum Status {
    // Unspecified status.
    STATUS_UNSPECIFIED = 0;
    // The notification is unread.
    UNREAD = 1;
    // The notification is archived.
    ARCHIVED = 2;
  }

  // Type enumeration for inbox notifications.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;
    // Memo comment notification.
    MEMO_COMMENT = 1;
    // Version update notification.
    VERSION_UPDATE = 2;
  }
}

message ListInboxesRequest {
  // Required. The parent resource whose inboxes will be listed.
  // Format: users/{user}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/User"}
  ];

  // Optional. The maximum number of inboxes to return.
  // The service may return fewer than this value.
  // If unspecified, at most 50 inboxes will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListInboxes` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter to apply to the list results.
  // Example: "status=UNREAD" or "type=MEMO_COMMENT"
  // Supported operators: =, !=
  // Supported fields: status, type, sender, create_time
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The order to sort results by.
  // Example: "create_time desc" or "status asc"
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

message ListInboxesResponse {
  // The list of inboxes.
  repeated Inbox inboxes = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // The total count of inboxes (may be approximate).
  int32 total_size = 3;
}

message UpdateInboxRequest {
  // Required. The inbox to update.
  Inbox inbox = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set to true, allows updating missing fields.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];
}

message DeleteInboxRequest {
  // Required. The resource name of the inbox to delete.
  // Format: inboxes/{inbox}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "memos.api.v1/Inbox"}
  ];
}
