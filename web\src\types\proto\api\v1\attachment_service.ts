// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/attachment_service.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { HttpBody } from "../../google/api/httpbody";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";

export const protobufPackage = "memos.api.v1";

export interface Attachment {
  /**
   * The name of the attachment.
   * Format: attachments/{attachment}
   */
  name: string;
  /** Output only. The creation timestamp. */
  createTime?:
    | Date
    | undefined;
  /** The filename of the attachment. */
  filename: string;
  /** Input only. The content of the attachment. */
  content: Uint8Array;
  /** Optional. The external link of the attachment. */
  externalLink: string;
  /** The MIME type of the attachment. */
  type: string;
  /** Output only. The size of the attachment in bytes. */
  size: number;
  /**
   * Optional. The related memo. Refer to `Memo.name`.
   * Format: memos/{memo}
   */
  memo?: string | undefined;
}

export interface CreateAttachmentRequest {
  /** Required. The attachment to create. */
  attachment?:
    | Attachment
    | undefined;
  /**
   * Optional. The attachment ID to use for this attachment.
   * If empty, a unique ID will be generated.
   */
  attachmentId: string;
}

export interface ListAttachmentsRequest {
  /**
   * Optional. The maximum number of attachments to return.
   * The service may return fewer than this value.
   * If unspecified, at most 50 attachments will be returned.
   * The maximum value is 1000; values above 1000 will be coerced to 1000.
   */
  pageSize: number;
  /**
   * Optional. A page token, received from a previous `ListAttachments` call.
   * Provide this to retrieve the subsequent page.
   */
  pageToken: string;
  /**
   * Optional. Filter to apply to the list results.
   * Example: "type=image/png" or "filename:*.jpg"
   * Supported operators: =, !=, <, <=, >, >=, :
   * Supported fields: filename, type, size, create_time, memo
   */
  filter: string;
  /**
   * Optional. The order to sort results by.
   * Example: "create_time desc" or "filename asc"
   */
  orderBy: string;
}

export interface ListAttachmentsResponse {
  /** The list of attachments. */
  attachments: Attachment[];
  /**
   * A token that can be sent as `page_token` to retrieve the next page.
   * If this field is omitted, there are no subsequent pages.
   */
  nextPageToken: string;
  /** The total count of attachments (may be approximate). */
  totalSize: number;
}

export interface GetAttachmentRequest {
  /**
   * Required. The attachment name of the attachment to retrieve.
   * Format: attachments/{attachment}
   */
  name: string;
}

export interface GetAttachmentBinaryRequest {
  /**
   * Required. The attachment name of the attachment.
   * Format: attachments/{attachment}
   */
  name: string;
  /** The filename of the attachment. Mainly used for downloading. */
  filename: string;
  /** Optional. A flag indicating if the thumbnail version of the attachment should be returned. */
  thumbnail: boolean;
}

export interface UpdateAttachmentRequest {
  /** Required. The attachment which replaces the attachment on the server. */
  attachment?:
    | Attachment
    | undefined;
  /** Required. The list of fields to update. */
  updateMask?: string[] | undefined;
}

export interface DeleteAttachmentRequest {
  /**
   * Required. The attachment name of the attachment to delete.
   * Format: attachments/{attachment}
   */
  name: string;
}

function createBaseAttachment(): Attachment {
  return {
    name: "",
    createTime: undefined,
    filename: "",
    content: new Uint8Array(0),
    externalLink: "",
    type: "",
    size: 0,
    memo: undefined,
  };
}

export const Attachment: MessageFns<Attachment> = {
  encode(message: Attachment, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(18).fork()).join();
    }
    if (message.filename !== "") {
      writer.uint32(26).string(message.filename);
    }
    if (message.content.length !== 0) {
      writer.uint32(34).bytes(message.content);
    }
    if (message.externalLink !== "") {
      writer.uint32(42).string(message.externalLink);
    }
    if (message.type !== "") {
      writer.uint32(50).string(message.type);
    }
    if (message.size !== 0) {
      writer.uint32(56).int64(message.size);
    }
    if (message.memo !== undefined) {
      writer.uint32(66).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Attachment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAttachment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filename = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.content = reader.bytes();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.externalLink = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.size = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Attachment>): Attachment {
    return Attachment.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Attachment>): Attachment {
    const message = createBaseAttachment();
    message.name = object.name ?? "";
    message.createTime = object.createTime ?? undefined;
    message.filename = object.filename ?? "";
    message.content = object.content ?? new Uint8Array(0);
    message.externalLink = object.externalLink ?? "";
    message.type = object.type ?? "";
    message.size = object.size ?? 0;
    message.memo = object.memo ?? undefined;
    return message;
  },
};

function createBaseCreateAttachmentRequest(): CreateAttachmentRequest {
  return { attachment: undefined, attachmentId: "" };
}

export const CreateAttachmentRequest: MessageFns<CreateAttachmentRequest> = {
  encode(message: CreateAttachmentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.attachment !== undefined) {
      Attachment.encode(message.attachment, writer.uint32(10).fork()).join();
    }
    if (message.attachmentId !== "") {
      writer.uint32(18).string(message.attachmentId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateAttachmentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateAttachmentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.attachment = Attachment.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.attachmentId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateAttachmentRequest>): CreateAttachmentRequest {
    return CreateAttachmentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateAttachmentRequest>): CreateAttachmentRequest {
    const message = createBaseCreateAttachmentRequest();
    message.attachment = (object.attachment !== undefined && object.attachment !== null)
      ? Attachment.fromPartial(object.attachment)
      : undefined;
    message.attachmentId = object.attachmentId ?? "";
    return message;
  },
};

function createBaseListAttachmentsRequest(): ListAttachmentsRequest {
  return { pageSize: 0, pageToken: "", filter: "", orderBy: "" };
}

export const ListAttachmentsRequest: MessageFns<ListAttachmentsRequest> = {
  encode(message: ListAttachmentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(18).string(message.pageToken);
    }
    if (message.filter !== "") {
      writer.uint32(26).string(message.filter);
    }
    if (message.orderBy !== "") {
      writer.uint32(34).string(message.orderBy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAttachmentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAttachmentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.orderBy = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAttachmentsRequest>): ListAttachmentsRequest {
    return ListAttachmentsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListAttachmentsRequest>): ListAttachmentsRequest {
    const message = createBaseListAttachmentsRequest();
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.filter = object.filter ?? "";
    message.orderBy = object.orderBy ?? "";
    return message;
  },
};

function createBaseListAttachmentsResponse(): ListAttachmentsResponse {
  return { attachments: [], nextPageToken: "", totalSize: 0 };
}

export const ListAttachmentsResponse: MessageFns<ListAttachmentsResponse> = {
  encode(message: ListAttachmentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.attachments) {
      Attachment.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    if (message.totalSize !== 0) {
      writer.uint32(24).int32(message.totalSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAttachmentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAttachmentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.attachments.push(Attachment.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAttachmentsResponse>): ListAttachmentsResponse {
    return ListAttachmentsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListAttachmentsResponse>): ListAttachmentsResponse {
    const message = createBaseListAttachmentsResponse();
    message.attachments = object.attachments?.map((e) => Attachment.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    message.totalSize = object.totalSize ?? 0;
    return message;
  },
};

function createBaseGetAttachmentRequest(): GetAttachmentRequest {
  return { name: "" };
}

export const GetAttachmentRequest: MessageFns<GetAttachmentRequest> = {
  encode(message: GetAttachmentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAttachmentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAttachmentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetAttachmentRequest>): GetAttachmentRequest {
    return GetAttachmentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetAttachmentRequest>): GetAttachmentRequest {
    const message = createBaseGetAttachmentRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGetAttachmentBinaryRequest(): GetAttachmentBinaryRequest {
  return { name: "", filename: "", thumbnail: false };
}

export const GetAttachmentBinaryRequest: MessageFns<GetAttachmentBinaryRequest> = {
  encode(message: GetAttachmentBinaryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.filename !== "") {
      writer.uint32(18).string(message.filename);
    }
    if (message.thumbnail !== false) {
      writer.uint32(24).bool(message.thumbnail);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAttachmentBinaryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAttachmentBinaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.filename = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.thumbnail = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetAttachmentBinaryRequest>): GetAttachmentBinaryRequest {
    return GetAttachmentBinaryRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetAttachmentBinaryRequest>): GetAttachmentBinaryRequest {
    const message = createBaseGetAttachmentBinaryRequest();
    message.name = object.name ?? "";
    message.filename = object.filename ?? "";
    message.thumbnail = object.thumbnail ?? false;
    return message;
  },
};

function createBaseUpdateAttachmentRequest(): UpdateAttachmentRequest {
  return { attachment: undefined, updateMask: undefined };
}

export const UpdateAttachmentRequest: MessageFns<UpdateAttachmentRequest> = {
  encode(message: UpdateAttachmentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.attachment !== undefined) {
      Attachment.encode(message.attachment, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateAttachmentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAttachmentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.attachment = Attachment.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateAttachmentRequest>): UpdateAttachmentRequest {
    return UpdateAttachmentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateAttachmentRequest>): UpdateAttachmentRequest {
    const message = createBaseUpdateAttachmentRequest();
    message.attachment = (object.attachment !== undefined && object.attachment !== null)
      ? Attachment.fromPartial(object.attachment)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteAttachmentRequest(): DeleteAttachmentRequest {
  return { name: "" };
}

export const DeleteAttachmentRequest: MessageFns<DeleteAttachmentRequest> = {
  encode(message: DeleteAttachmentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteAttachmentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteAttachmentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteAttachmentRequest>): DeleteAttachmentRequest {
    return DeleteAttachmentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteAttachmentRequest>): DeleteAttachmentRequest {
    const message = createBaseDeleteAttachmentRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type AttachmentServiceDefinition = typeof AttachmentServiceDefinition;
export const AttachmentServiceDefinition = {
  name: "AttachmentService",
  fullName: "memos.api.v1.AttachmentService",
  methods: {
    /** CreateAttachment creates a new attachment. */
    createAttachment: {
      name: "CreateAttachment",
      requestType: CreateAttachmentRequest,
      requestStream: false,
      responseType: Attachment,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([10, 97, 116, 116, 97, 99, 104, 109, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              33,
              58,
              10,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              34,
              19,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListAttachments lists all attachments. */
    listAttachments: {
      name: "ListAttachments",
      requestType: ListAttachmentsRequest,
      requestStream: false,
      responseType: ListAttachmentsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              21,
              18,
              19,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetAttachment returns a attachment by name. */
    getAttachment: {
      name: "GetAttachment",
      requestType: GetAttachmentRequest,
      requestStream: false,
      responseType: Attachment,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              30,
              18,
              28,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** GetAttachmentBinary returns a attachment binary by name. */
    getAttachmentBinary: {
      name: "GetAttachmentBinary",
      requestType: GetAttachmentBinaryRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([13, 110, 97, 109, 101, 44, 102, 105, 108, 101, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              39,
              18,
              37,
              47,
              102,
              105,
              108,
              101,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
              47,
              42,
              125,
              47,
              123,
              102,
              105,
              108,
              101,
              110,
              97,
              109,
              101,
              125,
            ]),
          ],
        },
      },
    },
    /** UpdateAttachment updates a attachment. */
    updateAttachment: {
      name: "UpdateAttachment",
      requestType: UpdateAttachmentRequest,
      requestStream: false,
      responseType: Attachment,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              22,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              53,
              58,
              10,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              50,
              39,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              46,
              110,
              97,
              109,
              101,
              61,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteAttachment deletes a attachment by name. */
    deleteAttachment: {
      name: "DeleteAttachment",
      requestType: DeleteAttachmentRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              30,
              42,
              28,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              97,
              116,
              116,
              97,
              99,
              104,
              109,
              101,
              110,
              116,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
